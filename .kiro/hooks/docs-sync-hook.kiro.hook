{"enabled": true, "name": "Documentation Sync", "description": "Monitors TypeScript source files, configuration files, and other relevant project files for changes and automatically updates documentation in README.md and the Docs folder to keep them synchronized with code changes", "version": "1", "when": {"type": "fileEdited", "patterns": ["src/**/*.ts", "src/**/*.tsx", "pages/**/*.ts", "pages/**/*.tsx", "components/**/*.ts", "components/**/*.tsx", "lib/**/*.ts", "types/**/*.ts", "hooks/**/*.ts", "utils/**/*.ts", "package.json", "tsconfig.json", "next.config.js", "tailwind.config.js"]}, "then": {"type": "askAgent", "prompt": "Source code files have been modified in this Next.js TypeScript project. Please review the changes and update the relevant documentation in either the README.md file or the appropriate files in the Docs/ folder. Focus on updating technical architecture documentation, API references, component documentation, and any implementation details that may have changed. Ensure the documentation accurately reflects the current state of the codebase, including new features, modified interfaces, updated dependencies, or architectural changes."}}