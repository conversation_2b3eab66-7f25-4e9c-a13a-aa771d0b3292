# Project Structure & Organization

## Root Directory Layout

```
/
├── pages/           # Next.js file-based routing
├── components/      # Reusable React components
├── lib/            # Utility functions and configurations
├── styles/         # Global CSS and Tailwind config
├── public/         # Static assets (images, icons, etc.)
├── types/          # TypeScript type definitions
├── hooks/          # Custom React hooks
├── utils/          # Helper functions
└── Docs/           # Project documentation
```

## Documentation Structure

- `Docs/Project/` - PRD, README, implementation plans
- `Docs/Architecture/` - Technical architecture, database schema
- `Docs/Development/` - Coding standards, workflows, pre-commit
- `Docs/Infrastructure/` - Supabase setup, storage configuration
- `Docs/Testing/` - Testing strategies and guidelines

## Database Schema

- `vehicles` table: id, name, year, photo_url, description, price, availability
- `bookings` table: id, vehicle_id, customer_name, email, dates, status

## Key Pages Structure

- `/` - Home page with service overview
- `/servicos` - Services and use cases
- `/frota` - Fleet showcase with vehicle gallery
- `/reserva` - Booking form with calendar
- `/contacto` - Contact form and business info
- `/termos` - Terms and rental policies
- `/admin` - Protected admin panel

## Component Organization

- Keep components small and focused
- Use descriptive English names for components
- Portuguese only for UI labels and content
- Organize by feature when components grow

## File Naming Conventions

- Use kebab-case for files and folders
- TypeScript files use `.ts` or `.tsx` extensions
- Component files match component name
- Use descriptive names that indicate purpose
