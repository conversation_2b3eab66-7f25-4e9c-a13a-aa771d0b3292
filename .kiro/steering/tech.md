# Tech Stack & Build System

## Core Technologies

- **Frontend**: Next.js with TypeScript and Tailwind CSS
- **Backend**: Supabase (PostgreSQL, Auth, Storage, APIs)
- **Hosting**: Vercel with auto-deployment
- **Analytics**: Google Analytics 4, Search Console

## Development Tools

- TypeScript for all code (non-strict mode for rapid development)
- ESLint + Prettier for code quality (warnings only, non-blocking)
- Jest + React Testing Library for testing (basic test coverage)
- Pre-commit hooks optional for faster iteration

## Common Commands

```bash
# Development
npm install          # Install dependencies
npm run dev         # Start development server
npm run build       # Build for production
npm run start       # Start production server

# Quality Checks
npm run lint        # Run ESLint
npm run type-check  # TypeScript validation
npm run test        # Run Jest tests
npm run format      # Prettier formatting
```

## Development Workflow Rules

### Server Management

- **Always check for running dev servers**: Before starting a new development server with `npm run dev`, check if one is already running on the same port to avoid conflicts
- Use `lsof -i :3000` (or relevant port) to check for existing processes
- Kill existing processes if needed before starting new ones

### Version Control

- **Commit after every successful task completion**: After completing any implementation task successfully, commit changes with a proper, descriptive commit message
- Follow conventional commit format: `feat:`, `fix:`, `docs:`, `style:`, `refactor:`, `test:`, `chore:`
- Push commits to remote repository to maintain backup and collaboration
- Example: `git add . && git commit -m "feat: implement booking form with date validation" && git push`

## Architecture Patterns

- Use functional React components with hooks
- Prefer SSG/ISR over SSR for performance
- File-based routing in `/pages` directory
- Components organized in `/components`
- Utility-first CSS with Tailwind classes

## Supabase Integration

- Use typed API calls with generated types
- Store only public URLs for Supabase Storage files
- Implement Row Level Security (RLS) policies
- Never expose service keys to frontend

## Required MCP Tools

**MANDATORY**: For every implementation task requiring libraries or frameworks:

- **Context7 MCP**: MUST be used for fact-checking, best practices, and up-to-date library documentation
- **Supabase MCP**: MUST be used for all Supabase-related tasks (database operations, auth, storage, etc.)

## Key Libraries

- Context7 references: `/supabase/supabase`, `/vercel/next.js`, `/microsoft/typescript`
- Always verify library versions and best practices via Context7 MCP before implementation
