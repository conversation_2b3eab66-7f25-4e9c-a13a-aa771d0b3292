# Design Document

## Overview

The Client Demo Website is designed as a professional, client-ready showcase for Vintage Marketing Portugal. The architecture prioritizes visual impact, core functionality, and rapid development to demonstrate the business concept effectively. The system uses a modern JAMstack approach with Next.js for the frontend and Supabase for backend services.

## Architecture

### High-Level Architecture

```mermaid
graph TB
    A[User Browser] --> B[Next.js Frontend]
    B --> C[Supabase API]
    C --> D[PostgreSQL Database]
    C --> E[Supabase Storage]
    C --> F[Supabase Auth]
    B --> G[Email Service]
    H[Admin Panel] --> B
    I[Vercel CDN] --> A
```

### Technology Stack

- **Frontend**: Next.js 15 with TypeScript and Tailwind CSS
- **UI Components**: Headless UI for accessible, unstyled components
- **Animations**: Comprehensive Framer Motion ecosystem with advanced performance optimization
- **Backend**: Supabase (PostgreSQL, Auth, Storage, Edge Functions)
- **Email**: Resend (simpler than SendGrid for this use case)
- **Hosting**: Vercel with automatic deployments
- **Styling**: Tailwind CSS with custom vintage theme

### Animation System Architecture

The website features a sophisticated, production-ready animation system built on Framer Motion that provides:

#### Core Animation Components

- **Basic Animations**: FadeIn, SlideIn, ScaleIn, StaggerContainer with performance optimization
- **Scroll-Based Animations**: ScrollReveal, TextReveal, CardStack, StaggeredCard, IndependentSection
- **Page Transitions**: Event-driven transitions with Next.js App Router integration
- **Micro-Interactions**: PulseIcon, MagneticButton, RippleButton, GlowEffect, CounterAnimation
- **Advanced Effects**: Card-stacking/folding scroll effects, parallax backgrounds, hover interactions

#### Performance & Accessibility Features

- **Device Adaptation**: Automatic animation optimization based on hardware capabilities
- **Network Awareness**: Reduced animations on slow connections (2G detection)
- **Accessibility Compliance**: Comprehensive `prefers-reduced-motion` support
- **Responsive Behavior**: Screen-size aware animation complexity scaling
- **Hardware Acceleration**: GPU-optimized transforms and opacity animations only

#### Integration Patterns

- **Next.js App Router**: Seamless page transitions with Suspense integration
- **Event-Driven State Management**: Uses Framer Motion callbacks instead of manual timing
- **Variants with StaggerChildren**: Proper orchestration following Framer Motion best practices
- **Context-Based Coordination**: Global animation state management via PageTransitionContext

## Components and Interfaces

### Frontend Components

#### Core Layout Components

- **Header**: Navigation with logo, menu items (Home, Frota, Reservas, Contacto)
- **Footer**: Contact info, social links, copyright
- **Layout**: Consistent wrapper for all pages

#### Page Components

- **HomePage**: Hero section, service overview, featured vehicles, CTA buttons
- **FleetPage**: Simple vehicle grid, vehicle detail modals using Headless UI Dialog
- **BookingPage**: Multi-step form with Headless UI components for vehicle selection, date picker, customer details
- **ContactPage**: Contact form using Headless UI form components, business info, embedded Google Maps
- **AdminDashboard**: Booking management, vehicle management, email templates, SEO management with Headless UI Tabs

#### Shared Components

- **VehicleCard**: Reusable vehicle display component
- **DatePicker**: Custom calendar with availability checking
- **BookingForm**: Multi-step booking process using Headless UI form components
- **EmailTemplateEditor**: Rich text editor for admin email customization
- **SEOManager**: Interface for managing page-specific SEO settings
- **BusinessInfoEditor**: Form for updating business information and structured data
- **SEOPreview**: Component showing how pages will appear in search results and social media

### API Interfaces

#### Supabase Database Schema

```sql
-- Vehicles table (existing)
CREATE TABLE vehicles (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  name text NOT NULL,
  year integer NOT NULL,
  photo_url text,
  description text,
  price numeric,
  availability jsonb DEFAULT '{}',
  created_at timestamp DEFAULT now()
);

-- Bookings table (enhanced)
CREATE TABLE bookings (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  vehicle_id uuid REFERENCES vehicles(id),
  customer_name text NOT NULL,
  email text NOT NULL,
  phone text,
  start_date date NOT NULL,
  end_date date NOT NULL,
  purpose text,
  status text DEFAULT 'pendente',
  admin_notes text,
  created_at timestamp DEFAULT now()
);

-- Contact inquiries table (new)
CREATE TABLE contact_inquiries (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  name text NOT NULL,
  email text NOT NULL,
  phone text,
  message text NOT NULL,
  created_at timestamp DEFAULT now()
);

-- Email templates table (new)
CREATE TABLE email_templates (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  name text NOT NULL,
  subject text NOT NULL,
  body text NOT NULL,
  variables jsonb DEFAULT '[]',
  created_at timestamp DEFAULT now()
);

-- SEO settings table (new)
CREATE TABLE seo_settings (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  page_slug text UNIQUE NOT NULL,
  page_title text,
  meta_description text,
  focus_keyphrase text,
  og_title text,
  og_description text,
  og_image_url text,
  twitter_title text,
  twitter_description text,
  twitter_image_url text,
  canonical_url text,
  robots_meta text DEFAULT 'index,follow',
  created_at timestamp DEFAULT now(),
  updated_at timestamp DEFAULT now()
);

-- Business info table for structured data (new)
CREATE TABLE business_info (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  business_name text NOT NULL,
  business_type text DEFAULT 'LocalBusiness',
  address_street text,
  address_city text,
  address_postal_code text,
  address_country text DEFAULT 'Portugal',
  phone text,
  email text,
  website_url text,
  opening_hours jsonb DEFAULT '{}',
  geo_latitude numeric,
  geo_longitude numeric,
  created_at timestamp DEFAULT now(),
  updated_at timestamp DEFAULT now()
);
```

#### API Endpoints (Supabase Auto-generated)

- `GET /vehicles` - Fetch all vehicles
- `GET /vehicles/:id` - Fetch single vehicle
- `POST /bookings` - Create new booking
- `GET /bookings` - Fetch bookings (admin only)
- `PATCH /bookings/:id` - Update booking status (admin only)
- `POST /contact_inquiries` - Submit contact form
- `GET /email_templates` - Fetch email templates (admin only)
- `POST /send-email` - Send confirmation email (Edge Function)
- `GET /seo_settings` - Fetch SEO settings (admin only)
- `GET /seo_settings/:page_slug` - Fetch SEO settings for specific page
- `POST /seo_settings` - Create SEO settings (admin only)
- `PATCH /seo_settings/:id` - Update SEO settings (admin only)
- `GET /business_info` - Fetch business information
- `PATCH /business_info/:id` - Update business information (admin only)

## Data Models

### Vehicle Model

```typescript
interface Vehicle {
  id: string;
  name: string;
  year: number;
  photo_url?: string;
  description?: string;
  price?: number;
  availability: Record<string, boolean>; // {"2025-07-20": true}
  created_at: string;
}
```

### Booking Model

```typescript
interface Booking {
  id: string;
  vehicle_id: string;
  customer_name: string;
  email: string;
  phone?: string;
  start_date: string;
  end_date: string;
  purpose?: string;
  status: 'pendente' | 'confirmado' | 'cancelado';
  admin_notes?: string;
  created_at: string;
  vehicle?: Vehicle; // Populated via join
}
```

### Email Template Model

```typescript
interface EmailTemplate {
  id: string;
  name: string;
  subject: string;
  body: string;
  variables: string[]; // ["customer_name", "vehicle_name", "dates"]
  created_at: string;
}
```

### SEO Settings Model

```typescript
interface SEOSettings {
  id: string;
  page_slug: string;
  page_title?: string;
  meta_description?: string;
  focus_keyphrase?: string;
  og_title?: string;
  og_description?: string;
  og_image_url?: string;
  twitter_title?: string;
  twitter_description?: string;
  twitter_image_url?: string;
  canonical_url?: string;
  robots_meta: string;
  created_at: string;
  updated_at: string;
}
```

### Business Info Model

```typescript
interface BusinessInfo {
  id: string;
  business_name: string;
  business_type: string;
  address_street?: string;
  address_city?: string;
  address_postal_code?: string;
  address_country: string;
  phone?: string;
  email?: string;
  website_url?: string;
  opening_hours: Record<string, string>; // {"monday": "9:00-18:00"}
  geo_latitude?: number;
  geo_longitude?: number;
  created_at: string;
  updated_at: string;
}
```

## User Interface Design

### Design System

- **Color Palette**: Vintage-inspired earth tones (browns, creams, golds)
- **Typography**: Modern serif for headings, clean sans-serif for body
- **Imagery**: High-quality vintage vehicle photos with consistent styling and color grading
- **Layout**: Clean, spacious design with clear visual hierarchy

### Page Layouts

#### Homepage

- Hero section with background video/image of vintage vehicles
- Service overview with 3-column layout
- Featured vehicles carousel
- Call-to-action sections leading to booking

#### Fleet Page

- Simple grid layout with vehicle cards (2 vehicles: Fleur de Lys Van, BMW R100 RS)
- Modal overlays for detailed vehicle information
- Clear pricing and booking CTAs

#### Booking Page

- Multi-step form with progress indicator
- Step 1: Vehicle selection with availability calendar
- Step 2: Customer details form
- Step 3: Booking confirmation and summary

#### Admin Panel

- Dashboard with booking statistics
- Tabbed interface for bookings, vehicles, email templates, and SEO management
- Inline editing for quick updates
- Email template editor with preview functionality
- SEO management interface with page-specific settings
- Business information editor for structured data
- SEO preview showing search results and social media appearance

## Error Handling

### Frontend Error Handling

- Form validation with Portuguese error messages
- Network error handling with retry mechanisms
- Loading states for all async operations
- Graceful fallbacks for missing images or data

### Backend Error Handling

- Database constraint violations
- Authentication errors
- File upload failures
- Email sending failures

### Error Messages (Portuguese)

```typescript
const errorMessages = {
  required: 'Este campo é obrigatório',
  email: 'Por favor, insira um email válido',
  dateConflict: 'As datas selecionadas não estão disponíveis',
  networkError: 'Erro de conexão. Tente novamente.',
  uploadError: 'Erro ao carregar imagem. Tente novamente.',
};
```

## Email System Design

### Email Service Integration

- Use Resend for email delivery (simpler setup than SendGrid)
- Supabase Edge Function to handle email sending
- Template system with variable substitution

### Email Templates

- **Booking Confirmation**: Sent when admin confirms booking
- **Booking Cancellation**: Sent when booking is cancelled
- **Contact Form Response**: Auto-response for contact inquiries

### Template Variables

- `{{customer_name}}` - Customer's name
- `{{vehicle_name}}` - Selected vehicle name
- `{{start_date}}` - Booking start date
- `{{end_date}}` - Booking end date
- `{{total_price}}` - Total booking price
- `{{admin_notes}}` - Custom message from admin

## Performance Considerations

### Frontend Optimization

- Next.js Static Site Generation (SSG) for static pages
- Incremental Static Regeneration (ISR) for vehicle data
- Image optimization with Next.js Image component
- Lazy loading for non-critical components

### Backend Optimization

- Supabase connection pooling
- Efficient database queries with proper indexing
- Image compression and WebP format support
- CDN delivery via Vercel Edge Network

## Security Considerations

### Authentication & Authorization

- Supabase Auth for admin panel access
- Row Level Security (RLS) policies for data protection
- API key management through environment variables

### Data Protection

- Input validation and sanitization
- SQL injection prevention via Supabase client
- XSS protection through React's built-in escaping
- HTTPS enforcement via Vercel

## SEO Strategy

### Technical SEO

- Next.js built-in SEO optimization
- Portuguese meta tags and descriptions
- Structured data (JSON-LD) for local business
- XML sitemap generation
- Robots.txt configuration

### Content Strategy

- Portuguese keywords: "aluguer veículos vintage", "carros vintage eventos"
- Local SEO targeting Portuguese cities
- Alt text for all vehicle images
- Semantic HTML structure

## Testing Strategy

### Simplified Testing Approach

Given the client demo focus, we'll implement basic testing:

- **Manual Testing**: Core user flows (booking, admin approval)
- **Basic Unit Tests**: Critical business logic only
- **Browser Testing**: Chrome, Safari, mobile browsers
- **Performance Testing**: Google PageSpeed Insights

### Test Scenarios

1. User can view vehicles and submit booking
2. Admin can approve booking and send email
3. Forms validate correctly in Portuguese
4. Site works on mobile devices
5. Images load properly from Supabase Storage
