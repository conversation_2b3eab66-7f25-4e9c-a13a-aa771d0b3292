/** @type {import('tailwindcss').Config} */
module.exports = {
  content: [
    './src/pages/**/*.{js,ts,jsx,tsx,mdx}',
    './src/components/**/*.{js,ts,jsx,tsx,mdx}',
    './src/app/**/*.{js,ts,jsx,tsx,mdx}',
  ],
  theme: {
    extend: {
      colors: {
        // Design System Primary Colors
        primary: {
          black: '#000000',
          darkGray: '#1A1A1A',
          mediumGray: '#666666',
          lightGray: '#999999',
          white: '#FFFFFF',
        },
        // Design System Background Colors
        background: {
          cream: '#F5F1E8',
          beige: '#E8E0D0',
          darkSection: '#000000',
          cardBackground: '#f7f5f0ff',
        },
        // Design System Accent Colors
        accent: {
          vintage: '#8B4513',
          warmBrown: '#A0522D',
        },
        // Legacy colors for backward compatibility (will be phased out)
        vintage: {
          cream: '#F5F1E8',
          brown: '#8B4513',
          gold: '#DAA520',
          darkBrown: '#654321',
          lightBrown: '#D2B48C',
        },
      },
      fontFamily: {
        serif: ['Georgia', 'serif'],
        sans: ['Inter', 'sans-serif'],
        heading: ['var(--font-open-sans)', 'Inter', 'sans-serif'],
        logo: ['var(--font-limelight)', 'Georgia', 'serif'],
      },
      fontSize: {
        // Design System Typography Scale
        hero: 'clamp(2.5rem, 5vw, 4rem)',
        section: 'clamp(1.8rem, 3vw, 2.5rem)',
        card: '1.25rem',
        nav: '0.95rem',
      },
      fontWeight: {
        hero: '900',
        section: '800',
        card: '700',
        nav: '500',
      },
      letterSpacing: {
        hero: '-0.02em',
        section: '-0.01em',
      },
      lineHeight: {
        hero: '1.1',
        body: '1.6',
      },
      borderRadius: {
        card: '12px',
        button: '24px',
        cta: '50px',
        image: '8px',
      },
      boxShadow: {
        card: '0 8px 24px rgba(0, 0, 0, 0.12)',
        'card-hover': '0 12px 32px rgba(0, 0, 0, 0.18)',
        gallery: '0 4px 16px rgba(0, 0, 0, 0.1)',
        'gallery-hover': '0 8px 24px rgba(0, 0, 0, 0.15)',
      },
      backdropBlur: {
        nav: '10px',
      },
      animation: {
        'card-hover': 'cardHover 0.3s ease',
        'button-hover': 'buttonHover 0.3s ease',
        'cta-hover': 'ctaHover 0.3s ease',
      },
      keyframes: {
        cardHover: {
          '0%': { transform: 'translateY(0px)' },
          '100%': { transform: 'translateY(-4px)' },
        },
        buttonHover: {
          '0%': { transform: 'translateY(0px)' },
          '100%': { transform: 'translateY(-1px)' },
        },
        ctaHover: {
          '0%': { transform: 'translateX(0px)' },
          '100%': { transform: 'translateX(4px)' },
        },
      },
    },
  },
  plugins: [],
};
