# Deployment Guide: Vintage Marketing Portugal

## Overview

This guide covers the deployment process for the Vintage Marketing Portugal website, including Supabase backend setup, Vercel frontend deployment, and environment configuration.

## Prerequisites

- Node.js 18+ installed locally
- Git repository access
- Supabase account
- Vercel account
- Domain name (optional)

## Supabase Backend Deployment

### 1. Create Supabase Project

1. **Sign up/Login** to [Supabase](https://supabase.com)
2. **Create New Project**:
   - Project name: `vintage-marketing-portugal`
   - Database password: Generate secure password
   - Region: Choose closest to Portugal (Europe West)

3. **Wait for Setup**: Project initialization takes 2-3 minutes

### 2. Database Schema Setup

1. **Access SQL Editor** in Supabase dashboard
2. **Run Migration Scripts** in order:

```sql
-- 1. Create vehicles table
CREATE TABLE vehicles (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  name text NOT NULL,
  year integer NOT NULL,
  photo_url text,
  description text,
  price numeric,
  availability jsonb DEFAULT '{}',
  created_at timestamp with time zone DEFAULT now(),
  updated_at timestamp with time zone DEFAULT now()
);

-- 2. Create bookings table
CREATE TABLE bookings (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  vehicle_id uuid REFERENCES vehicles(id) ON DELETE CASCADE,
  customer_name text NOT NULL,
  customer_email text NOT NULL,
  customer_phone text,
  start_date date NOT NULL,
  end_date date NOT NULL,
  status text NOT NULL DEFAULT 'pendente' CHECK (status IN ('pendente', 'confirmado', 'cancelado', 'completo')),
  notes text,
  total_price numeric,
  created_at timestamp with time zone DEFAULT now(),
  updated_at timestamp with time zone DEFAULT now()
);

-- 3. Create contact_inquiries table
CREATE TABLE contact_inquiries (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  name text NOT NULL,
  email text NOT NULL,
  phone text,
  company text,
  subject text,
  message text NOT NULL,
  status text NOT NULL DEFAULT 'new' CHECK (status IN ('new', 'in_progress', 'resolved', 'closed')),
  created_at timestamp with time zone DEFAULT now(),
  updated_at timestamp with time zone DEFAULT now()
);

-- 4. Create email_templates table
CREATE TABLE email_templates (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  name text NOT NULL UNIQUE,
  subject text NOT NULL,
  body text NOT NULL,
  template_type text NOT NULL CHECK (template_type IN ('booking_confirmation', 'booking_cancellation', 'contact_response', 'admin_notification')),
  is_active boolean DEFAULT true,
  created_at timestamp with time zone DEFAULT now(),
  updated_at timestamp with time zone DEFAULT now()
);
```

3. **Create Database Functions**:

```sql
-- Function to automatically update updated_at column
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = now();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Add triggers to all tables
CREATE TRIGGER update_vehicles_updated_at BEFORE UPDATE ON vehicles
FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_bookings_updated_at BEFORE UPDATE ON bookings
FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_contact_inquiries_updated_at BEFORE UPDATE ON contact_inquiries
FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_email_templates_updated_at BEFORE UPDATE ON email_templates
FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Admin role checking function
CREATE OR REPLACE FUNCTION is_admin()
RETURNS boolean AS $$
DECLARE
  jwt_claims jsonb;
BEGIN
  jwt_claims := auth.jwt();

  IF jwt_claims IS NULL THEN
    RETURN false;
  END IF;

  RETURN (
    jwt_claims ->> 'role' = 'admin' OR
    (jwt_claims -> 'user_metadata' ->> 'role') = 'admin'
  );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;
```

### 3. Row Level Security Setup

1. **Enable RLS** on all tables:

```sql
-- Enable RLS
ALTER TABLE vehicles ENABLE ROW LEVEL SECURITY;
ALTER TABLE bookings ENABLE ROW LEVEL SECURITY;
ALTER TABLE contact_inquiries ENABLE ROW LEVEL SECURITY;
ALTER TABLE email_templates ENABLE ROW LEVEL SECURITY;
```

2. **Create RLS Policies**:

```sql
-- Vehicles policies
CREATE POLICY "Public can view vehicles" ON vehicles
  FOR SELECT USING (true);

CREATE POLICY "Only admins can insert vehicles" ON vehicles
  FOR INSERT WITH CHECK (is_admin());

CREATE POLICY "Only admins can update vehicles" ON vehicles
  FOR UPDATE USING (is_admin());

CREATE POLICY "Only admins can delete vehicles" ON vehicles
  FOR DELETE USING (is_admin());

-- Bookings policies
CREATE POLICY "Public can create bookings" ON bookings
  FOR INSERT WITH CHECK (true);

CREATE POLICY "Admins can view all bookings" ON bookings
  FOR SELECT USING (is_admin());

CREATE POLICY "Only admins can update bookings" ON bookings
  FOR UPDATE USING (is_admin());

CREATE POLICY "Only admins can delete bookings" ON bookings
  FOR DELETE USING (is_admin());

-- Contact inquiries policies
CREATE POLICY "Public can create contact inquiries" ON contact_inquiries
  FOR INSERT WITH CHECK (true);

CREATE POLICY "Only admins can view contact inquiries" ON contact_inquiries
  FOR SELECT USING (is_admin());

CREATE POLICY "Only admins can update contact inquiries" ON contact_inquiries
  FOR UPDATE USING (is_admin());

CREATE POLICY "Only admins can delete contact inquiries" ON contact_inquiries
  FOR DELETE USING (is_admin());

-- Email templates policies
CREATE POLICY "Only admins can access email templates" ON email_templates
  FOR ALL USING (is_admin());
```

### 4. Storage Setup

1. **Create Storage Bucket**:
   - Go to Storage in Supabase dashboard
   - Create bucket named `vehicle-images`
   - Set as public bucket
   - Configure upload policies for admin users

2. **Storage Policies**:

```sql
-- Allow public access to view images
CREATE POLICY "Public can view vehicle images" ON storage.objects
  FOR SELECT USING (bucket_id = 'vehicle-images');

-- Allow admin users to upload images
CREATE POLICY "Admins can upload vehicle images" ON storage.objects
  FOR INSERT WITH CHECK (bucket_id = 'vehicle-images' AND is_admin());
```

### 5. Sample Data (Optional)

```sql
-- Insert sample vehicles
INSERT INTO vehicles (name, year, description, price, photo_url) VALUES
('Carrinha Fleur de Lys', 1978, 'Carrinha vintage francesa perfeita para eventos de marketing e sessões fotográficas. Com o seu design único e charme retro, é ideal para campanhas publicitárias e eventos corporativos.', 120.00, 'https://images.unsplash.com/photo-1544829099-b9a0c5303bea?w=800'),
('BMW R100 RS Clássica', 1982, 'Motocicleta BMW clássica dos anos 80, perfeita para sessões fotográficas e eventos temáticos. Um ícone do design alemão que adiciona sofisticação a qualquer produção.', 95.00, 'https://images.unsplash.com/photo-1558618666-fcd25c85cd64?w=800');
```

## Vercel Frontend Deployment

### 1. Connect Repository

1. **Login to Vercel**: Visit [vercel.com](https://vercel.com)
2. **Import Project**: Click "New Project" and import from Git
3. **Select Repository**: Choose your vintage-marketing-portugal repository
4. **Configure Project**:
   - Framework Preset: Next.js
   - Root Directory: `./` (default)
   - Build Command: `npm run build`
   - Output Directory: `.next` (default)

### 2. Environment Variables

1. **Add Environment Variables** in Vercel dashboard:

```bash
# Supabase Configuration
NEXT_PUBLIC_SUPABASE_URL=https://your-project.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=your-anon-key

# Supabase Service Role (for admin operations)
SUPABASE_SERVICE_ROLE_KEY=your-service-role-key

# Resend Email (optional)
RESEND_API_KEY=your-resend-api-key

# Google Analytics (optional)
NEXT_PUBLIC_GA_ID=your-ga-id
```

2. **Get Supabase Keys**:
   - Go to Supabase Project Settings > API
   - Copy Project URL and anon public key
   - Copy service_role key (keep secure)

### 3. Deploy

1. **Initial Deployment**: Vercel will automatically deploy after configuration
2. **Monitor Build**: Check build logs for any errors
3. **Test Deployment**: Visit the provided URL to test functionality

### 4. Custom Domain (Optional)

1. **Add Domain** in Vercel dashboard:
   - Go to Project Settings > Domains
   - Add your custom domain
   - Configure DNS records as instructed

2. **SSL Certificate**: Automatically provisioned by Vercel

## Environment Configuration

### Development Environment

```bash
# .env.local (for local development)
NEXT_PUBLIC_SUPABASE_URL=https://your-project.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=your-anon-key
SUPABASE_SERVICE_ROLE_KEY=your-service-role-key
RESEND_API_KEY=your-resend-api-key
```

### Production Environment

- All environment variables configured in Vercel dashboard
- Automatic HTTPS via Vercel
- CDN distribution globally
- Automatic deployments on git push

## Post-Deployment Checklist

### 1. Functionality Testing

- [ ] Homepage loads correctly
- [ ] All navigation links work
- [ ] Vehicle showcase displays data from Supabase
- [ ] Booking form submits successfully
- [ ] Contact form submits to database
- [ ] Mobile responsiveness works
- [ ] Portuguese content displays correctly

### 2. Database Testing

- [ ] Vehicles table populated with sample data
- [ ] Booking submissions create database records
- [ ] Contact form submissions create database records
- [ ] RLS policies prevent unauthorized access
- [ ] Admin functions work correctly

### 3. Performance Testing

- [ ] Page load times < 2 seconds
- [ ] Images load and optimize correctly
- [ ] Mobile performance acceptable
- [ ] Lighthouse scores > 90

### 4. SEO Configuration

- [ ] Meta tags display correctly
- [ ] Portuguese content indexed
- [ ] Sitemap accessible
- [ ] Google Analytics tracking (if configured)

## Monitoring and Maintenance

### 1. Supabase Monitoring

- **Database Usage**: Monitor in Supabase dashboard
- **API Requests**: Track usage and performance
- **Storage Usage**: Monitor image storage consumption
- **Auth Usage**: Track admin authentication

### 2. Vercel Monitoring

- **Deployment Status**: Monitor build and deployment success
- **Performance Metrics**: Track Core Web Vitals
- **Error Tracking**: Monitor runtime errors
- **Analytics**: Track user behavior (if enabled)

### 3. Regular Maintenance

- **Database Backups**: Automatic via Supabase
- **Security Updates**: Keep dependencies updated
- **Performance Monitoring**: Regular Lighthouse audits
- **Content Updates**: Keep vehicle and service information current

## Troubleshooting

### Common Issues

1. **Build Failures**:
   - Check TypeScript errors: `npm run type-check`
   - Verify environment variables are set
   - Check for missing dependencies

2. **Database Connection Issues**:
   - Verify Supabase URL and keys
   - Check RLS policies
   - Confirm database schema is correct

3. **Image Loading Issues**:
   - Verify Supabase Storage setup
   - Check image URLs and permissions
   - Confirm Next.js Image configuration

4. **Form Submission Issues**:
   - Check RLS policies for INSERT permissions
   - Verify form validation logic
   - Check network requests in browser dev tools

### Support Resources

- **Supabase Documentation**: [docs.supabase.com](https://docs.supabase.com)
- **Vercel Documentation**: [vercel.com/docs](https://vercel.com/docs)
- **Next.js Documentation**: [nextjs.org/docs](https://nextjs.org/docs)
- **Project Documentation**: See `/Docs` folder in repository

## Security Considerations

### 1. Environment Variables

- Never commit `.env.local` to version control
- Use different keys for development and production
- Regularly rotate API keys
- Keep service role keys secure

### 2. Database Security

- RLS policies properly configured
- Admin access properly restricted
- Regular security audits
- Monitor for unusual activity

### 3. Frontend Security

- No sensitive data in client-side code
- Proper form validation and sanitization
- HTTPS enforced in production
- Regular dependency updates

## Backup and Recovery

### 1. Database Backups

- **Automatic Backups**: Enabled by default in Supabase
- **Manual Backups**: Can be triggered in dashboard
- **Point-in-Time Recovery**: Available for paid plans

### 2. Code Backups

- **Git Repository**: Primary backup via version control
- **Vercel Integration**: Automatic deployment from git
- **Local Development**: Keep local copies updated

### 3. Recovery Procedures

- Database restoration via Supabase dashboard
- Code rollback via git and Vercel
- Environment variable restoration from documentation
