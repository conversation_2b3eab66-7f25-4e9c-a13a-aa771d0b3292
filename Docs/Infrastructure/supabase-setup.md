# Supabase Setup: Vintage Marketing Portugal

> **Note:** Supabase project setup, table creation, auth, and storage configuration are managed by the AI Assistant via direct Supabase connection. Human team members do not need to perform these steps manually.

## 1. Create Project

- Go to [supabase.com](https://supabase.com) and create a new project.
- Set region to Europe (for latency).

## 2. Create Tables

- Use SQL editor to create `vehicles` and `bookings` tables (see database-schema.md).

## 3. Configure Auth

- Enable email/password and magic link authentication.
- Set up admin users for backend access.

## 4. API Keys

- Copy anon/public key for frontend use.
- Never expose service role key in frontend.

## 5. Storage

- Set up Supabase Storage buckets for all images and files (e.g., 'vehicles', 'docs').
- Restrict uploads to admin users.
- Store public URLs in the database.

## 6. Security

- Configure RLS (Row Level Security) for bookings and vehicles.
- Allow public read for fleet, restrict write to admin.

## 7. Test

- Insert sample data and test API from Next.js app.

## 8. References

- See [Context7: /supabase/supabase] for Supabase Storage integration and best practices.
