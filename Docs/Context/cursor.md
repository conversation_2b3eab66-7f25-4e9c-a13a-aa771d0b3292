# Cursor Context Notes: Vintage Marketing Portugal

> **AI AGENT RULES:** All AI agents and automation for this project MUST follow the rules and workflow defined in `Docs/Project/cursor-ai-rules.md` for every implementation, workflow, and decision. That file is the authoritative source for AI coding standards, workflow, and responsibilities.

## Business Overview

- Vintage vehicle rental for marketing/advertising in Portugal.
- Two vehicles: Fleur de Lys Van, BMW R100 RS.
- Target: Portuguese businesses for impactful campaigns/events.
- Key goals: Professional, vintage aesthetic, mobile-responsive, easy content updates, automated booking, cost-effective, top SEO for Portugal.

## Core Features

- Fleet showcase (photos, details, prices)
- Booking system (no payment gateway)
- Contact info, terms & conditions
- Admin interface (manage bookings, vehicles)
- Portuguese language, local SEO focus

## Tech Stack

- Frontend: Next.js (TypeScript, Tailwind CSS)
- Backend: Supabase (Postgres, Auth, Storage)
- Hosting: Vercel
- Analytics: Google Analytics 4, Search Console

## Pages

- Home, Services, Fleet, Booking, Contact, Terms, Admin

## Database

- vehicles: id, name, year, photo_url, description, price, availability (jsonb)
- bookings: id, vehicle_id, customer_name, email, start_date, end_date, purpose, status, created_at

## SEO

- Portugal-optimized keywords, meta, schema (JSON-LD), sitemap, robots.txt
- Local SEO: Google My Business, backlinks, reviews

## Development Notes

- Next.js SSG/ISR, Tailwind for UI, Supabase for backend and image storage (see Context7: /supabase/supabase, /supabase/storage-py)
- Custom admin panel (Portuguese labels)
- Pre-commit: ESLint, TS, Jest, Husky
- Cost: Free/low for most services
- **Supabase setup/configuration and CI/CD pipeline are managed by the AI Assistant via direct Supabase and GitHub connections.**

## Supabase Storage Best Practices (Context7)

- Use Supabase Storage for all images and files. See [Supabase Storage Docs](/supabase/supabase) for upload/download examples.
- Store only public URLs in the DB.
- Restrict uploads to authenticated users (admin panel).
- Use buckets for logical separation (e.g., 'vehicles', 'docs').
- Optimize images before upload for web performance.

## Implementation Plan Reference

- The authoritative, up-to-date implementation plan for Vintage Marketing Portugal is maintained in [Docs/Project/implementation-plan.md](../Project/implementation-plan.md).
- All AI agents and human contributors MUST follow this plan for feature development, task breakdown, and project management.
- Updates to the plan should be reflected in this file and referenced in all workflow and decision-making processes.

## Next Steps

- Domain, trademark, Next.js init, Supabase setup, build pages, booking logic, SEO, deploy, train staff, backlinks
