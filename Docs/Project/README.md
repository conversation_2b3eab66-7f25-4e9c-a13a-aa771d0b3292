# Vintage Marketing Portugal

> **Note:** Supabase setup/configuration and CI/CD/deployment are managed by the AI Assistant via direct Supabase and GitHub connections. Human contributors focus on business, SEO, and feature prioritization.

A professional, SEO-optimized website for vintage vehicle rental in Portugal. Built with Next.js, Supabase, and Vercel.

## Features

- Fleet showcase (Fleur de Lys Van, BMW R100 RS)
- Booking system (calendar, no payment)
- Admin panel (manage bookings/vehicles)
- Portuguese language, local SEO
- Mobile-responsive, vintage aesthetic

## Tech Stack

- Next.js (TypeScript, Tailwind CSS)
- Supabase (Postgres, Auth, Storage)
- Vercel (hosting)

## Setup

1. Clone repo & install deps: `npm install`
2. Supabase project setup and configuration are managed by the AI Assistant.
3. Configure Supabase Storage for images (see Context7: /supabase/supabase)
4. Run dev server: `npm run dev`
5. CI/CD pipeline and deployment are managed by the AI Assistant via GitHub connection.

## Development

- See coding-standards.md, development-workflow.md, pre-commit-workflow.md
- All code in TypeScript, use Tai<PERSON>wind for styling
- Pre-commit checks: <PERSON><PERSON><PERSON>, T<PERSON>, Jest, Prettier (via <PERSON><PERSON>)

## SEO

- Portugal-optimized keywords, meta, schema, sitemap
- See technical-architecture.md for details

## License

MIT
