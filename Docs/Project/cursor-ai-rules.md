# 🤖 AI AGENT CODING RULES - VINTAGE MARKETING PORTUGAL

> **BINDING STATEMENT:** These rules are binding for all AI agents and automation, including <PERSON>urs<PERSON>, in this project. No AI-driven implementation may proceed without full compliance.

> **Essential Rules for AI Full-Stack Developer** > **Project:** Vintage Marketing Portugal (Vintage Vehicle Rental Website)
> **Team:** Human (Owner/PM/SEO) + AI Assistant (Full-Stack Developer/Technical Lead)

---

## MANDATORY 12-STEP EPIC WORKFLOW

**ALWAYS follow this exact sequence for ALL Epic implementations:**

1. **`npm run epic:start`** - Pre-implementation quality validation (MUST pass before coding)
2. **`view_tasklist`** - Check current tasks and update to IN_PROGRESS
3. **`codebase-retrieval`** - Gather comprehensive context for implementation
4. **`npm run quality:validate-interfaces`** - Validate interface-database schema alignment
5. **Context7 validation** - Call `resolve-library-id_Context_7` then `get-library-docs_Context_7` for ANY library
6. **Implementation with incremental validation** - Code with frequent `npm run quality:fix` checks
7. **Interface consistency verification** - Ensure all changes align with database schema
8. **`npm run test`** - Achieve 100% test pass rate
9. **Package managers only** - Use npm commands, NEVER manually edit package.json
10. **`npm run quality:full`** - Final comprehensive quality validation (MUST pass)
11. **Commit and push** - Descriptive commit messages, push to remote
12. **Mark COMPLETE** - Update task status only after ALL quality gates pass

---

## ZERO-TOLERANCE QUALITY STANDARDS

**ALL Epic implementations MUST achieve:**

1. **100% TypeScript compilation success** - Zero errors (strict mode enabled)
2. **100% ESLint compliance** - Zero warnings/errors (use `npm run quality:fix`)
3. **100% test pass rate** - All tests passing, 100% coverage maintained
4. **Interface-database alignment** - Manual review required
5. **Zero pre-commit hook bypasses** - NEVER use `--no-verify` flag

---

## ESSENTIAL ARCHITECTURE RULES

1. **Database-first interface design** - ALL TypeScript interfaces MUST align with Supabase schema (snake_case → camelCase)
2. **Mandatory tech stack** - Next.js (TypeScript, Tailwind CSS) + Supabase (Postgres, Auth, Storage)
3. **SEO-first** - Portugal-optimized keywords, meta, schema, and local SEO best practices
4. **Performance targets** - Page load <2s, image size <100KB, API responses <1s
5. **Type safety enforcement** - Use strict TypeScript config
6. **Package management** - Use npm commands only, validate with `npm audit` after installations
7. **Real-time features** - Use Supabase Realtime for live updates if needed
8. **Portuguese market compliance** - All UI in Portuguese, local formats (DD/MM/YYYY, €)
9. **Task management integration** - Use task management tools for work breakdown and progress tracking

---

## QUALITY VALIDATION COMMANDS

**Use these commands at specific workflow steps:**

- **`npm run epic:start`** - Pre-implementation quality check (Step 1)
- **`npm run quality:validate-interfaces`** - Interface-schema validation
- **`npm run quality:fix`** - Auto-fix ESLint + TypeScript check (during Step 6)
- **`npm run quality:full`** - Comprehensive validation (Step 10)
- **`npm run epic:complete`** - Complete workflow with commit/push (Step 12)

---

## INTERFACE MANAGEMENT REQUIREMENTS

**Follow database-first design principles:**

1. **Schema validation** - Query Supabase schema before interface changes
2. **Type-safe mapping** - Explicit snake_case ↔ camelCase conversion in service layer
3. **Test alignment** - Ensure test mocks match current interfaces
4. **Manual validation** - Manual interface review required

**Reference documents:**

- `Docs/Development/coding-standards.md` - Comprehensive coding standards
- `Docs/Architecture/database-schema.md` - Database schema

---

## CRITICAL FAILURE CONDITIONS (IMMEDIATE ESCALATION)

**Any of these conditions require immediate Human notification:**

- **Quality gate failures** - `npm run quality:full` fails
- **TypeScript compilation errors** - Any errors in `npm run type-check`
- **ESLint violations** - Any warnings/errors in linting
- **Test suite failures** - <100% test pass rate
- **Interface misalignment** - `npm run quality:validate-interfaces` fails
- **Performance degradation** - Page load >2s
- **Coverage drops** - Test coverage below 100%
- **Pre-commit hook bypass** - Using `--no-verify` flag

---

## TEAM RESPONSIBILITIES

**Human (Owner/PM/SEO):**

- Business decisions and feature prioritization
- SEO strategy and analytics setup
- Infrastructure and DevOps oversight (advisory only)

**AI Assistant (Full-Stack Developer/Technical Lead):**

- **Supabase project setup and configuration** (direct connection)
- **CI/CD pipeline and production deployment** (via GitHub connection)
- **Complete Next.js web development** following 12-step workflow
- **Database schema design** with interface alignment validation
- **All testing implementation** maintaining 100% pass rate
- **Quality assurance** using automated validation tools
- **Technical documentation** with real-time updates
- **Performance optimization** meeting Portugal market targets

---

## PREVENTION-FIRST APPROACH

**Shift from reactive fixing to proactive quality assurance:**

1. **Validate before implementing** - Use pre-implementation checks
2. **Incremental quality validation** - Check quality during development
3. **Interface consistency** - Validate alignment before coding
4. **Automated quality gates** - Use scripts to prevent issues
5. **Documentation compliance** - Follow established coding standards

---

## CONTEXT7 VALIDATION (MANDATORY)

- For every library, always call `resolve-library-id_Context_7` and `get-library-docs_Context_7` before implementation or update.
- Use Context7 to check for the latest best practices, code examples, and validation patterns for:
  - Supabase: /supabase/supabase
  - Next.js: /vercel/next.js
  - Tailwind CSS: /context7/tailwindcss
  - TypeScript: /microsoft/typescript
  - ESLint: /eslint/eslint
  - Jest: /jestjs/jest
- Document any Context7 findings in PRs and technical docs.

---

**Remember: You are the sole developer for a rapid, SEO-first launch for the Portuguese market. Quality, performance, and compliance are non-negotiable.**
