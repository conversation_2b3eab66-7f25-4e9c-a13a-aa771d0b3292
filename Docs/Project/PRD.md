# Product Requirements Document (PRD)

> **Note:** All technical setup, Supabase configuration, CI/CD pipeline, and production deployment are managed by the AI Assistant. Human team members focus on business, SEO, and feature prioritization.

## Project: Vintage Marketing Portugal Website

### 1. Purpose

Build a professional, mobile-responsive website for a vintage vehicle rental business in Portugal, optimized for local SEO, with a booking system, fleet showcase, and admin interface.

### 2. Target Audience

- Portuguese businesses seeking vintage vehicles for marketing/events.

### 3. Features

- Home: Service overview, CTA, SEO-optimized.
- Services: Use cases for marketing/events.
- Fleet: Vehicle gallery with details/prices.
- Booking: Calendar-based form, availability, submit booking (no payment).
- Contact: Inquiry form, business info, Google Maps.
- Terms: Rental policies, legal info.
- Admin: Manage bookings/vehicles, protected by auth.

### 4. Functional Requirements

- Display vehicle info from database.
- Booking form with calendar, prevents double-booking.
- Admin can approve/cancel bookings, edit vehicles.
- All content in Portuguese, with SEO best practices.

### 5. Non-Functional Requirements

- Mobile-responsive (Tailwind CSS).
- Fast load times (Next.js SSG/ISR, Supabase Storage images).
- Secure admin (Supabase Auth).
- Easy content updates for non-technical staff.
- Cost-effective (free/low-cost tiers).

### 6. Success Metrics

- Top 3 Google ranking for target keywords in Portugal.
- <2s page load time on mobile.
- 100% booking form reliability.
- Positive user/admin feedback.

### 7. Constraints

- No payment gateway.
- Must use Next.js, Supabase, Vercel.
- Portuguese language focus.

### 8. Timeline

- 3-7 days for MVP (UI: 2-3, booking: 1-2, admin/SEO: 1-2).

### 9. Risks

- SEO competition, double-booking bugs, non-technical admin usability.

### 10. Out of Scope

- Payment processing, multi-tenant support, advanced analytics, email notifications.

### 11. References

- Supabase Storage best practices: [Context7: /supabase/supabase]
