# Implementation Plan: Vintage Marketing Portugal

## EPIC 1: Project Initialization & Setup

### Task 1.1: Domain & Legal

- Check domain availability for vintagemarketingportugal.com
- Register domain
- Verify trademark with Portuguese Institute of Industrial Property (INPI)

### Task 1.2: Repository & Tooling

- Initialize Next.js project with TypeScript
- Install Tailwind CSS, next-seo, react-calendar
- Set up <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Jest (pre-commit workflow)
- Configure GitHub repository and CI/CD (Vercel integration)

---

## EPIC 2: Supabase Backend

### Task 2.1: Supabase Project Setup

- Create Supabase project (EU region)
- Set up `vehicles` and `bookings` tables (see database-schema.md)
- Configure Row Level Security (RLS) for tables
- Enable email/password and magic link authentication
- Set up admin users

### Task 2.2: Supabase Storage

- Create storage buckets ('vehicles', 'docs')
- Restrict uploads to admin users
- Store only public URLs in DB
- Enable automatic backups

---

## EPIC 3: Frontend Development (Next.js)

### Task 3.1: Core Pages

- Home (service overview, CTA, SEO-optimized)
- Services (use cases for marketing/events)
- Fleet (vehicle gallery, details, prices, fetched from Supabase)
- Booking (calendar-based form, availability, submit booking)
- Contact (inquiry form, business info, Google Maps)
- Terms (rental policies, legal info)
- Admin (manage bookings/vehicles, protected by auth)

### Task 3.2: UI/UX & Responsiveness

- Apply Tailwind CSS for mobile responsiveness and vintage aesthetic
- Use Portuguese for all UI labels/content

### Task 3.3: SEO Implementation

- Integrate next-seo and next-sitemap
- Add meta tags, schema (JSON-LD), and sitemap
- Optimize image alt text and content for Portugal keywords
- Set up robots.txt

---

## EPIC 4: Booking System

### Task 4.1: Booking Form

- Implement calendar with react-calendar
- Fetch and display vehicle availability from Supabase
- Prevent double-booking
- Submit booking to Supabase
- (Optional) Trigger SendGrid email confirmation

### Task 4.2: Admin Booking Management

- View, approve, cancel bookings
- Update booking status
- Edit vehicle details and availability

---

## EPIC 5: Admin Panel

### Task 5.1: Authentication & Security

- Protect admin routes with Supabase Auth
- Ensure only admins can upload images/files

### Task 5.2: Usability

- Use Portuguese labels (e.g., “Gerir Reservas”, “Confirmar”, “Cancelar”)
- Train staff on admin interface (~10 min)

---

## EPIC 6: SEO & Analytics

### Task 6.1: Local SEO

- Set up Google My Business
- Add business address, phone, email, and fleet photos
- Encourage customer reviews

### Task 6.2: Performance & Monitoring

- Integrate Google Analytics 4 and Search Console
- Monitor rankings and performance

### Task 6.3: Backlink Building

- Outreach to Portuguese automotive blogs, event planners, directories
- Use Ahrefs for backlink opportunities (optional)

---

## EPIC 7: Testing & Quality Assurance

### Task 7.1: Automated Testing

- Write unit tests for booking logic, admin actions, API calls (Jest, React Testing Library)
- Integration tests for booking flow and admin panel

### Task 7.2: Manual Testing

- Test on mobile and desktop browsers
- Use Google’s Mobile-Friendly Test and Rich Results Test

### Task 7.3: Pre-Commit & CI/CD

- Ensure all pre-commit checks pass (lint, type, test, format)
- All tests/checks must pass before PR merge and deploy

---

## EPIC 8: Launch & Handover

### Task 8.1: Deployment

- Deploy to Vercel (production)
- Submit sitemap to Google Search Console

### Task 8.2: Handover & Training

- Provide documentation for non-technical staff
- Final training on admin panel

---

## Notes & References

- All technical setup, Supabase configuration, CI/CD, and deployment are managed by the AI Assistant.
- Human team members focus on business, SEO, and feature prioritization.
- All code in TypeScript, use Tailwind for styling, Portuguese for UI.
- See Docs/Project/cursor-ai-rules.md for AI agent rules and responsibilities.
- Database schema and storage best practices: Docs/Architecture/database-schema.md, Docs/Infrastructure/storage-configuration.md
- Coding standards, workflow, and testing: Docs/Development/coding-standards.md, Docs/Development/development-workflow.md, Docs/Testing/testing-strategy.md

---

### Optional/Stretch Goals

- Blog section for long-tail SEO (Next.js dynamic routes, Supabase content)
- Multilingual support (Next.js i18n)
- Email notifications (SendGrid)
- Advanced analytics (Ahrefs)
