# Animation API Reference

## Component API

### Basic Animation Components

#### FadeIn

```typescript
interface FadeInProps {
  children: React.ReactNode;
  delay?: number; // Default: 0
  duration?: number; // Default: 0.6
  className?: string; // Default: ''
}
```

**Usage:**

```typescript
<FadeIn delay={0.2} duration={0.8} className="mb-4">
  <Content />
</FadeIn>
```

#### SlideIn

```typescript
interface SlideInProps {
  children: React.ReactNode;
  direction?: 'left' | 'right' | 'up' | 'down'; // Default: 'left'
  delay?: number; // Default: 0
  duration?: number; // Default: 0.6
  className?: string; // Default: ''
}
```

**Usage:**

```typescript
<SlideIn direction="up" delay={0.1}>
  <Navigation />
</SlideIn>
```

#### ScaleIn

```typescript
interface ScaleInProps {
  children: React.ReactNode;
  delay?: number; // Default: 0
  duration?: number; // Default: 0.5
  className?: string; // Default: ''
}
```

#### StaggerContainer

```typescript
interface StaggerContainerProps {
  children: React.ReactNode;
  staggerDelay?: number; // Default: 0.1
  className?: string; // Default: ''
}
```

### Page-Level Components

#### AnimatedPageWrapper

```typescript
interface AnimatedPageWrapperProps {
  children: React.ReactNode;
  className?: string; // Default: ''
}
```

**Features:**

- Orchestrates page-level animations
- Uses staggerChildren for coordinated timing
- Integrates with PageTransitionContext

#### PageTransition

```typescript
interface PageTransitionProps {
  children: React.ReactNode;
  className?: string; // Default: ''
}
```

**Features:**

- Handles page enter/exit animations
- Integrates with Next.js App Router
- Uses AnimatePresence with mode="wait"

### Scroll-Based Components

#### ScrollReveal

```typescript
interface ScrollRevealProps {
  children: React.ReactNode;
  threshold?: number; // Default: 0.1 (10% visible)
  delay?: number; // Default: 0
  className?: string; // Default: ''
}
```

**Usage:**

```typescript
<ScrollReveal threshold={0.2} delay={0.1}>
  <Section />
</ScrollReveal>
```

#### TextReveal

```typescript
interface TextRevealProps {
  children: React.ReactNode;
  delay?: number; // Default: 0
  stagger?: boolean; // Default: false
  className?: string; // Default: ''
}
```

#### CardStack

```typescript
interface CardStackProps {
  children: React.ReactNode;
  index: number; // Required: Card position in stack
  total: number; // Required: Total number of cards
  className?: string; // Default: ''
}
```

**Mathematical Implementation:**

- Stack progress: `(index + 1) / total`
- Y-transform: `[0, -100 * index]`
- Scale: `[1, 0.95 - (index * 0.05)]`
- Opacity: `[1, 0.8 - (index * 0.1)]`

#### StaggeredCard

```typescript
interface StaggeredCardProps {
  children: React.ReactNode;
  index: number; // Required: Card position for timing
  className?: string; // Default: ''
}
```

### Micro-Interaction Components

#### MagneticButton

```typescript
interface MagneticButtonProps {
  children: React.ReactNode;
  className?: string; // Default: ''
  strength?: number; // Default: 0.15 (15% attraction)
}
```

**Behavior:**

- Real-time mouse position tracking
- Magnetic attraction based on strength
- Smooth return to center on mouse leave

#### CounterAnimation

```typescript
interface CounterAnimationProps {
  from: number; // Required: Starting value
  to: number; // Required: Ending value
  duration?: number; // Default: 2 (seconds)
  className?: string; // Default: ''
}
```

**Features:**

- RequestAnimationFrame for 60fps
- Custom cubic ease-out easing
- Automatic cleanup on completion

#### AnimatedButton

```typescript
interface AnimatedButtonProps {
  children: React.ReactNode;
  href?: string; // Optional: Link destination
  onClick?: () => void; // Optional: Click handler
  variant?: 'primary' | 'secondary' | 'outline'; // Default: 'primary'
  className?: string; // Default: ''
}
```

#### HoverCard

```typescript
interface HoverCardProps {
  children: React.ReactNode;
  className?: string; // Default: ''
}
```

**Effects:**

- Hover: 2% scale increase, 5px lift
- Tap: Brief scale-down to 98%
- Smooth transitions with easeOut timing

## Hook API

### useScrollAnimations

```typescript
// ScrollReveal hook
export const useScrollReveal = (
  target?: RefObject<HTMLElement>,
  threshold?: number
) => {
  return {
    opacity: MotionValue<number>;
    y: MotionValue<number>;
    scale: MotionValue<number>;
    progress: MotionValue<number>;
  };
};

// CardStack hook
export const useCardStack = (
  index: number,
  total: number,
  target?: RefObject<HTMLElement>
) => {
  return {
    y: MotionValue<number>;
    scale: MotionValue<number>;
    opacity: MotionValue<number>;
    zIndex: number;
  };
};

// Parallax hook
export const useParallaxImproved = (
  speed?: number,
  target?: RefObject<HTMLElement>
) => {
  return {
    y: MotionValue<number>;
    scale: MotionValue<number>;
  };
};
```

### usePerformanceOptimization

```typescript
export const useAnimationOptimization = () => {
  return {
    shouldOptimize: boolean;
    prefersReducedMotion: boolean;
    animationConfig: {
      duration: number;       // 0.1 (optimized) or 0.6 (normal)
      ease: string;          // 'linear' (optimized) or 'easeOut'
      stagger: number;       // 0.01 (optimized) or 0.1 (normal)
    };
  };
};

export const useReducedMotion = () => boolean;
```

## Utility API

### animationOptimization.ts

```typescript
// Device capability detection
export const detectDeviceCapabilities = () => {
  return {
    isLowEnd: boolean;
    hasSlowConnection: boolean;
  };
};

// Timing optimization
export const getOptimizedTiming = (
  baseDuration: number,
  shouldOptimize: boolean
) => number;

// Responsive configuration
export const getResponsiveAnimationConfig = () => {
  return {
    isMobile: boolean;
    isTablet: boolean;
    isDesktop: boolean;
    animationComplexity: 'simple' | 'medium' | 'full';
    staggerMultiplier: number;
    distanceMultiplier: number;
  };
};

// Performance testing
export const testAnimationPerformance = async (
  animationFunction: () => void,
  duration?: number
): Promise<{
  averageFPS: number;
  minFPS: number;
  maxFPS: number;
}>;
```

## Context API

### PageTransitionContext

```typescript
interface PageTransitionContextType {
  isTransitioning: boolean;
  transitionState: 'idle' | 'exiting' | 'entering' | 'entered';
  startTransition: () => void;
  completeTransition: () => void;
}

// Usage
const { isTransitioning, transitionState } = usePageTransition();
```

## Animation Standards

### Timing Values

```typescript
// Standard durations
const ANIMATION_DURATIONS = {
  fast: 0.2, // Micro-interactions
  normal: 0.5, // Basic animations
  slow: 0.8, // Complex animations
  page: 0.5, // Page transitions
};

// Stagger delays
const STAGGER_DELAYS = {
  tight: 0.05, // Text characters
  normal: 0.1, // List items
  loose: 0.15, // Cards
};
```

### Easing Curves

```typescript
const EASING_CURVES = {
  primary: [0.25, 0.46, 0.45, 0.94], // Natural motion
  anticipate: 'anticipate', // Page transitions
  easeOut: 'easeOut', // Reveals
  linear: 'linear', // Performance fallback
};
```

### Transform Values

```typescript
// Slide distances
const SLIDE_DISTANCES = {
  mobile: 20, // Reduced for mobile
  tablet: 35, // Medium for tablet
  desktop: 50, // Full for desktop
};

// Scale values
const SCALE_VALUES = {
  scaleIn: { from: 0.8, to: 1.0 },
  hover: { from: 1.0, to: 1.02 },
  tap: { from: 1.0, to: 0.98 },
};
```

## Performance Guidelines

### GPU Acceleration

**Recommended Properties:**

- `opacity`: Composited layer
- `transform`: Composited layer
  - `translateX`, `translateY`, `translateZ`
  - `scale`, `scaleX`, `scaleY`
  - `rotate`, `rotateX`, `rotateY`, `rotateZ`

**Avoid These Properties:**

- Layout: `width`, `height`, `top`, `left`
- Paint: `background-color`, `border-color`
- Composite: `box-shadow`, `border-radius`

### Memory Management

```typescript
// Proper cleanup in useEffect
useEffect(() => {
  const cleanup = () => {
    // Animation cleanup logic
  };

  return cleanup;
}, [dependencies]);

// Use useCallback for stable references
const handleAnimation = useCallback(() => {
  // Animation logic
}, [dependencies]);
```

### Performance Monitoring

```typescript
// Monitor animation performance
const monitor = new AnimationPerformanceMonitor();
monitor.start();

// Check FPS during development
if (process.env.NODE_ENV === 'development') {
  console.log('Animation FPS:', monitor.getCurrentFPS());
}
```

This API reference provides complete documentation for all animation components, hooks, utilities, and performance guidelines in the Vintage Marketing Portugal animation system.
