# Development Guide: Vintage Marketing Portugal

## Quick Start

### Prerequisites

- Node.js 18+ and npm
- Git for version control
- Code editor (VS Code recommended)

### Setup

1. **Clone and Install**:

   ```bash
   git clone <repository-url>
   cd vintage-marketing-portugal
   npm install
   ```

2. **Environment Configuration**:

   ```bash
   cp .env.local.example .env.local
   # Configure your Supabase and Resend API keys
   ```

3. **Start Development**:
   ```bash
   npm run dev
   ```

## Project Structure

```
src/
├── app/                    # Next.js App Router pages
│   ├── globals.css        # Global styles and design system
│   ├── layout.tsx         # Root layout with providers
│   ├── page.tsx           # Homepage component
│   ├── servicos/          # Services page
│   ├── frota/             # Fleet showcase page
│   ├── reservas/          # Booking system page
│   ├── contacto/          # Contact page
│   └── termos/            # Terms and conditions page
├── components/            # Reusable React components
│   ├── layout/           # Layout components (<PERSON><PERSON>, <PERSON>er)
│   ├── forms/            # Form components (BookingForm, etc.)
│   ├── ui/               # UI components (<PERSON><PERSON><PERSON>, Modal)
│   └── ContactPage.tsx   # Contact page component
├── hooks/                # Custom React hooks
│   └── useBookingForm.ts # Booking form state management
├── lib/                  # Utility functions and configurations
│   └── supabase.ts       # Supabase client configuration
├── types/                # TypeScript type definitions
│   └── index.ts          # Database types and interfaces
└── utils/                # Helper functions
```

## Development Workflow

### 1. Feature Development

1. **Create Feature Branch**:

   ```bash
   git checkout -b feature/new-feature-name
   ```

2. **Implement Feature**:
   - Follow component patterns established in existing code
   - Use TypeScript for all new code
   - Follow design system conventions
   - Add Portuguese content for user-facing text

3. **Test Implementation**:

   ```bash
   npm run dev          # Test in development
   npm run build        # Test production build
   npm run type-check   # Verify TypeScript
   npm run lint         # Check code quality
   ```

4. **Commit Changes**:
   ```bash
   git add .
   git commit -m "feat: implement new feature with Portuguese localization"
   git push origin feature/new-feature-name
   ```

### 2. Component Development

#### Creating New Components

1. **Component Structure**:

   ```typescript
   // src/components/NewComponent.tsx
   import { ComponentProps } from '@/types';

   interface NewComponentProps {
     // Define props with TypeScript
   }

   export default function NewComponent({ ...props }: NewComponentProps) {
     return (
       <div className="design-system-classes">
         {/* Component content */}
       </div>
     );
   }
   ```

2. **Design System Integration**:
   - Use design system color classes (`primary-black`, `background-cream`)
   - Follow typography scale (`text-hero`, `text-section`, `text-card`)
   - Use component classes (`btn-primary`, `card`, `section-light`)
   - Implement responsive design with mobile-first approach

3. **Portuguese Localization**:
   - All user-facing text in Portuguese
   - Error messages in Portuguese
   - Form labels and placeholders in Portuguese
   - SEO content optimized for Portuguese market

### 3. Database Integration

#### Working with Supabase

1. **Type Safety**:

   ```typescript
   import { Vehicle, Booking, ContactInquiry } from '@/types';
   import { supabase } from '@/lib/supabase';

   // Always check if supabase client is configured
   if (!supabase) {
     console.error('Supabase not configured');
     return;
   }
   ```

2. **Data Fetching Patterns**:

   ```typescript
   const [data, setData] = useState<Vehicle[]>([]);
   const [loading, setLoading] = useState(true);
   const [error, setError] = useState<string | null>(null);

   useEffect(() => {
     const fetchData = async () => {
       try {
         const { data: vehicles, error } = await supabase
           .from('vehicles')
           .select('*')
           .order('created_at', { ascending: false });

         if (error) throw error;
         setData(vehicles || []);
       } catch (err) {
         setError(err.message);
       } finally {
         setLoading(false);
       }
     };

     fetchData();
   }, []);
   ```

3. **Form Submissions**:

   ```typescript
   const handleSubmit = async (formData: BookingFormData) => {
     setIsSubmitting(true);
     try {
       const { data, error } = await supabase
         .from('bookings')
         .insert(formData)
         .select()
         .single();

       if (error) throw error;
       // Handle success
     } catch (error) {
       // Handle error with Portuguese message
       setError('Erro ao criar reserva. Tente novamente.');
     } finally {
       setIsSubmitting(false);
     }
   };
   ```

## Design System Usage

### Color System

```typescript
// Primary colors
className = 'bg-primary-black text-primary-white';
className = 'text-primary-mediumGray';

// Background colors
className = 'bg-background-cream';
className = 'section-light'; // Predefined section with cream background

// Accent colors
className = 'text-accent-vintage hover:text-accent-warmBrown';
```

### Typography

```typescript
// Heading hierarchy
className = 'text-hero font-hero'; // Main page titles
className = 'text-section font-section'; // Section headings
className = 'text-card font-card'; // Card titles

// Body text
className = 'text-body'; // Standard body text
className = 'text-body-secondary'; // Secondary/helper text
```

### Components

```typescript
// Buttons
className = 'btn-primary'; // Black button
className = 'btn-secondary'; // Outlined button
className = 'btn-cta'; // CTA button with icon

// Cards
className = 'card'; // Standard card with shadow and hover

// Sections
className = 'section-light'; // Light cream background
className = 'section-dark'; // Dark background with white text
```

## Form Development

### Multi-step Forms

1. **Use Custom Hook**:

   ```typescript
   import { useBookingForm } from '@/hooks/useBookingForm';

   const {
     currentStep,
     formData,
     updateFormData,
     goToNextStep,
     canProceedToNext,
   } = useBookingForm();
   ```

2. **Step Components**:
   - Create separate components for each step
   - Implement validation logic
   - Use consistent error handling
   - Provide Portuguese error messages

### Form Validation

```typescript
const validateEmail = (email: string): boolean => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
};

const validatePhone = (phone: string): boolean => {
  // Portuguese phone number validation
  const phoneRegex = /^(\+351)?[0-9]{9}$/;
  return phoneRegex.test(phone.replace(/\s/g, ''));
};
```

## SEO Implementation

### Page Metadata

```typescript
import { Metadata } from 'next';

export const metadata: Metadata = {
  title: 'Página - Vintage Marketing Portugal',
  description: 'Descrição otimizada para SEO em português',
  keywords: 'palavras-chave, portugal, vintage, marketing',
  openGraph: {
    title: 'Página - Vintage Marketing Portugal',
    description: 'Descrição para redes sociais',
    images: ['/images/og-image.jpg'],
  },
};
```

### Portuguese Content Guidelines

- Use Portuguese keywords naturally in content
- Target local search terms (Lisboa, Porto, Portugal)
- Include business-specific terms (aluguer, veículos vintage, eventos)
- Optimize for mobile search (Portuguese users are mobile-first)

## Testing Guidelines

### Component Testing

```typescript
import { render, screen } from '@testing-library/react';
import Component from './Component';

describe('Component', () => {
  it('renders Portuguese content correctly', () => {
    render(<Component />);
    expect(screen.getByText('Texto em Português')).toBeInTheDocument();
  });
});
```

### Integration Testing

- Test form submissions with Supabase
- Verify Portuguese error messages
- Test responsive design on mobile devices
- Validate SEO metadata

## Performance Optimization

### Image Optimization

```typescript
import Image from 'next/image';

<Image
  src="/images/vehicle.jpg"
  alt="Descrição em português"
  width={800}
  height={600}
  className="rounded-image"
  priority={isAboveFold}
/>
```

### Code Splitting

- Use dynamic imports for heavy components
- Implement lazy loading for images
- Optimize bundle size with tree shaking

## Deployment

### Production Checklist

- [ ] All environment variables configured
- [ ] Supabase RLS policies tested
- [ ] Portuguese content reviewed
- [ ] SEO metadata complete
- [ ] Mobile responsiveness verified
- [ ] Performance metrics acceptable
- [ ] Error handling implemented

### Monitoring

- Google Analytics 4 for user behavior
- Search Console for SEO performance
- Supabase dashboard for database metrics
- Vercel analytics for performance monitoring

## Troubleshooting

### Common Issues

1. **Supabase Connection**:

   ```typescript
   // Always check if client is configured
   if (!supabase) {
     console.error('Supabase client not configured');
     return;
   }
   ```

2. **TypeScript Errors**:

   ```bash
   npm run type-check  # Check for type errors
   ```

3. **Build Errors**:

   ```bash
   npm run build      # Test production build
   ```

4. **Styling Issues**:
   - Check Tailwind class names
   - Verify design system integration
   - Test responsive breakpoints

### Getting Help

- Check existing component patterns
- Review design system documentation
- Test with Portuguese content
- Verify mobile-first responsive design
