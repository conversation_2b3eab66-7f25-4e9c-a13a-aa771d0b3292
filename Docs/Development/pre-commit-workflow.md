# Pre-Commit Workflow: Vintage Marketing Portugal

## Tools

- **<PERSON>sky**: Git hooks
- **ESLint**: Linting
- **TypeScript**: Type checking
- **Jest**: Unit/integration tests
- **Prettier**: Code formatting

## Workflow

1. On `pre-commit`, run:
   - `eslint . --ext .ts,.tsx`
   - `tsc --noEmit`
   - `jest --bail`
   - `prettier --check .`
2. Block commit if any check fails.
3. All checks must pass before PR merge.

## Setup

- Install <PERSON><PERSON>: `npx husky-init && npm install`
- Add scripts to `package.json` for lint, type, test, format.
- Configure <PERSON><PERSON> hooks in `.husky/` directory.

## Context7 Library References

- Supabase: /supabase/supabase
- Next.js: /vercel/next.js
- Tailwind CSS: /context7/tailwindcss
- TypeScript: /microsoft/typescript
- ESLint: /eslint/eslint
- Jest: /jestjs/jest

**Tip:** Always check Context7 for the latest libraries, code examples, and validation patterns before implementing or updating any feature.
