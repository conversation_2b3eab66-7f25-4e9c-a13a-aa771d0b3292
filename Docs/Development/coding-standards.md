# Coding Standards: Vintage Marketing Portugal

## General

- Use TypeScript for all code (frontend/backend).
- Use functional components and React hooks.
- Write clear, descriptive variable and function names (in English).
- Use Portuguese for UI labels/content only.
- Keep components small and focused.

## Next.js

- Use file-based routing.
- Prefer SSG/ISR for static content, SSR only if needed.
- Use `getStaticProps`/`getServerSideProps` appropriately.
- Organize pages in `/pages`, components in `/components`.

## Tailwind CSS

- Use utility classes for layout and styling.
- Use responsive classes (`sm:`, `md:`, etc.) for mobile support.
- Avoid custom CSS unless necessary.

## Supabase

- Use typed API calls.
- Validate all user input before writing to DB.
- Never expose service keys to frontend.

## Linting/Formatting

- Use ESLint (with Next.js/TypeScript plugins).
- Use Prettier for code formatting.
- Fix all lint errors before commit.

## Testing

- Use Jest and React Testing Library for unit/integration tests.
- Write tests for all critical logic (booking, admin, etc.).
