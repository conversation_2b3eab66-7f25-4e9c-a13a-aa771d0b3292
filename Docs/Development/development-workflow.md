# Development Workflow: Vintage Marketing Portugal

> **Note:** Supabase setup/configuration and CI/CD pipeline (including production deployment) are fully managed by the AI Assistant via direct Supabase and GitHub connections. Human team members focus on business, SEO, and feature prioritization.

## Branching

- Use `main` for production-ready code.
- Create feature branches (`feature/xyz`) for new features.
- Use `fix/xyz` for bug fixes.

## Pull Requests

- Open PRs from feature/fix branches to `main`.
- PRs must pass all checks (lint, type, tests) before merge.
- At least one code review required before merging.

## Commits

- Use clear, descriptive commit messages (English).
- Reference issues/tasks in commits if applicable.

## Code Review

- Review for code quality, standards, and business logic.
- Suggest improvements, request changes as needed.

## CI/CD

- CI/CD pipeline and production deployment are managed by the AI Assistant via GitHub connection.
- All tests/checks must pass before deploy.

## Context7 Library References

- Supabase: /supabase/supabase
- Next.js: /vercel/next.js
- Tailwind CSS: /context7/tailwindcss
- TypeScript: /microsoft/typescript
- ESLint: /eslint/eslint
- Jest: /jestjs/jest

**Tip:** Always check Context7 for the latest libraries, code examples, and validation patterns before implementing or updating any feature.
