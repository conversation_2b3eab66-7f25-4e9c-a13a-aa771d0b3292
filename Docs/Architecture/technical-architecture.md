# Technical Architecture: Vintage Marketing Portugal

## Overview

A modern web app for vintage vehicle rental, built with Next.js (frontend), Supabase (backend/database/auth/storage), and Vercel (hosting). Integrates with Google services for analytics/SEO.

> **Note:** Supabase setup/configuration and CI/CD pipeline (including production deployment) are fully managed by the AI Assistant via direct Supabase and GitHub connections.

## Components

- **Frontend**: Next.js 15 (TypeScript, Tailwind CSS)
  - **Layout System**: Complete layout architecture with Header and Footer components
  - **Design System**: Comprehensive styling system with color palette, typography, and component styles
  - **Animation System**: Framer Motion-based animation components for enhanced user experience
  - **Page Components**: All core pages implemented (Home, Services, Fleet, Booking, Contact, Terms)
  - **Booking System**: Multi-step form with state management and validation
  - **Vehicle Management**: Real-time display with modal integration
  - **Form Components**: Modular form components with Portuguese validation
- **Backend/DB**: Supabase ✅ **IMPLEMENTED**
  - PostgreSQL tables: vehicles, bookings, contact_inquiries, email_templates (all deployed)
  - Row Level Security (RLS): Comprehensive policies for public/admin access control
  - Auth: email/password authentication for admin users
  - RESTful and real-time APIs with full TypeScript support
  - Storage: All images/files managed via Supabase Storage
  - Database functions: is_admin() for JWT-based role checking (implemented)
  - Auto-updating timestamps with database triggers
- **Email System**: Email template management with variable substitution
- **SEO/Analytics**: Google Analytics 4, Search Console, next-seo, next-sitemap
- **Hosting/Deployment**: Vercel (auto-deploy, SSL, CDN) managed by the AI Assistant via GitHub connection

## Supabase Setup

- Supabase project setup, table creation, auth, and storage configuration are managed by the AI Assistant.

## Data Flow

1. User browses fleet (Next.js fetches from Supabase)
2. User books vehicle (form posts to Supabase)
3. Admin manages bookings/vehicles (protected Next.js pages, Supabase Auth)

## Security

- Supabase Auth for admin
- No payment data stored
- HTTPS via Vercel

## Localization

- All content in Portuguese, with local SEO and city targeting

## Design System Architecture

### Tailwind Configuration

The design system is implemented through a comprehensive Tailwind CSS configuration (`tailwind.config.js`):

- **Content Paths**: Configured to scan all TypeScript/JSX files in `src/` directory
- **Theme Extension**: Custom design tokens extend the default Tailwind theme
- **Color System**: Structured color palette with semantic naming (primary, background, accent)
- **Typography Scale**: Responsive font sizing using CSS clamp() for fluid typography
- **Component Tokens**: Consistent spacing, shadows, and border radius values
- **Animation System**: Custom keyframes and transitions for micro-interactions

### Typography System

- **Font Configuration**: Open Sans font family loaded via Next.js Google Fonts with multiple weights (400, 500, 600, 700, 800)
- **CSS Variables**: Font family exposed as `--font-open-sans` CSS variable for Tailwind integration
- **Design System**: Open Sans used for headings via `font-heading` class, Inter for body text
- **Specialty Fonts**: Limelight font for logo and brand elements via `font-logo` class
- **Responsive Typography**: Clamp-based font sizing for optimal readability across devices
- **Font Loading**: Optimized font loading with Next.js font optimization and variable font support

### Color Palette

The design system uses a structured color palette defined in `tailwind.config.js`:

- **Primary Colors**:
  - `primary.black`: #000000
  - `primary.darkGray`: #1A1A1A
  - `primary.mediumGray`: #666666
  - `primary.lightGray`: #999999
  - `primary.white`: #FFFFFF

- **Background Colors**:
  - `background.cream`: #F5F1E8
  - `background.beige`: #E8E0D0
  - `background.darkSection`: #000000
  - `background.cardBackground`: #f5f1e8ff (cream with alpha)

- **Accent Colors**:
  - `accent.vintage`: #8B4513
  - `accent.warmBrown`: #A0522D

- **Legacy Support**: Vintage color palette maintained for backward compatibility during migration

### Component System

The design system provides consistent styling tokens defined in Tailwind configuration:

- **Typography Scale**:
  - `fontSize.hero`: clamp(2.5rem, 5vw, 4rem) with `fontWeight.hero`: 900
  - `fontSize.section`: clamp(1.8rem, 3vw, 2.5rem) with `fontWeight.section`: 800
  - `fontSize.card`: 1.25rem with `fontWeight.card`: 700
  - `fontSize.nav`: 0.95rem with `fontWeight.nav`: 500

- **Border Radius System**:
  - `borderRadius.card`: 12px
  - `borderRadius.button`: 24px
  - `borderRadius.cta`: 50px
  - `borderRadius.image`: 8px

- **Shadow System**:
  - `boxShadow.card`: 0 8px 24px rgba(0, 0, 0, 0.12)
  - `boxShadow.card-hover`: 0 12px 32px rgba(0, 0, 0, 0.18)
  - `boxShadow.gallery`: 0 4px 16px rgba(0, 0, 0, 0.1)
  - `boxShadow.gallery-hover`: 0 8px 24px rgba(0, 0, 0, 0.15)

- **Animation System**: Custom keyframes for card hover, button hover, and CTA hover effects with consistent 0.3s ease timing

### Parallax System

The design system includes optimized CSS classes for parallax effects:

- **Hero Background Optimization** (`.hero-bg-image`):
  - Responsive image positioning across device sizes
  - Performance optimizations with `transform: translateZ(0)` and `backface-visibility: hidden`
  - Device-specific opacity and positioning adjustments
  - Mobile: 70% opacity with 5% center positioning
  - Tablet: 40% opacity with 60% center positioning
  - Desktop: 40% opacity with center 35% positioning

- **Parallax Container** (`.parallax-container`):
  - Performance optimizations for smooth scrolling
  - Hardware acceleration with `transform: translateZ(0)`
  - 3D rendering context with `perspective: 1000px`
  - Prevents visual artifacts during parallax animations

### Animation System Architecture

#### Comprehensive Animation Ecosystem

The animation system represents a production-ready, best-in-class implementation featuring:

**System Organization**:

```
src/components/animations/
├── index.ts                    # Barrel exports for clean imports
├── FadeIn.tsx                  # Basic fade-in animations
├── SlideIn.tsx                 # Directional slide animations
├── ScaleIn.tsx                 # Scale-up animations
├── StaggerContainer.tsx        # Staggered child animations
├── AnimatedPageWrapper.tsx     # Page-level animation orchestration
├── PageTransition.tsx          # Page transition system
├── ScrollAnimations.tsx        # Scroll-based animation components
└── MicroInteractions.tsx       # Interactive micro-animations

src/hooks/
├── useScrollAnimations.ts      # Scroll animation hooks
└── usePerformanceOptimization.ts # Performance optimization hooks

src/contexts/
└── PageTransitionContext.tsx   # Global transition state management

src/utils/
└── animationOptimization.ts    # Performance utilities and testing
```

**Import Patterns**:

- From pages: `import { ScrollReveal, TextReveal, StaggeredCard } from '@/components/animations';`
- From components: `import { FadeIn, SlideIn, HoverCard } from '@/components/animations';`
- Barrel export enables clean imports of multiple components

#### Advanced Framer Motion Implementation

The system leverages Framer Motion's full feature set with sophisticated patterns:

- **Variants with StaggerChildren**: Proper orchestration using Framer Motion's built-in timing
- **Event-Driven State Management**: Uses animation callbacks instead of manual timing
- **Hardware Acceleration**: Exclusively uses GPU-accelerated properties (`transform`, `opacity`)
- **Accessibility Excellence**: Comprehensive `prefers-reduced-motion` support with graceful fallbacks
- **Performance Optimization**: Device capability detection and automatic adaptation
- **Next.js App Router Integration**: Seamless page transitions with Suspense coordination

#### Animation Components

##### FadeIn Component (`components/animations/FadeIn.tsx`)

- **Purpose**: Reusable fade-in animation wrapper for content reveals
- **Animation Properties**:
  - Initial: `opacity: 0, y: 20` (invisible, slightly below)
  - Final: `opacity: 1, y: 0` (fully visible, in position)
  - Easing: Custom cubic-bezier `[0.25, 0.46, 0.45, 0.94]` for natural motion
- **Customization**:
  - `delay`: Animation delay in seconds (default: 0)
  - `duration`: Animation duration in seconds (default: 0.6)
  - `className`: Optional CSS classes for styling
- **Usage Pattern**:
  ```typescript
  <FadeIn delay={0.2} duration={0.8} className="mb-8">
    <VehicleCard vehicle={vehicle} />
  </FadeIn>
  ```

#### Planned Animation Components

- **ScaleIn**: Scale-based entrance animations for cards and modals
- **SlideIn**: Directional slide animations for navigation and content
- **StaggerContainer**: Sequential animation timing for lists and grids

#### Animation Performance Strategy

- **GPU Acceleration**: All animations use `transform` and `opacity` properties
- **Reduced Motion**: Automatic detection and respect for accessibility preferences
- **Lazy Loading**: Animation components only load when needed
- **Memory Management**: Proper cleanup of animation listeners and timers

### Responsive Design

- **Mobile-First Approach**: All components designed for mobile and scaled up
- **Breakpoint System**: Consistent breakpoints across all components
- **Grid Layouts**: Responsive grid systems (1/2/3 columns based on screen size)
- **Typography Scaling**: Clamp-based responsive font sizing

## State Management Architecture

### Custom Hooks

#### useBookingForm Hook (`src/hooks/useBookingForm.ts`)

- **Purpose**: Centralized state management for multi-step booking form
- **Features**:
  - Step navigation with validation
  - Form data persistence across steps
  - Progress tracking and completion states
  - Reset functionality for new bookings
- **State Management**:
  - `currentStep`: Current step in booking process
  - `formData`: Accumulated form data across all steps
  - `isSubmitting`: Loading state during form submission
  - `isCompleted`: Success state after booking completion
- **Validation Logic**: Step-specific validation rules with `canProceedToNext()` function

#### useParallax Hook (`src/hooks/useParallax.ts`)

- **Purpose**: Custom React hook for creating smooth parallax scroll effects
- **Features**:
  - Real-time scroll position tracking via `window.pageYOffset`
  - Configurable speed multiplier for effect intensity control
  - Automatic event listener cleanup for memory leak prevention
  - Smooth animation calculations for visual depth enhancement
- **Parameters**:
  - `speed` (optional): Number between 0-1 controlling parallax intensity (default: 0.5)
- **Return Value**: `offset` number representing calculated parallax offset in pixels
- **Use Cases**:
  - Hero section background image parallax
  - Layered content movement effects
  - Visual depth enhancement for marketing sections
  - Background element animations during scroll

### Form State Patterns

- **Multi-step Forms**: Centralized state with step validation
- **Real-time Validation**: Immediate feedback on form field changes
- **Error Handling**: Consistent error state management across components
- **Loading States**: Unified loading state patterns for async operations

## TypeScript Integration

- **Auto-generated Types**: Database types are automatically generated from Supabase schema
- **Type Safety**: Full end-to-end type safety from database to frontend components
- **Form Interfaces**: Dedicated form data interfaces (`BookingFormData`, `ContactFormData`)
- **Status Enums**: Strongly typed status values for bookings, inquiries, and email templates
- **API Responses**: Consistent `ApiResponse<T>` interface for all API calls
- **Custom Hook Types**: Strongly typed custom hooks with proper return types

## Key Type Definitions

```typescript
// Database table types (auto-generated)
export type Vehicle = Database['public']['Tables']['vehicles']['Row'];
export type Booking = Database['public']['Tables']['bookings']['Row'];
export type ContactInquiry =
  Database['public']['Tables']['contact_inquiries']['Row'];
export type EmailTemplate =
  Database['public']['Tables']['email_templates']['Row'];

// Status enums for type safety
export type BookingStatus = 'pending' | 'confirmed' | 'cancelled' | 'completed';
export type InquiryStatus = 'new' | 'in_progress' | 'resolved' | 'closed';
export type EmailTemplateType =
  | 'booking_confirmation'
  | 'booking_cancellation'
  | 'contact_response'
  | 'admin_notification';
```

## Component Architecture

### Layout Components

#### Header Component (`src/components/layout/Header.tsx`)

- **Purpose**: Main navigation header with responsive design
- **Features**:
  - Responsive mobile/desktop navigation
  - Hamburger menu for mobile devices
  - Brand logo with link to home page
  - Navigation links: Home, Frota, Reservas, Contacto
  - Vintage color scheme integration (darkBrown background, cream text, gold hover)
- **State Management**: Local state for mobile menu toggle
- **Accessibility**: Proper ARIA labels and semantic HTML
- **Styling**: Tailwind CSS with custom vintage color palette

#### Footer Component (Planned)

- Contact information and business details
- Links to terms and policies
- Social media integration

### Page Components

#### FleetPage Component (`src/app/frota/page.tsx`)

- **Purpose**: Display all available vintage vehicles with detailed information
- **Features**:
  - Real-time data fetching from Supabase vehicles table
  - Responsive grid layout (1/2/3 columns based on screen size)
  - Loading states with spinner animation
  - Error handling with Portuguese error messages and retry functionality
  - Empty state handling for when no vehicles are available
  - Integration with VehicleCard and VehicleModal components
  - Call-to-action section for bookings
- **State Management**:
  - `vehicles`: Array of Vehicle objects from database
  - `loading`: Boolean for loading state display
  - `error`: String for error message display
  - `selectedVehicle`: Currently selected vehicle for modal
  - `isModalOpen`: Boolean controlling modal visibility
- **Data Flow**:
  1. Component mounts → `fetchVehicles()` called
  2. Supabase query: `SELECT * FROM vehicles ORDER BY created_at ASC`
  3. Loading state → Success/Error state
  4. Vehicle click → Modal opens with detailed view

#### VehicleCard Component (`src/components/VehicleCard.tsx`)

- **Purpose**: Reusable card component for vehicle display in grid layouts
- **Features**:
  - Next.js Image optimization with responsive sizing
  - Price formatting with fallback ("Preço sob consulta")
  - Year badge overlay on vehicle images
  - Description truncation using CSS line-clamp
  - Hover animations and transform effects
  - Direct booking link with vehicle ID parameter
  - Fallback placeholder for missing images
- **Props**: `vehicle: Vehicle`, `onClick: () => void`
- **Styling**: Card shadows, vintage colors, smooth transitions

#### VehicleModal Component (`src/components/VehicleModal.tsx`)

- **Purpose**: Modal overlay for detailed vehicle information
- **Features**:
  - Headless UI Dialog for accessibility compliance
  - Responsive grid layout (image + details)
  - Smooth enter/exit animations
  - Vehicle specifications and feature lists
  - Dual action buttons (booking and contact)
  - Contact information section
- **Props**: `vehicle: Vehicle | null`, `isOpen: boolean`, `onClose: () => void`
- **Dependencies**: `@headlessui/react` for modal implementation

### Navigation Structure

```
/ (Home) → Main landing page with service overview
/servicos → Services overview with detailed service descriptions ✅ IMPLEMENTED
/frota → Fleet showcase with vehicle grid and modals ✅ IMPLEMENTED
/reservas → Multi-step booking system ✅ IMPLEMENTED
/contacto → Contact form with business information and Google Maps ✅ IMPLEMENTED
/termos → Terms and conditions page ✅ IMPLEMENTED
```

### Booking System Architecture

#### Multi-Step Form Flow

The booking system implements a 4-step wizard pattern with centralized state management:

1. **Vehicle Selection** (`VehicleSelection.tsx`)
   - Fetches available vehicles from Supabase
   - Displays vehicle grid with images and key details
   - Handles vehicle selection with visual feedback
   - Validates selection before proceeding

2. **Date Selection** (`DateSelection.tsx`)
   - Custom date picker with availability checking
   - Integrates with vehicle booking data to prevent conflicts
   - Date range validation (start/end dates)
   - Portuguese date formatting and error messages

3. **Customer Details** (`CustomerDetails.tsx`)
   - Form validation with required fields (name, email)
   - Optional fields (phone, special requirements)
   - Real-time validation feedback
   - Accessibility-compliant form inputs

4. **Booking Confirmation** (`BookingConfirmation.tsx`)
   - Complete booking summary with vehicle details
   - Date range and duration calculations
   - Final submission to Supabase bookings table
   - Success/error handling with Portuguese messaging

#### State Management Pattern

```typescript
// Centralized form state in BookingForm component
interface BookingFormState extends Partial<BookingFormData> {
  selectedVehicle?: Vehicle;
}

// Step validation logic
const canProceedToNext = (): boolean => {
  switch (currentStep) {
    case 'vehicle':
      return !!formData.vehicle_id && !!formData.selectedVehicle;
    case 'dates':
      return !!formData.start_date && !!formData.end_date;
    case 'details':
      return !!(formData.customer_name && formData.customer_email);
    case 'confirmation':
      return true;
  }
};
```

#### User Experience Features

- **Progress Indicator**: Visual step progress with completed step checkmarks
- **Step Navigation**: Forward/backward navigation with validation gates
- **Responsive Design**: Mobile-first layout with collapsible descriptions
- **Success States**: Completion animation with option to create new booking
- **Error Handling**: Portuguese error messages with retry functionality

## Image Management & Storage

### Supabase Storage Integration (Context7)

- Use Supabase Storage for all user-uploaded images/files. See [Context7: /supabase/supabase] for integration examples and best practices.
- Store public URLs in the DB, restrict uploads to admin users.

### External Image Sources

- **Unsplash Integration**: Configured for high-quality placeholder and demo images
  - Domain: `images.unsplash.com`
  - Used for vehicle placeholders and marketing imagery during development
  - Next.js Image optimization enabled for Unsplash URLs
- **Image Optimization**: Next.js automatic optimization for all image sources
  - WebP format conversion when supported
  - Responsive image sizing with `sizes` attribute
  - Lazy loading by default
