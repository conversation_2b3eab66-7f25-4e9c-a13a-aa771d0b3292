# Component Documentation

## Layout Components

### Header Component

**File**: `src/components/layout/Header.tsx`

**Purpose**: Main navigation header providing site-wide navigation with responsive design.

**Features**:

- Responsive navigation (desktop horizontal menu, mobile hamburger menu)
- Brand logo linking to homepage
- Navigation menu with key site sections
- Mobile-first design with collapsible menu
- Vintage color scheme integration
- Smooth hover transitions

**Navigation Structure**:

- Home (`/`) - Landing page with hero section, service overview, and featured vehicles ✅ IMPLEMENTED
- Serviços (`/servicos`) - Services overview with detailed descriptions ✅ IMPLEMENTED
- Frota (`/frota`) - Fleet showcase with vehicle grid and modals ✅ IMPLEMENTED
- Reservas (`/reservas`) - Multi-step booking system ✅ IMPLEMENTED
- Contacto (`/contacto`) - Contact form with business information ✅ IMPLEMENTED
- Termos (`/termos`) - Terms and conditions page ✅ IMPLEMENTED

**State Management**:

- `isMenuOpen`: Boolean state controlling mobile menu visibility
- Local component state using React `useState` hook

**Styling**:

- Uses design system color tokens from Tailwind configuration
- Background: Dark section styling with `bg-background-darkSection`
- Text: Primary white text with `text-primary-white`
- Hover: Accent color transitions with `hover:text-accent-vintage`
- Responsive breakpoints: Mobile-first approach with `md:` prefix for desktop styles

**Accessibility**:

- Proper ARIA labels on interactive elements
- Semantic HTML structure with `<header>`, `<nav>`, and `<h1>` tags
- Keyboard navigation support
- Screen reader friendly menu toggle button

**Dependencies**:

- Next.js `Link` component for client-side navigation
- React `useState` hook for state management

### Footer Component

**File**: `src/components/layout/Footer.tsx`

**Purpose**: Site-wide footer providing business information and navigation links.

**Features**:

- Business contact information (email, phone, address)
- Operating hours and location details
- Quick navigation links to all main pages
- Social media integration (Facebook, Instagram)
- Design system compliant styling with dark section background
- Responsive grid layout (1 column mobile, 3 columns desktop)

**Content Sections**:

1. **Company Information**: Business description and copyright
2. **Contact Details**: Email, phone, and physical address with clickable links
3. **Quick Links**: Navigation to all main site pages including terms

**Styling**:

- Uses `bg-background-darkSection` for consistent dark background
- Primary white text (`text-primary-white`) with medium gray secondary text (`text-primary-mediumGray`)
- Hover effects on links with smooth transitions using design system accent colors
- Responsive design with proper spacing and grid layout
- Full integration with design system color palette and typography scale

## Page Components

### HomePage Component

**File**: `src/app/page.tsx`

**Purpose**: Main landing page showcasing the vintage vehicle rental service with hero section, service overview, and featured vehicles.

**Features**:

- **Hero Section**: Full-screen hero with background image (Fleur de Lys van), compelling headline, and dual CTA buttons
- **Service Overview**: Three-column grid explaining key use cases (corporate events, photo shoots, special events)
- **Featured Vehicles**: Integration with FeaturedVehicles component showing first 2 vehicles from database
- **Call-to-Action Sections**: Multiple conversion points throughout the page
- **SEO Optimization**: Portuguese meta tags, structured data, and keyword optimization

**Content Sections**:

1. **Hero Section**: Background image overlay with value proposition and primary CTAs
2. **Service Benefits**: Three-column grid with icons and service descriptions
3. **Featured Vehicles**: Real-time vehicle showcase with modal integration
4. **Final CTA**: Contact section with compelling messaging

**Design System Integration**:

- Uses `section-light` and alternating background colors for visual hierarchy
- Consistent typography with hero, section, and card heading styles
- Button styling follows design system (primary, secondary, CTA variants)
- Responsive grid layouts with proper spacing and alignment

**SEO Configuration**:

- Title: "Vintage Marketing Portugal - Aluguer de Veículos Vintage para Eventos"
- Description: Comprehensive Portuguese description with target keywords
- Keywords: "aluguer veículos vintage, carros vintage eventos, marketing Portugal"
- Open Graph and Twitter Card integration

**Dependencies**:

- FeaturedVehicles component for vehicle showcase
- Next.js Image component for hero background
- Design system classes for consistent styling

### ReservasPage Component

**File**: `src/app/reservas/page.tsx`

**Purpose**: Booking page that provides a complete reservation interface for vintage vehicle rentals.

**Features**:

- Clean, focused layout with hero section and booking form integration
- Portuguese SEO optimization with booking-specific keywords
- Integration with multi-step BookingForm component
- Responsive design with centered content layout
- Design system compliant styling with cream background

**Content Sections**:

1. **Hero Section**: Page title and booking process description
2. **Booking Form Section**: Integrated BookingForm component with full reservation workflow

**SEO Configuration**:

- Title: "Reservas - Vintage Marketing Portugal"
- Description: "Reserve o seu veículo vintage para eventos de marketing. Processo simples e rápido."
- Keywords: "reserva, aluguer veículos vintage, eventos marketing, Portugal"

**Layout Structure**:

- Maximum width container (max-w-4xl) for optimal readability
- Centered content alignment for professional appearance
- Consistent padding and spacing following design system

**Dependencies**:

- BookingForm component for complete reservation workflow
- Next.js Metadata API for SEO optimization
- Design system classes for consistent styling

### ServicosPage Component

**File**: `src/app/servicos/page.tsx`

**Purpose**: Services overview page showcasing vintage vehicle rental services for marketing events and campaigns.

**Features**:

- Comprehensive service descriptions for corporate events and photo shoots
- Multi-section layout with hero, main services, additional services, process, and CTA
- Responsive grid layouts (1-2 columns for main services, 1-3 columns for additional services)
- Process workflow visualization with numbered steps
- Call-to-action section with dual buttons (fleet and contact)
- Portuguese SEO optimization with targeted keywords

**Content Sections**:

1. **Hero Section**: Page title and value proposition
2. **Main Services**: Corporate events and photo shoots (detailed descriptions with feature lists)
3. **Additional Services**: Special events, creative consulting, and flexible rental options
4. **Process Section**: 4-step workflow (consultation, selection, booking, event)
5. **Call-to-Action**: Links to fleet page and contact form

**SEO Configuration**:

- Title: "Serviços - Vintage Marketing Portugal"
- Description: Targeted for Portuguese market with relevant keywords
- Keywords: "serviços aluguer veículos vintage, eventos corporativos Portugal, sessões fotográficas vintage"

**Styling**:

- Design system compliant with cream background (#F5F1E8)
- Section alternation: light sections (cream) and dark sections (black)
- Card-based layout for service descriptions
- Consistent typography hierarchy (hero, section, card headings)
- Icon integration with circular black backgrounds
- Hover effects on interactive elements

**Navigation Integration**:

- Links to `/frota` (fleet page) and `/contacto` (contact page)
- Consistent with site-wide navigation structure

**Dependencies**:

- Next.js `Link` component for internal navigation
- Next.js `Metadata` API for SEO optimization
- Design system classes from Tailwind configuration

### FleetPage Component

**File**: `src/app/frota/page.tsx`

**Purpose**: Display all available vintage vehicles with detailed information and booking capabilities.

**Features**:

- Real-time vehicle data fetching from Supabase
- Responsive grid layout (1 column mobile, 2 desktop with max-width container)
- Loading states with spinner animation and Portuguese messaging
- Error handling with retry functionality and Portuguese error messages
- Empty state messaging with SVG icon
- Vehicle detail modal integration
- Call-to-action section for bookings
- **Animation Integration**: Uses FadeIn, SlideIn, and StaggerContainer for smooth page transitions

**State Management**:

- `vehicles`: Array of Vehicle objects from database
- `loading`: Boolean for loading state display
- `error`: String for error message display
- `selectedVehicle`: Currently selected vehicle for modal
- `isModalOpen`: Boolean controlling modal visibility

**Data Flow**:

1. Component mounts and triggers `fetchVehicles()`
2. Supabase query fetches all vehicles ordered by creation date
3. Loading state displays spinner during fetch
4. Success: vehicles populate grid, Error: retry button shown
5. Vehicle click opens modal with detailed information

**Error Handling**:

- Supabase connection errors display Portuguese error messages
- Network errors display Portuguese error messages
- Retry functionality for failed requests
- Graceful fallback for missing vehicle data
- Configuration validation: Shows error if Supabase client is not properly configured

**Dependencies**:

- `@supabase/supabase-js` for data fetching
- Custom `VehicleCard` and `VehicleModal` components
- Next.js `useState` and `useEffect` hooks
- Animation components from `../../../components/animations` (FadeIn, SlideIn, StaggerContainer)

### ContactPage Component

**File**: `src/components/ContactPage.tsx`

**Purpose**: Complete contact page providing contact form, business information, and location details.

**Features**:

- **Contact Form**: Full-featured contact form with validation and submission to Supabase
- **Business Information**: Complete contact details, address, and operating hours
- **Google Maps Integration**: Embedded map showing business location
- **Form Validation**: Real-time validation with Portuguese error messages
- **Success States**: Confirmation message after successful form submission
- **Responsive Design**: Optimized layout with improved spacing and container structure

**Layout Structure**:

- **Container**: Maximum width container (max-w-7xl) with responsive padding (px-4 sm:px-6 lg:px-8)
- **Header Section**: Centered content with increased bottom margin (mb-16) and improved text spacing
- **Main Content**: Grid layout optimized for better content distribution
- **Form Section**: Enhanced card layout with improved padding and spacing
- **Contact Info**: Structured information cards with consistent styling

**Form Fields**:

- **Required**: Name, Email, Message
- **Optional**: Phone, Company, Subject
- All fields include proper validation and Portuguese error messaging

**State Management**:

- `formData`: ContactFormData interface with all form fields
- `errors`: FormErrors interface for field-specific error messages
- `isSubmitting`: Boolean for form submission loading state
- `isSubmitted`: Boolean for success state display

**Form Validation**:

- **Name**: Required, minimum 2 characters
- **Email**: Required, valid email format
- **Phone**: Optional, but if provided must be valid Portuguese format
- **Message**: Required, minimum 10 characters

**Data Flow**:

1. User fills out contact form with validation feedback
2. Form submission validates all fields
3. Valid data is inserted into `contact_inquiries` table with status 'new'
4. Success state displays confirmation message
5. Option to send new message resets form

**Business Information Display**:

- Email with clickable mailto link
- Phone with clickable tel link
- Physical address: Rua dos vintage, 123, 1200-001 Lisboa, Portugal
- Operating hours (Monday-Friday 9-18h, Saturday 9-13h, Sunday closed)

**Google Maps Integration**:

- Embedded iframe showing business location in Lisboa
- Responsive map container with proper aspect ratio
- Accessibility attributes and loading optimization

**Error Handling**:

- Supabase connection validation
- Form validation with Portuguese error messages
- Network error handling with user-friendly messages
- Success/error state management

**User Experience Features**:

- **Real-time Validation**: Errors clear as user types
- **Loading States**: Button shows "A enviar..." during submission
- **Success Animation**: Green checkmark and confirmation message
- **Reset Functionality**: Option to send new message after success
- **Responsive Design**: Mobile-first layout with proper spacing
- **Dark Section Styling**: Proper text contrast with white text on black backgrounds for accessibility compliance

**SEO and Accessibility**:

- Proper form labels and ARIA attributes
- Semantic HTML structure
- Portuguese content for local SEO
- Keyboard navigation support

**Dependencies**:

- `@/lib/supabase` for database operations
- `@/types` for ContactFormData interface
- `@/utils/validation` for form validation utilities
- React hooks (`useState`) for state management

## Form Components

### BookingForm Component

**File**: `src/components/forms/BookingForm.tsx`

**Purpose**: Multi-step booking form providing complete reservation workflow for vintage vehicle rentals.

**Features**:

- **Multi-step Process**: 4-step booking workflow (vehicle selection, date selection, customer details, confirmation)
- **Progress Indicator**: Visual step progress with completed step indicators
- **State Management**: Centralized form state with validation at each step
- **Step Navigation**: Forward/backward navigation with validation checks
- **Success Handling**: Completion state with option to create new booking
- **Responsive Design**: Mobile-first layout with collapsible step descriptions

**Step Workflow**:

1. **Vehicle Selection**: Choose from available vintage vehicles
2. **Date Selection**: Pick rental dates with availability checking
3. **Customer Details**: Enter contact information and special requirements
4. **Confirmation**: Review booking details and submit reservation

**State Management**:

- `currentStep`: Current step in the booking process (`BookingStep` type)
- `formData`: Accumulated form data across all steps (`BookingFormState` interface)
- `isSubmitting`: Loading state during form submission
- `isCompleted`: Success state after booking submission
- `currentStepIndex`: Index of current step in steps array
- `isFirstStep`: Boolean indicating if currently on first step

**Props Interface**:

```typescript
export type BookingStep = 'vehicle' | 'dates' | 'details' | 'confirmation';

export interface BookingFormState extends Partial<BookingFormData> {
  selectedVehicle?: Vehicle;
}
```

**Step Validation**:

- **Vehicle Step**: Requires `vehicle_id` and `selectedVehicle`
- **Dates Step**: Requires `start_date` and `end_date`
- **Details Step**: Requires `customer_name` and `customer_email`
- **Confirmation Step**: Always valid (review step)

**User Experience Features**:

- **Progress Visualization**: Numbered circles with checkmarks for completed steps
- **Step Descriptions**: Contextual descriptions for each step (hidden on mobile)
- **Navigation Controls**: Previous/Next buttons with proper disabled states
- **Success Animation**: Completion state with green checkmark icon
- **Reset Functionality**: "Fazer Nova Reserva" button to restart process

**Styling**:

- Design system compliant with cream card background (#f5f1e8ff) and consistent accents
- Consistent button styling (black primary, disabled states)
- Responsive grid layout for progress indicators
- Smooth transitions for step changes

**Dependencies**:

- `@heroicons/react` for UI icons (chevrons, checkmark)
- Sub-components: `VehicleSelection`, `DateSelection`, `CustomerDetails`, `BookingConfirmation`
- Custom types from `@/types` for form data interfaces

### VehicleSelection Component

**File**: `src/components/forms/VehicleSelection.tsx`

**Purpose**: First step of booking form allowing users to select from available vintage vehicles.

**Features**:

- Real-time vehicle data fetching from Supabase
- Vehicle grid display with images and key details
- Selection state management with visual feedback
- Loading and error states with Portuguese messaging
- Integration with parent BookingForm state

**Props Interface**:

```typescript
interface VehicleSelectionProps {
  selectedVehicle?: Vehicle;
  onVehicleSelect: (data: {
    vehicle_id: string;
    selectedVehicle: Vehicle;
  }) => void;
}
```

### DateSelection Component

**File**: `src/components/forms/DateSelection.tsx`

**Purpose**: Second step of booking form for selecting rental dates with availability checking.

**Features**:

- Custom date picker with availability validation
- Conflict detection with existing bookings
- Date range selection (start and end dates)
- Portuguese date formatting and error messages
- Integration with vehicle availability data

**Props Interface**:

```typescript
interface DateSelectionProps {
  vehicleId: string;
  selectedDates: { start_date?: string; end_date?: string };
  onDatesSelect: (data: { start_date: string; end_date: string }) => void;
}
```

### CustomerDetails Component

**File**: `src/components/forms/CustomerDetails.tsx`

**Purpose**: Third step of booking form for collecting customer contact information and special requirements.

**Features**:

- Form validation with Portuguese error messages
- Required fields: name and email
- Optional fields: phone and special notes
- Real-time validation feedback
- Accessibility-compliant form inputs

**Props Interface**:

```typescript
interface CustomerDetailsProps {
  customerData: {
    customer_name?: string;
    customer_email?: string;
    customer_phone?: string;
    notes?: string;
  };
  onDetailsSubmit: (data: CustomerDetailsData) => void;
}
```

### BookingConfirmation Component

**File**: `src/components/forms/BookingConfirmation.tsx`

**Purpose**: Final step of booking form for reviewing and confirming reservation details.

**Features**:

- Complete booking summary display
- Vehicle details with image
- Date range and duration calculation
- Customer information review
- Final submission with loading state
- Integration with Supabase for booking creation

**Props Interface**:

```typescript
interface BookingConfirmationProps {
  bookingData: BookingFormData & { selectedVehicle: Vehicle };
  onConfirm: () => Promise<void>;
  isSubmitting: boolean;
}
```

## Shared Components

### VehicleCard Component

**File**: `src/components/VehicleCard.tsx`

**Purpose**: Reusable card component for displaying vehicle information in grid layouts.

**Props Interface**:

```typescript
interface VehicleCardProps {
  vehicle: Vehicle;
  onClick: () => void;
}
```

**Features**:

- Next.js Image optimization with responsive sizing
- Price formatting with fallback text ("Preço sob consulta")
- Year badge overlay on vehicle image
- Description truncation using CSS line-clamp
- Hover animations and transform effects
- Direct booking link with vehicle ID parameter
- Fallback placeholder for missing images
- Support for multiple image sources (Supabase Storage, Unsplash)

**Interactive Elements**:

- **Clickable Image**: Vehicle image triggers onClick handler for modal display
- **Clickable Title**: Vehicle name triggers onClick handler with hover color transition
- **Details Button**: Interactive button element (not span) with hover effects and proper cursor styling
- **Booking Button**: Direct navigation to booking page with vehicle ID parameter

**Styling**:

- Card shadow effects with hover enhancement (`hover:shadow-xl`)
- Card lift animation (`hover:-translate-y-1`)
- Image scale effect on hover (`hover:scale-105`)
- Vintage color scheme integration
- Responsive image sizing with aspect ratio preservation
- Smooth transitions for all interactive elements (200ms duration)

**User Experience Improvements**:

- Multiple click targets for opening vehicle details (image, title, details button)
- Clear visual feedback on hover states
- Proper button semantics for screen readers
- Consistent interaction patterns across the component

**Accessibility**:

- Proper alt text for vehicle images
- Semantic button elements for interactive actions
- Keyboard navigation support
- Screen reader friendly content structure
- ARIA-compliant interactive elements

### FeaturedVehicles Component

**File**: `src/components/FeaturedVehicles.tsx`

**Purpose**: Homepage section displaying featured vehicles from the database with modal integration.

**Features**:

- Real-time vehicle data fetching from Supabase (limited to 2 vehicles for homepage)
- Loading states with spinner animation and Portuguese loading text
- Empty state handling with call-to-action to contact page
- Integration with VehicleCard and VehicleModal components
- Responsive grid layout for featured vehicles
- Link to full fleet page ("Ver Toda a Frota")
- **Design System Integration**: Updated to use consistent design system styling
- **Consistent Headings**: Standardized section heading "Os Nossos Veículos" across all states

**State Management**:

- `vehicles`: Array of Vehicle objects (limited to first 2 from database)
- `loading`: Boolean for loading state display
- `selectedVehicle`: Currently selected vehicle for modal display
- `isModalOpen`: Boolean controlling modal visibility

**Data Flow**:

1. Component mounts and triggers `fetchVehicles()`
2. Supabase query: `SELECT * FROM vehicles ORDER BY created_at ASC LIMIT 2`
3. Loading state displays spinner with Portuguese text
4. Success: vehicles populate grid, Error: console logging
5. Vehicle click opens modal with detailed information

**Design System Styling**:

- **Section Layout**: Uses `section-light` class for consistent cream background (#F5F1E8)
- **Container**: Standardized container with `mx-auto px-8` for consistent spacing
- **Typography**:
  - Headings use `text-section font-section text-primary-black` for consistent section titles
  - Body text uses `text-body text-primary-mediumGray leading-body` for optimal readability
- **Loading States**:
  - Spinner uses `border-primary-black` for brand consistency
  - Loading text uses `text-primary-mediumGray` for secondary text styling
- **Responsive Design**: Mobile-first approach with consistent breakpoints

**Error Handling**:

- Supabase connection validation with early return
- Console error logging for debugging
- Graceful fallback for empty vehicle data
- Portuguese empty state messaging: "Estamos a preparar a nossa frota. Volte em breve para ver os nossos veículos."

**User Experience**:

- Consistent design system styling across all states
- Smooth loading transitions
- Clear call-to-action buttons
- Mobile-responsive design with improved spacing

**Dependencies**:

- `@supabase/supabase-js` for data fetching
- Custom `VehicleCard` and `VehicleModal` components
- Next.js `Link` component for navigation
- React hooks (`useState`, `useEffect`)
- Design system classes from Tailwind configuration

### VehicleModal Component

**File**: `src/components/VehicleModal.tsx`

**Purpose**: Modal overlay displaying detailed vehicle information with booking capabilities.

**Props Interface**:

```typescript
interface VehicleModalProps {
  vehicle: Vehicle | null;
  isOpen: boolean;
  onClose: () => void;
}
```

**Features**:

- Headless UI Dialog component for accessibility compliance
- Responsive grid layout (image left, details right on desktop)
- Smooth enter/exit animations using Framer Motion principles
- Vehicle specifications and feature list display
- Dual action buttons (booking and contact)
- Contact information section with help text
- Close button with proper ARIA labels and design system styling

**Layout Structure**:

- Left panel: Vehicle image with year badge
- Right panel: Title, price, description, specifications, features, actions
- Mobile: Stacked layout with image on top

**Design System Integration**:

- **Modal Panel**: Updated to use design system card styling with `rounded-xl` border radius and `shadow-[0_8px_24px_rgba(0,0,0,0.12)]` shadow
- **Card Background**: Uses design system `cardBackground` color (#f5f1e8ff - cream with alpha) for consistent card styling across components
- **Close Button**: Enhanced with design system colors (`text-primary-black`, `hover:text-primary-darkGray`) and improved styling (`bg-white/90`, `shadow-sm`)
- **Transitions**: Improved transition timing (`transition-all duration-300`) for consistent animation feel
- **Color Palette**: Migrated from vintage-specific colors to primary design system colors for consistency

**Styling Updates**:

- Modal container uses consistent card shadow from design system
- Close button follows primary color scheme with proper hover states
- Enhanced backdrop styling with improved opacity and blur effects
- Consistent border radius matching other card components

**Interaction Patterns**:

- Click outside or ESC key to close modal
- Primary action: "Reservar Este Veículo" redirects to booking page
- Secondary action: "Mais Informações" links to contact page
- Smooth backdrop blur and scale animations
- Enhanced close button with improved hover feedback

**Dependencies**:

- `@headlessui/react` for accessible modal implementation
- Next.js Image component for optimized image display
- Custom Vehicle type definitions from TypeScript interfaces
- Design system color tokens from Tailwind configuration

## Animation Components

### Animation System Overview

The animation system represents a sophisticated, production-ready ecosystem built on Framer Motion that exceeds industry standards. This comprehensive system provides:

**Core Features**:

- **Modular Architecture**: 25+ animation components organized by category
- **Performance Excellence**: Hardware-accelerated animations with device adaptation
- **Accessibility Leadership**: Comprehensive `prefers-reduced-motion` support
- **Advanced Effects**: Card-stacking, parallax, magnetic interactions, and micro-animations
- **Next.js Integration**: Seamless App Router integration with event-driven state management

**Component Categories**:

1. **Basic Animations**: FadeIn, SlideIn, ScaleIn, StaggerContainer
2. **Page-Level**: AnimatedPageWrapper, PageTransition, LoadingAnimation
3. **Scroll-Based**: ScrollReveal, TextReveal, CardStack, StaggeredCard, IndependentSection
4. **Micro-Interactions**: MagneticButton, RippleButton, GlowEffect, CounterAnimation

**Common Animation Properties**:

- **Easing Curve**: Custom cubic-bezier `[0.25, 0.46, 0.45, 0.94]` for natural, smooth motion
- **Performance**: GPU-accelerated transforms using `opacity`, `x`, `y`, and `scale` properties
- **Accessibility**: Respects user's motion preferences via `prefers-reduced-motion`
- **TypeScript**: Full type safety with proper interfaces for all props

### FadeIn Component

**File**: `components/animations/FadeIn.tsx`

**Purpose**: Smooth fade-in animation with vertical slide effect for content reveals.

**Props Interface**:

```typescript
interface FadeInProps {
  children: ReactNode;
  delay?: number; // Animation delay in seconds (default: 0)
  duration?: number; // Animation duration in seconds (default: 0.6)
  className?: string; // Optional CSS classes for styling
}
```

**Animation Properties**:

- **Initial**: `opacity: 0, y: 20` (invisible, 20px below final position)
- **Final**: `opacity: 1, y: 0` (fully visible, in final position)
- **Duration**: 0.6 seconds (default)
- **Easing**: Custom cubic-bezier for natural motion

### SlideIn Component

**File**: `components/animations/SlideIn.tsx`

**Purpose**: Directional slide-in animations with fade effect for dynamic content entry.

**Props Interface**:

```typescript
interface SlideInProps {
  children: ReactNode;
  direction?: 'left' | 'right' | 'up' | 'down'; // Slide direction (default: 'left')
  delay?: number; // Animation delay in seconds (default: 0)
  duration?: number; // Animation duration in seconds (default: 0.6)
  className?: string; // Optional CSS classes
}
```

**Animation Properties**:

- **Directions**: 50px offset in specified direction with opacity fade
- **Initial States**:
  - `left`: `x: -50, opacity: 0`
  - `right`: `x: 50, opacity: 0`
  - `up`: `y: -50, opacity: 0`
  - `down`: `y: 50, opacity: 0`
- **Final**: `x: 0, y: 0, opacity: 1`

### ScaleIn Component

**File**: `components/animations/ScaleIn.tsx`

**Purpose**: Scale-up animation with fade effect for emphasis and attention-drawing elements.

**Props Interface**:

```typescript
interface ScaleInProps {
  children: ReactNode;
  delay?: number; // Animation delay in seconds (default: 0)
  duration?: number; // Animation duration in seconds (default: 0.5)
  className?: string; // Optional CSS classes
}
```

**Animation Properties**:

- **Initial**: `scale: 0.8, opacity: 0` (80% size, invisible)
- **Final**: `scale: 1, opacity: 1` (full size, fully visible)
- **Duration**: 0.5 seconds (default, slightly faster than other animations)

### StaggerContainer Component

**File**: `components/animations/StaggerContainer.tsx`

**Purpose**: Container component that animates children with staggered timing for sequential reveals.

**Props Interface**:

```typescript
interface StaggerContainerProps {
  children: ReactNode;
  staggerDelay?: number; // Delay between child animations (default: 0.1s)
  className?: string; // Optional CSS classes for container
}
```

**Animation Properties**:

- **Container**: Fades in with staggered children animation
- **Children**: Each child animates with `y: 20` to `y: 0` and opacity fade
- **Stagger Timing**: 0.1 second delay between each child (configurable)
- **Initial Delay**: 0.2 seconds before first child starts
- **Child Duration**: 0.6 seconds per child animation

**Usage Pattern**:

```typescript
<StaggerContainer className="grid grid-cols-2 gap-4" staggerDelay={0.15}>
  {items.map((item) => (
    <ItemCard key={item.id} item={item} />
  ))}
</StaggerContainer>
```

### HoverCard Component

**File**: `components/animations/HoverCard.tsx`

**Purpose**: Interactive hover animations for cards and clickable elements.

**Props Interface**:

```typescript
interface HoverCardProps {
  children: ReactNode;
  className?: string;
  hoverScale?: number; // Scale factor on hover (default: 1.02)
  hoverY?: number; // Vertical lift on hover (default: -5px)
}
```

**Animation Properties**:

- **Hover**: Scale and lift effect with smooth transition
- **Tap**: Brief scale-down effect (0.98) for tactile feedback
- **Transition**: 0.2 seconds with `easeOut` timing
- **Default Effects**: 2% scale increase, 5px upward lift

### Scroll Animation System

The scroll animation system provides sophisticated, performance-optimized scroll-based interactions:

#### ScrollReveal Component

**Purpose**: Viewport-triggered reveal animations with configurable thresholds.

```typescript
<ScrollReveal threshold={0.1} delay={0.2}>
  <ContentSection />
</ScrollReveal>
```

**Features**:

- Configurable trigger thresholds (0.1 = 10% visible)
- Combines opacity, Y-transform, and scale for rich effects
- Uses Framer Motion's optimized `useScroll` and `useTransform`

#### CardStack Component

**Purpose**: Sophisticated card-stacking/folding effects during scroll.

```typescript
{cards.map((card, index) => (
  <CardStack key={card.id} index={index} total={cards.length}>
    <CardContent card={card} />
  </CardStack>
))}
```

**Mathematical Implementation**:

- Calculates stacking progress: `(index + 1) / total`
- Y-transform: `[0, -100 * index]` for layered stacking
- Scale reduction: `[1, 0.95 - (index * 0.05)]` for depth perception
- Opacity fade: `[1, 0.8 - (index * 0.1)]` for visual hierarchy

#### TextReveal Component

**Purpose**: Text-specific reveal animations with staggering support.

```typescript
<TextReveal delay={0.2} stagger={true}>
  <h1>Animated Heading</h1>
</TextReveal>
```

### Micro-Interactions System

**Features**:

- Layered animation with background pulse and interactive foreground
- Infinite loop with `easeInOut` timing
- Hover and tap states for user feedback

#### MagneticButton Component

**Purpose**: Real-time mouse tracking with magnetic attraction effect.

```typescript
<MagneticButton strength={0.15}>
  <ButtonContent />
</MagneticButton>
```

**Implementation**:

- Real-time mouse position calculation
- Configurable magnetic strength (0.15 = 15% attraction)
- Smooth return to center on mouse leave

#### CounterAnimation Component

**Purpose**: Smooth number counting with custom easing.

```typescript
<CounterAnimation from={0} to={100} duration={2} />
```

**Features**:

- RequestAnimationFrame for 60fps performance
- Custom cubic ease-out: `1 - Math.pow(1 - progress, 3)`
- Automatic cleanup when animation completes

### Performance Optimization System

#### Device Capability Detection

```typescript
const { shouldOptimize, prefersReducedMotion, animationConfig } =
  useAnimationOptimization();

// Automatic adaptation based on:
// - Hardware cores (navigator.hardwareConcurrency)
// - Network speed (navigator.connection.effectiveType)
// - User preferences (prefers-reduced-motion)
```

#### Responsive Animation Scaling

```typescript
const config = getResponsiveAnimationConfig();
// Returns:
// - Mobile: Simple animations, reduced distances
// - Tablet: Medium complexity, proportional scaling
// - Desktop: Full animation complexity
```

#### Performance Monitoring

```typescript
const performance = await testAnimationPerformance(animationFunction, 2000);
// Returns: { averageFPS, minFPS, maxFPS }
```

### Animation System Integration

**Export Structure** (`components/animations/index.ts`):

```typescript
// Basic animation components
export { default as FadeIn } from './FadeIn';
export { default as SlideIn } from './SlideIn';
export { default as ScaleIn } from './ScaleIn';
export { default as StaggerContainer } from './StaggerContainer';

// Page wrapper components
export {
  default as AnimatedPageWrapper,
  AnimatedItem,
  AnimatedSection,
} from './AnimatedPageWrapper';

// Scroll-based animations
export {
  ScrollReveal,
  TextReveal,
  CardStack,
  StaggeredCard,
  IndependentSection,
} from './ScrollAnimations';

// Page transitions
export { default as PageTransition, LoadingAnimation } from './PageTransition';

// Micro-interactions
export {
  MagneticButton,
  RippleButton,
  GlowEffect,
  CounterAnimation,
} from './MicroInteractions';
```

**Import Patterns**:

The animation components are located in the root `components/animations/` directory and should be imported using relative paths from page components:

```typescript
// From pages in src/app/ directory
import {
  FadeIn,
  SlideIn,
  StaggerContainer,
} from '../../../components/animations';

// From components in src/components/ directory
import { FadeIn, SlideIn, StaggerContainer } from '../../components/animations';
```

**Dependencies**:

- `framer-motion` for all animation functionality
- React `ReactNode` type for children props
- All components use `'use client'` directive for client-side rendering

```typescript
export { default as FadeIn } from './FadeIn';
export { default as SlideIn } from './SlideIn';
export { default as ScaleIn } from './ScaleIn';
export { default as StaggerContainer } from './StaggerContainer';
export { default as HoverCard } from './HoverCard';
```

**Usage Examples**:

```typescript
// Page section animations
<FadeIn>
  <h1>Welcome to Vintage Marketing</h1>
</FadeIn>

// Directional content reveals
<SlideIn direction="up" delay={0.2}>
  <CallToActionSection />
</SlideIn>

// Staggered grid animations
<StaggerContainer className="vehicle-grid">
  {vehicles.map((vehicle) => (
    <VehicleCard key={vehicle.id} vehicle={vehicle} />
  ))}
</StaggerContainer>

// Interactive hover effects
<HoverCard hoverScale={1.05} hoverY={-8}>
  <ProductCard product={product} />
</HoverCard>
```

**Performance Considerations**:

- All animations use GPU-accelerated properties (`transform`, `opacity`)
- Framer Motion automatically optimizes for 60fps performance
- Components are client-side only (`'use client'` directive)
- Minimal DOM impact with single wrapper elements
- Efficient re-render patterns with proper dependency management

**Dependencies**:

- `framer-motion` for animation engine
- React `ReactNode` type for children props
- Next.js client-side component compatibility

## Custom Hooks

## Component Architecture Patterns

### File Organization

```
components/
├── animations/      # Complete animation system (Framer Motion)
│   ├── index.ts     # Barrel exports for all animation components
│   ├── FadeIn.tsx   # Fade-in animation component ✅ IMPLEMENTED
│   ├── SlideIn.tsx  # Directional slide animations ✅ IMPLEMENTED
│   ├── ScaleIn.tsx  # Scale-up animations ✅ IMPLEMENTED
│   ├── StaggerContainer.tsx # Staggered child animations ✅ IMPLEMENTED
│   └── HoverCard.tsx # Interactive hover animations ✅ IMPLEMENTED

src/components/
├── layout/          # Layout components (Header, Footer)
├── ui/             # Reusable UI components
├── forms/          # Form components
└── admin/          # Admin-specific components

src/hooks/
├── useParallax.ts   # Parallax scroll effects
└── useBookingForm.ts # Booking form state management
```

### Styling Conventions

- **Design System Integration**: Use structured design tokens from `tailwind.config.js`
- **Color Palette**: Semantic color naming (primary, background, accent) for consistent theming
- **Typography Scale**: Responsive font sizing with clamp() values (hero, section, card, nav)
- **Component Tokens**: Consistent border radius, shadows, and spacing from design system
- **Mobile-First**: Responsive design approach with consistent breakpoints
- **Font System**:
  - Open Sans for headings (`font-heading` class)
  - Inter for body text (default)
  - Limelight for logo and brand elements (`font-logo` class)
- **Font Loading**: Next.js Google Fonts optimization with variable font support and CSS custom properties

### State Management

- Local component state for UI interactions
- Context providers for shared state (planned)
- Supabase client for data fetching and mutations

### TypeScript Integration

- All components use TypeScript with proper type definitions
- Props interfaces defined for reusable components
- Integration with auto-generated Supabase types
