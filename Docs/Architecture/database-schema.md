# Database Schema: Vintage Marketing Portugal

## Implementation Status

✅ **IMPLEMENTED** - All tables, functions, and RLS policies are deployed and active in Supabase.

## Table: vehicles

```sql
CREATE TABLE vehicles (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  name text NOT NULL,
  year integer NOT NULL,
  photo_url text, -- Supabase Storage public URL
  description text,
  price numeric,
  availability jsonb DEFAULT '{}', -- e.g., {"2025-07-20": true, "2025-07-21": false}
  created_at timestamp with time zone DEFAULT now(),
  updated_at timestamp with time zone DEFAULT now()
);

-- Auto-update trigger for updated_at
CREATE TRIGGER update_vehicles_updated_at BEFORE UPDATE ON vehicles
FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
```

## Table: bookings

```sql
CREATE TABLE bookings (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  vehicle_id uuid REFERENCES vehicles(id) ON DELETE CASCADE,
  customer_name text NOT NULL,
  customer_email text NOT NULL,
  customer_phone text,
  start_date date NOT NULL,
  end_date date NOT NULL,
  status text NOT NULL DEFAULT 'pending' CHECK (status IN ('pending', 'confirmed', 'cancelled', 'completed')),
  notes text,
  total_price numeric,
  created_at timestamp with time zone DEFAULT now(),
  updated_at timestamp with time zone DEFAULT now()
);

-- Auto-update trigger for updated_at
CREATE TRIGGER update_bookings_updated_at BEFORE UPDATE ON bookings
FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
```

## Table: contact_inquiries

```sql
CREATE TABLE contact_inquiries (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  name text NOT NULL,
  email text NOT NULL,
  phone text,
  company text,
  subject text,
  message text NOT NULL,
  status text NOT NULL DEFAULT 'new' CHECK (status IN ('new', 'in_progress', 'resolved', 'closed')),
  created_at timestamp with time zone DEFAULT now(),
  updated_at timestamp with time zone DEFAULT now()
);

-- Auto-update trigger for updated_at
CREATE TRIGGER update_contact_inquiries_updated_at BEFORE UPDATE ON contact_inquiries
FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
```

## Table: email_templates

```sql
CREATE TABLE email_templates (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  name text NOT NULL UNIQUE,
  subject text NOT NULL,
  body text NOT NULL,
  template_type text NOT NULL CHECK (template_type IN ('booking_confirmation', 'booking_cancellation', 'contact_response', 'admin_notification')),
  is_active boolean DEFAULT true,
  created_at timestamp with time zone DEFAULT now(),
  updated_at timestamp with time zone DEFAULT now()
);

-- Auto-update trigger for updated_at
CREATE TRIGGER update_email_templates_updated_at BEFORE UPDATE ON email_templates
FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
```

## Database Functions

```sql
-- Function to automatically update updated_at column
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = now();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Function to check if user is admin (IMPLEMENTED)
CREATE OR REPLACE FUNCTION is_admin()
RETURNS boolean AS $$
DECLARE
  jwt_claims jsonb;
BEGIN
  -- Get the JWT claims
  jwt_claims := auth.jwt();

  -- If no JWT (unauthenticated user), return false
  IF jwt_claims IS NULL THEN
    RETURN false;
  END IF;

  -- Check if user has admin role
  RETURN (
    jwt_claims ->> 'role' = 'admin' OR
    (jwt_claims -> 'user_metadata' ->> 'role') = 'admin'
  );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;
```

## Row Level Security (RLS) Policies

All tables have RLS enabled with the following access patterns:

### Vehicles Table

- **Public users**: SELECT (read) access only
- **Admin users**: Full CRUD access (INSERT, UPDATE, DELETE)

```sql
-- RLS Policies for vehicles
CREATE POLICY "Public can view vehicles" ON vehicles
  FOR SELECT USING (true);

CREATE POLICY "Only admins can insert vehicles" ON vehicles
  FOR INSERT WITH CHECK (is_admin());

CREATE POLICY "Only admins can update vehicles" ON vehicles
  FOR UPDATE USING (is_admin());

CREATE POLICY "Only admins can delete vehicles" ON vehicles
  FOR DELETE USING (is_admin());
```

### Bookings Table

- **Public users**: INSERT (create bookings) access
- **Admin users**: Full CRUD access (view, update, delete all bookings)

```sql
-- RLS Policies for bookings
CREATE POLICY "Public can create bookings" ON bookings
  FOR INSERT WITH CHECK (true);

CREATE POLICY "Admins can view all bookings" ON bookings
  FOR SELECT USING (is_admin());

CREATE POLICY "Only admins can update bookings" ON bookings
  FOR UPDATE USING (is_admin());

CREATE POLICY "Only admins can delete bookings" ON bookings
  FOR DELETE USING (is_admin());
```

### Contact Inquiries Table

- **Public users**: INSERT (create inquiries) access
- **Admin users**: Full CRUD access (view, update, delete all inquiries)

```sql
-- RLS Policies for contact_inquiries
CREATE POLICY "Public can create contact inquiries" ON contact_inquiries
  FOR INSERT WITH CHECK (true);

CREATE POLICY "Only admins can view contact inquiries" ON contact_inquiries
  FOR SELECT USING (is_admin());

CREATE POLICY "Only admins can update contact inquiries" ON contact_inquiries
  FOR UPDATE USING (is_admin());

CREATE POLICY "Only admins can delete contact inquiries" ON contact_inquiries
  FOR DELETE USING (is_admin());
```

### Email Templates Table

- **Admin users only**: Full CRUD access
- **Public users**: No access

```sql
-- RLS Policies for email_templates
CREATE POLICY "Only admins can access email templates" ON email_templates
  FOR ALL USING (is_admin());
```

## TypeScript Integration

The database schema is fully typed using Supabase's auto-generated TypeScript types. Key type exports from `src/types/index.ts`:

- `Vehicle`, `VehicleInsert`, `VehicleUpdate`
- `Booking`, `BookingInsert`, `BookingUpdate`
- `ContactInquiry`, `ContactInquiryInsert`, `ContactInquiryUpdate`
- `EmailTemplate`, `EmailTemplateInsert`, `EmailTemplateUpdate`

## Status Enums

- **BookingStatus**: `'pendente' | 'confirmado' | 'cancelado' | 'completo'` (Portuguese values)
- **InquiryStatus**: `'new' | 'in_progress' | 'resolved' | 'closed'` (Admin-facing, English values)
- **EmailTemplateType**: `'booking_confirmation' | 'booking_cancellation' | 'contact_response' | 'admin_notification'`

## Portuguese Localization

### Database Content

- **Booking Status**: Uses Portuguese values for user-facing status display
- **Vehicle Names**: Stored in Portuguese (e.g., "Carrinha Fleur de Lys", "BMW R100 RS Clássica")
- **Vehicle Descriptions**: Full Portuguese descriptions with local terminology
- **Contact Inquiries**: All customer-submitted content in Portuguese

### Frontend Integration

- Status mapping between database values and display text
- Portuguese error messages for all form validations
- Localized date formatting using Portuguese locale
- Currency formatting in Euros with Portuguese conventions

## Migration History

The following migrations have been applied to the database:

1. `create_vehicles_table` - Creates vehicles table with triggers
2. `create_bookings_table` - Creates bookings table with foreign key constraints
3. `create_contact_inquiries_table` - Creates contact inquiries table
4. `create_email_templates_table` - Creates email templates table
5. `setup_rls_policies` - Enables RLS and creates all security policies
6. `fix_is_admin_function` - Fixes null JWT handling in admin function
7. `add_rls_documentation` - Adds documentation comments

## Security Notes

- **RLS Enforcement**: All tables have Row Level Security enabled
- **Admin Detection**: Uses JWT claims to identify admin users
- **Superuser Bypass**: Database administrators bypass RLS (expected behavior)
- **Frontend Security**: Client applications use anonymous keys and are subject to RLS policies
- **Data Validation**: CHECK constraints ensure valid status values
- **Referential Integrity**: Foreign key constraints maintain data consistency

## Notes

- All tables include `created_at` and `updated_at` timestamps with automatic updates
- Store only Supabase Storage public URLs in `photo_url` fields
- Database types are auto-generated by Supabase and kept in sync with `src/types/index.ts`
- See [Context7: /supabase/supabase] for Supabase Storage integration and best practices
