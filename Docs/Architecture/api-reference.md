# API Reference: Supabase Database Integration

## Overview

This document provides a comprehensive reference for interacting with the Supabase database, including TypeScript types, RLS policies, and usage examples.

## Database Client Setup

```typescript
// src/lib/supabase.ts
import { createClient } from '@supabase/supabase-js';
import { Database } from '@/types';

const supabaseUrl =
  process.env.NEXT_PUBLIC_SUPABASE_URL || 'https://placeholder.supabase.co';
const supabaseAnonKey =
  process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || 'placeholder-key';

// Check if environment variables are properly configured
const isConfigured =
  process.env.NEXT_PUBLIC_SUPABASE_URL &&
  process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;

export const supabase = isConfigured
  ? createClient<Database>(supabaseUrl, supabaseAnonKey)
  : null;
```

### Environment Variable Validation

The Supabase client now includes built-in validation to prevent runtime errors when environment variables are missing:

- **Development Safety**: Returns `null` instead of throwing errors when environment variables are not configured
- **Graceful Degradation**: Components can check for `supabase` availability before making API calls
- **Clear Error Messages**: Provides meaningful feedback when configuration is incomplete

## Type Definitions

### Core Database Types

```typescript
// Auto-generated from Supabase schema
export type Vehicle = Database['public']['Tables']['vehicles']['Row'];
export type VehicleInsert = Database['public']['Tables']['vehicles']['Insert'];
export type VehicleUpdate = Database['public']['Tables']['vehicles']['Update'];

export type Booking = Database['public']['Tables']['bookings']['Row'];
export type BookingInsert = Database['public']['Tables']['bookings']['Insert'];
export type BookingUpdate = Database['public']['Tables']['bookings']['Update'];

export type ContactInquiry =
  Database['public']['Tables']['contact_inquiries']['Row'];
export type ContactInquiryInsert =
  Database['public']['Tables']['contact_inquiries']['Insert'];
export type ContactInquiryUpdate =
  Database['public']['Tables']['contact_inquiries']['Update'];

export type EmailTemplate =
  Database['public']['Tables']['email_templates']['Row'];
export type EmailTemplateInsert =
  Database['public']['Tables']['email_templates']['Insert'];
export type EmailTemplateUpdate =
  Database['public']['Tables']['email_templates']['Update'];
```

### Status Enums

```typescript
// Portuguese status values for bookings
export type BookingStatus =
  | 'pendente'
  | 'confirmado'
  | 'cancelado'
  | 'completo';
export type InquiryStatus = 'new' | 'in_progress' | 'resolved' | 'closed';
export type EmailTemplateType =
  | 'booking_confirmation'
  | 'booking_cancellation'
  | 'contact_response'
  | 'admin_notification';
```

### Form Interfaces

```typescript
export interface BookingFormData {
  vehicle_id: string;
  customer_name: string;
  customer_email: string;
  customer_phone?: string;
  start_date: string;
  end_date: string;
  notes?: string;
}

export interface ContactFormData {
  name: string;
  email: string;
  phone?: string;
  company?: string;
  subject?: string;
  message: string;
}
```

## API Operations

### Vehicles

#### Public Operations (No Authentication Required)

```typescript
// Get all vehicles
const { data: vehicles, error } = await supabase
  .from('vehicles')
  .select('*')
  .order('year', { ascending: false });

// Get single vehicle
const { data: vehicle, error } = await supabase
  .from('vehicles')
  .select('*')
  .eq('id', vehicleId)
  .single();

// Get available vehicles for date range
const { data: vehicles, error } = await supabase
  .from('vehicles')
  .select('*')
  .not('availability', 'cs', `{"${startDate}": false, "${endDate}": false}`);
```

#### Admin Operations (Authentication Required)

```typescript
// Create vehicle (Admin only)
const { data: vehicle, error } = await supabase
  .from('vehicles')
  .insert({
    name: 'BMW R100 RS',
    year: 1978,
    description: 'Classic BMW motorcycle',
    price: 150.0,
    photo_url: 'https://supabase-storage-url/vehicle.jpg',
  })
  .select()
  .single();

// Update vehicle (Admin only)
const { data: vehicle, error } = await supabase
  .from('vehicles')
  .update({ price: 175.0 })
  .eq('id', vehicleId)
  .select()
  .single();

// Delete vehicle (Admin only)
const { error } = await supabase.from('vehicles').delete().eq('id', vehicleId);
```

### Bookings

#### Public Operations

```typescript
// Create booking (Public)
const { data: booking, error } = await supabase
  .from('bookings')
  .insert({
    vehicle_id: vehicleId,
    customer_name: 'João Silva',
    customer_email: '<EMAIL>',
    customer_phone: '+*********** 678',
    start_date: '2025-08-01',
    end_date: '2025-08-03',
    notes: 'Para evento de marketing',
  })
  .select()
  .single();
```

#### Admin Operations

```typescript
// Get all bookings (Admin only)
const { data: bookings, error } = await supabase
  .from('bookings')
  .select(
    `
    *,
    vehicles (
      name,
      year,
      photo_url
    )
  `
  )
  .order('created_at', { ascending: false });

// Update booking status (Admin only)
const { data: booking, error } = await supabase
  .from('bookings')
  .update({ status: 'confirmed' })
  .eq('id', bookingId)
  .select()
  .single();

// Get bookings by status (Admin only)
const { data: bookings, error } = await supabase
  .from('bookings')
  .select('*')
  .eq('status', 'pending');
```

### Contact Inquiries

#### Public Operations

```typescript
// Create contact inquiry (Public)
const { data: inquiry, error } = await supabase
  .from('contact_inquiries')
  .insert({
    name: 'Maria Santos',
    email: '<EMAIL>',
    phone: '+*********** 678',
    company: 'Marketing Empresa Lda',
    subject: 'Aluguer para evento',
    message:
      'Gostaria de alugar uma viatura vintage para o nosso próximo evento.',
  })
  .select()
  .single();
```

#### Admin Operations

```typescript
// Get all inquiries (Admin only)
const { data: inquiries, error } = await supabase
  .from('contact_inquiries')
  .select('*')
  .order('created_at', { ascending: false });

// Update inquiry status (Admin only)
const { data: inquiry, error } = await supabase
  .from('contact_inquiries')
  .update({ status: 'in_progress' })
  .eq('id', inquiryId)
  .select()
  .single();
```

### Email Templates

#### Admin Operations Only

```typescript
// Get all email templates (Admin only)
const { data: templates, error } = await supabase
  .from('email_templates')
  .select('*')
  .eq('is_active', true);

// Create email template (Admin only)
const { data: template, error } = await supabase
  .from('email_templates')
  .insert({
    name: 'Booking Confirmation PT',
    subject: 'Confirmação de Reserva - {{vehicle_name}}',
    body: 'Olá {{customer_name}}, a sua reserva foi confirmada...',
    template_type: 'booking_confirmation',
  })
  .select()
  .single();

// Update email template (Admin only)
const { data: template, error } = await supabase
  .from('email_templates')
  .update({
    subject: 'Nova Confirmação - {{vehicle_name}}',
    body: 'Caro {{customer_name}}, confirmamos a sua reserva...',
  })
  .eq('id', templateId)
  .select()
  .single();
```

## Row Level Security (RLS) Behavior

### Public Users (Unauthenticated)

- ✅ **Can read** vehicles
- ✅ **Can create** bookings and contact inquiries
- ❌ **Cannot read** bookings, contact inquiries, or email templates
- ❌ **Cannot modify** any data except creating bookings/inquiries

### Admin Users (Authenticated with admin role)

- ✅ **Full access** to all tables and operations
- ✅ **Can manage** vehicles, bookings, inquiries, and email templates
- ✅ **Can view** all data across all tables

### Authentication Context

```typescript
// Check if user is admin (for UI logic)
const {
  data: { user },
} = await supabase.auth.getUser();
const isAdmin =
  user?.user_metadata?.role === 'admin' || user?.app_metadata?.role === 'admin';

// The database-level is_admin() function handles server-side authorization
```

## Error Handling

```typescript
// Helper functions from src/lib/supabase.ts
export const handleSupabaseError = (error: any) => {
  console.error('Supabase error:', error);
  return {
    success: false,
    error: error.message || 'An unexpected error occurred',
  };
};

export const handleSupabaseSuccess = <T>(data: T) => {
  return {
    success: true,
    data,
  };
};

// Usage example
const createBooking = async (bookingData: BookingFormData) => {
  try {
    const { data, error } = await supabase
      .from('bookings')
      .insert(bookingData)
      .select()
      .single();

    if (error) return handleSupabaseError(error);
    return handleSupabaseSuccess(data);
  } catch (error) {
    return handleSupabaseError(error);
  }
};
```

## Real-time Subscriptions

```typescript
// Subscribe to booking changes (Admin only - will be filtered by RLS)
const subscription = supabase
  .channel('bookings-changes')
  .on(
    'postgres_changes',
    {
      event: '*',
      schema: 'public',
      table: 'bookings',
    },
    (payload) => {
      console.log('Booking changed:', payload);
      // Update UI accordingly
    }
  )
  .subscribe();

// Clean up subscription
subscription.unsubscribe();
```

## Best Practices

1. **Always use TypeScript types** for type safety
2. **Handle RLS policy violations gracefully** - operations may silently fail for unauthorized users
3. **Use select() with specific fields** to optimize performance
4. **Implement proper error handling** for all database operations
5. **Use real-time subscriptions** for admin dashboards to show live updates
6. **Store only public URLs** in photo_url fields (use Supabase Storage)
7. **Validate data on both client and server side** using the CHECK constraints as reference

## Environment Variables Required

```bash
# .env.local
NEXT_PUBLIC_SUPABASE_URL=your_supabase_project_url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key
SUPABASE_SERVICE_ROLE_KEY=your_supabase_service_role_key # For admin operations
```
