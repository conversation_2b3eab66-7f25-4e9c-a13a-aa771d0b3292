# Headless UI Testing Guide

## Overview

This guide explains how to properly test components that use Headless UI components (Dialog, Transition, Menu, etc.) to avoid <PERSON>act's `act()` warnings and ensure reliable test behavior.

## The Problem

Headless UI components manage their own internal state for transitions and animations. When these components mount or change their props (like `isOpen`), they trigger state updates that <PERSON><PERSON>'s test environment detects as "unwrapped" state updates, leading to warnings like:

```
An update to TransitionRootFn inside a test was not wrapped in act(...)
```

## The Solution

### 1. Import Required Testing Utilities

```typescript
import {
  render,
  screen,
  fireEvent,
  act,
  waitFor,
} from '@testing-library/react';
```

### 2. Wrap Renders in act()

When rendering components with Headless UI transitions:

```typescript
// ❌ Incorrect - causes act() warnings
render(<VehicleModal vehicle={mockVehicle} isOpen={true} onClose={mockOnClose} />);

// ✅ Correct - wraps state updates in act()
await act(async () => {
  render(<VehicleModal vehicle={mockVehicle} isOpen={true} onClose={mockOnClose} />);
});
```

### 3. Wait for Transitions to Complete

Use `waitFor()` to ensure all state updates have settled:

```typescript
// Wait for transition state updates to complete
await waitFor(() => {
  const button = screen.getByText('Reservar Este Veículo');
  expect(button).toBeInTheDocument();
});
```

### 4. Wrap User Interactions

When triggering events that might cause state updates:

```typescript
// ✅ Wrap interactions that might trigger state updates
await act(async () => {
  fireEvent.click(reservarButton);
});
```

## Complete Example

Here's a properly implemented test for a component using Headless UI Dialog:

```typescript
import { render, screen, fireEvent, act, waitFor } from '@testing-library/react';
import '@testing-library/jest-dom';
import VehicleModal from '@/components/VehicleModal';

describe('VehicleModal', () => {
  it('should handle modal interactions properly', async () => {
    const mockOnClose = jest.fn();

    // Wrap render in act() to handle Headless UI Transition state updates
    await act(async () => {
      render(
        <VehicleModal vehicle={mockVehicle} isOpen={true} onClose={mockOnClose} />
      );
    });

    // Wait for any transition state updates to complete
    await waitFor(() => {
      const button = screen.getByText('Reservar Este Veículo');
      expect(button).toBeInTheDocument();
    });

    // Find and interact with elements
    const reservarButton = screen.getByText('Reservar Este Veículo');

    // Wrap interactions in act()
    await act(async () => {
      fireEvent.click(reservarButton);
    });

    // Verify results
    expect(mockPush).toHaveBeenCalledWith('/reservas?vehicle=test-vehicle-456');
  });
});
```

## Using the Test Utilities

We've created helper utilities in `src/test-utils/headlessui-test-utils.ts`:

```typescript
import { renderWithTransition, actWithTransition } from '@/test-utils/headlessui-test-utils';

describe('VehicleModal', () => {
  it('should handle modal interactions properly', async () => {
    const mockOnClose = jest.fn();

    // Use the utility for cleaner code
    const { getByText } = await renderWithTransition(
      <VehicleModal vehicle={mockVehicle} isOpen={true} onClose={mockOnClose} />
    );

    const button = getByText('Reservar Este Veículo');

    // Use the utility for interactions
    await actWithTransition(() => {
      fireEvent.click(button);
    });

    expect(mockPush).toHaveBeenCalledWith('/reservas?vehicle=test-vehicle-456');
  });
});
```

## Best Practices

### 1. Always Use Async/Await

Make your test functions `async` and use `await` with `act()` and `waitFor()`:

```typescript
it('should work correctly', async () => {
  // Test implementation
});
```

### 2. Be Consistent

Apply the same pattern to all tests involving Headless UI components, even if they don't seem to need it:

```typescript
// Even for cases that might not trigger transitions
await act(async () => {
  render(<VehicleModal vehicle={null} isOpen={true} onClose={mockOnClose} />);
});
```

### 3. Test Isolation

Ensure proper cleanup between tests:

```typescript
describe('VehicleModal', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });
});
```

### 4. Use Test Utilities

Leverage the provided test utilities for consistent behavior across your test suite.

## Common Headless UI Components

This approach applies to all Headless UI components that manage transitions:

- `Dialog` and `Dialog.Panel`
- `Transition` and `Transition.Child`
- `Menu` and `Menu.Items`
- `Listbox` and `Listbox.Options`
- `Combobox` and `Combobox.Options`
- `Disclosure` and `Disclosure.Panel`

## Troubleshooting

### Warning Still Appears

If you still see act() warnings:

1. Check if you're testing the right component
2. Ensure all renders are wrapped in `act()`
3. Use `waitFor()` after renders
4. Run tests individually to isolate the issue

### Tests Are Flaky

If tests pass sometimes but fail other times:

1. Add more `waitFor()` calls
2. Increase timeout values
3. Check for race conditions in your component logic

### Performance Issues

If tests are slow:

1. Use `jest.useFakeTimers()` for components with delays
2. Mock heavy animations
3. Consider using `renderWithTransition` utility

## Related Files

- `__tests__/VehicleModal.test.tsx` - Example implementation
- `src/test-utils/headlessui-test-utils.ts` - Test utilities
- `src/components/VehicleModal.tsx` - Component being tested
