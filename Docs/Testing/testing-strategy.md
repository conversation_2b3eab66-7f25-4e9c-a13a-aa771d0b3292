# Testing Strategy: Vintage Marketing Portugal

## Pre-Commit Checks

- Use **<PERSON>sky** to run:
  - ESLint (lint all code)
  - TypeScript (type check)
  - Jest (unit/integration tests)
  - <PERSON><PERSON>er (format check)
- Block commits if any check fails.

## Unit Testing

- Use **Jest** and **React Testing Library** for all components and logic.
- Write tests for booking logic, admin actions, and API calls.

## Integration Testing

- Test booking flow end-to-end (form, DB, email).
- Test admin panel (auth, CRUD operations).

## Manual Testing

- Test on mobile and desktop browsers.
- Use Google’s Mobile-Friendly Test and Rich Results Test.

## CI/CD

- All tests/checks and deployment are managed by the AI Assistant via GitHub connection.
