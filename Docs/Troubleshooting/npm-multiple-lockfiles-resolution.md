# NPM Multiple Lockfiles Resolution Guide

## Problem Description

When running npm commands, you may encounter this warning:

```
Warning: Found multiple lockfiles. Selecting /Users/<USER>/package-lock.json.
   Consider removing the lockfiles at:
   * /Users/<USER>/project-name/package-lock.json
```

This warning indicates that npm found multiple `package-lock.json` files in the directory hierarchy and is unsure which one to use.

## Understanding the Issue

### How NPM Resolves Lockfiles

NPM searches for lockfiles in the following order:

1. Current directory (`./package-lock.json`)
2. Parent directories (walking up the tree)
3. Uses the first one it finds

When multiple lockfiles exist, npm issues a warning and selects one based on its internal logic, which may not be the one you want.

### Common Causes

1. **Accidental creation** - Running `npm install` in parent directories
2. **Project migration** - Moving projects between directories
3. **Nested projects** - Having projects within projects
4. **Development setup** - Creating temporary lockfiles during testing

## Diagnosis Steps

### 1. Locate All Lockfiles

```bash
# Find all package-lock.json files in current and parent directories
find /Users/<USER>"package-lock.json" -type f 2>/dev/null | head -10
```

### 2. Examine Lockfile Details

```bash
# Check file sizes and dates
ls -la /path/to/package-lock.json

# Check content (first few lines)
head -10 /path/to/package-lock.json
```

### 3. Identify the Correct Lockfile

The correct lockfile should:

- ✅ Be in your project root directory
- ✅ Have a substantial size (typically >100KB for real projects)
- ✅ Have a recent modification date
- ✅ Contain your project's actual dependencies

Incorrect lockfiles typically:

- ❌ Are in parent directories
- ❌ Are very small (empty or minimal)
- ❌ Have old modification dates
- ❌ Don't contain your project's dependencies

## Resolution Process

### Step 1: Backup Current State

```bash
# Create a backup of your current working lockfile
cp package-lock.json package-lock.json.backup
```

### Step 2: Identify Which Lockfile to Remove

**Example Analysis:**

```bash
# Project lockfile (KEEP)
$ ls -la ./package-lock.json
-rw-r--r--  1 <USER>  <GROUP>  516339 Jul 21 01:31 package-lock.json

# Parent lockfile (REMOVE)
$ ls -la /Users/<USER>/package-lock.json
-rw-r--r--  1 <USER>  <GROUP>  92 Nov 11  2023 package-lock.json
```

### Step 3: Verify Content Before Removal

```bash
# Check the parent lockfile content
cat /Users/<USER>/package-lock.json
```

If it shows minimal content like:

```json
{
  "name": "username",
  "lockfileVersion": 2,
  "requires": true,
  "packages": {}
}
```

This is safe to remove.

### Step 4: Remove the Incorrect Lockfile

```bash
# Remove the stale/incorrect lockfile
rm /Users/<USER>/package-lock.json
```

### Step 5: Verify the Fix

```bash
# Run npm command to check for warnings
npm list --depth=0

# Should show no lockfile warnings
```

### Step 6: Reinstall Dependencies (if needed)

```bash
# If node_modules was affected, reinstall
npm ci
```

## Verification Checklist

After resolution, verify:

- [ ] No lockfile warnings when running npm commands
- [ ] `npm list --depth=0` shows expected packages
- [ ] `npm run lint` works (if applicable)
- [ ] `npm test` works (if applicable)
- [ ] `npm run build` works (if applicable)
- [ ] All recent dependency upgrades are preserved

## Prevention Best Practices

### 1. Project Structure

```
/Users/<USER>/
├── project1/
│   ├── package.json
│   ├── package-lock.json  ✅ Correct location
│   └── node_modules/
├── project2/
│   ├── package.json
│   ├── package-lock.json  ✅ Correct location
│   └── node_modules/
└── package-lock.json      ❌ Avoid this
```

### 2. Development Practices

- **Always run npm commands from project root**
- **Never run `npm install` in parent directories**
- **Use `npm ci` in CI/CD for deterministic builds**
- **Regularly clean up stale lockfiles**

### 3. CI/CD Configuration

```bash
# In your CI/CD scripts, always use npm ci
npm ci  # Not npm install

# Verify you're in the right directory
pwd
ls package.json  # Should exist
```

### 4. Team Guidelines

- **Document project structure** in README
- **Use `.nvmrc` for Node.js version consistency**
- **Include lockfile in version control**
- **Never commit parent directory lockfiles**

## Troubleshooting Common Issues

### Issue: "npm ci" fails after lockfile removal

**Solution:**

```bash
# Regenerate lockfile
rm package-lock.json
npm install
```

### Issue: Dependencies seem wrong after fix

**Solution:**

```bash
# Verify package.json is correct
cat package.json

# Clean install
rm -rf node_modules package-lock.json
npm install
```

### Issue: Warning persists after removal

**Solution:**

```bash
# Check for other lockfile types
ls -la yarn.lock pnpm-lock.yaml

# Clear npm cache
npm cache clean --force
```

## Related Files

- `package.json` - Project dependencies
- `package-lock.json` - Lockfile (keep in project root)
- `node_modules/` - Installed packages
- `.npmrc` - NPM configuration

## When to Seek Help

Contact your team lead or DevOps if:

- Multiple projects share dependencies
- Corporate npm registry is involved
- Monorepo setup with complex dependencies
- CI/CD pipeline is affected

## Example: Vintage Marketing Portugal Resolution

**Problem:** Multiple lockfiles warning
**Root Cause:** Stale lockfile in `/Users/<USER>/package-lock.json`
**Solution:** Removed stale lockfile, kept project lockfile
**Result:** No warnings, all functionality preserved

This resolution maintained ESLint 9 and Jest 30 upgrades while eliminating the warning.
