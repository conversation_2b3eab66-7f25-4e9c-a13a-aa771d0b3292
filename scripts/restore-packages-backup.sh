#!/bin/bash

# 🔄 Package Restoration Script
# Restores packages from backup created on 2025-01-21

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Backup timestamp
BACKUP_TIMESTAMP="20250721_021928"

echo -e "${BLUE}🔄 Package Restoration Script${NC}"
echo "=============================================="
echo ""

# Check if we're in the right directory
if [ ! -f "package.json" ]; then
    echo -e "${RED}❌ Error: package.json not found. Please run this script from the project root.${NC}"
    exit 1
fi

# Check if backup files exist
BACKUP_DIR="backups"
PACKAGE_BACKUP="${BACKUP_DIR}/package.json.backup_${BACKUP_TIMESTAMP}"
LOCK_BACKUP="${BACKUP_DIR}/package-lock.json.backup_${BACKUP_TIMESTAMP}"

if [ ! -f "$PACKAGE_BACKUP" ]; then
    echo -e "${RED}❌ Error: Backup file not found: $PACKAGE_BACKUP${NC}"
    exit 1
fi

if [ ! -f "$LOCK_BACKUP" ]; then
    echo -e "${RED}❌ Error: Backup file not found: $LOCK_BACKUP${NC}"
    exit 1
fi

echo -e "${YELLOW}⚠️  This will restore your packages to the state before the 2025-01-21 updates:${NC}"
echo ""
echo "📦 Packages that will be restored:"
echo "  • @types/node: 24.0.15 → 20.19.9"
echo "  • date-fns: 4.1.0 → 3.6.0"
echo "  • react-datepicker: 8.4.0 → 7.6.0"
echo "  • resend: 4.7.0 → 3.5.0"
echo ""

# Confirmation prompt
read -p "Do you want to continue? (y/N): " -n 1 -r
echo ""
if [[ ! $REPLY =~ ^[Yy]$ ]]; then
    echo -e "${YELLOW}🚫 Restoration cancelled.${NC}"
    exit 0
fi

echo ""
echo -e "${BLUE}📋 Step 1: Creating current state backup${NC}"
CURRENT_TIMESTAMP=$(date +"%Y%m%d_%H%M%S")
cp package.json "backups/package.json.before_restore_${CURRENT_TIMESTAMP}"
cp package-lock.json "backups/package-lock.json.before_restore_${CURRENT_TIMESTAMP}"
echo -e "${GREEN}✅ Current state backed up with timestamp: ${CURRENT_TIMESTAMP}${NC}"

echo ""
echo -e "${BLUE}📋 Step 2: Restoring package files${NC}"
cp "$PACKAGE_BACKUP" package.json
cp "$LOCK_BACKUP" package-lock.json
echo -e "${GREEN}✅ Package files restored${NC}"

echo ""
echo -e "${BLUE}📋 Step 3: Cleaning node_modules${NC}"
if [ -d "node_modules" ]; then
    rm -rf node_modules
    echo -e "${GREEN}✅ node_modules removed${NC}"
else
    echo -e "${YELLOW}ℹ️  node_modules directory not found${NC}"
fi

echo ""
echo -e "${BLUE}📋 Step 4: Installing dependencies${NC}"
npm ci
echo -e "${GREEN}✅ Dependencies installed${NC}"

echo ""
echo -e "${BLUE}📋 Step 5: Verifying restoration${NC}"

# Run verification tests
echo -e "${YELLOW}🔍 Running TypeScript check...${NC}"
if npm run type-check > /dev/null 2>&1; then
    echo -e "${GREEN}✅ TypeScript check passed${NC}"
else
    echo -e "${RED}❌ TypeScript check failed${NC}"
    exit 1
fi

echo -e "${YELLOW}🔍 Running build test...${NC}"
if npm run build > /dev/null 2>&1; then
    echo -e "${GREEN}✅ Build test passed${NC}"
else
    echo -e "${RED}❌ Build test failed${NC}"
    exit 1
fi

echo ""
echo -e "${GREEN}🎉 Package restoration completed successfully!${NC}"
echo ""
echo -e "${BLUE}📊 Restored package versions:${NC}"
npm list @types/node date-fns react-datepicker resend tailwindcss --depth=0 2>/dev/null | grep -E "(├──|└──)" || true

echo ""
echo -e "${YELLOW}📝 Next steps:${NC}"
echo "1. Test your application thoroughly"
echo "2. Run the full test suite: npm test"
echo "3. Check for any functionality issues"
echo ""
echo -e "${BLUE}💡 To re-apply updates later, refer to:${NC}"
echo "   Docs/Maintenance/package-updates-2025-01-21.md"
echo ""
