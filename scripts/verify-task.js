#!/usr/bin/env node

/**
 * Task Verification Script for Vintage Marketing Portugal
 *
 * This script automates the verification process after completing a task.
 * It runs code quality checks, verifies compilation, and provides a checklist.
 */

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

console.log('🔍 Task Verification Script - Vintage Marketing Portugal\n');

// Colors for console output
const colors = {
  green: '\x1b[32m',
  red: '\x1b[31m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  reset: '\x1b[0m',
  bold: '\x1b[1m',
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function runCommand(command, description) {
  try {
    log(`\n📋 ${description}...`, 'blue');
    const output = execSync(command, {
      encoding: 'utf8',
      stdio: ['pipe', 'pipe', 'pipe'],
      cwd: process.cwd(),
    });
    log(`✅ ${description} - PASSED`, 'green');
    return { success: true, output };
  } catch (error) {
    log(`❌ ${description} - FAILED`, 'red');
    log(`Error: ${error.message}`, 'red');
    return { success: false, error: error.message };
  }
}

function checkFileExists(filePath, description) {
  const exists = fs.existsSync(filePath);
  if (exists) {
    log(`✅ ${description} - EXISTS`, 'green');
  } else {
    log(`❌ ${description} - MISSING`, 'red');
  }
  return exists;
}

async function main() {
  const results = {
    passed: 0,
    failed: 0,
    checks: [],
  };

  function addResult(description, success, details = '') {
    results.checks.push({ description, success, details });
    if (success) {
      results.passed++;
    } else {
      results.failed++;
    }
  }

  log('🚀 Starting Task Verification Process\n', 'bold');

  // 1. Code Quality Checks
  log('='.repeat(50), 'blue');
  log('1. CODE QUALITY CHECKS', 'bold');
  log('='.repeat(50), 'blue');

  // TypeScript compilation check
  const typeCheck = runCommand(
    'npm run type-check',
    'TypeScript Type Checking'
  );
  addResult('TypeScript Type Checking', typeCheck.success, typeCheck.error);

  // Linting check
  const lintCheck = runCommand('npm run lint', 'ESLint Code Quality Check');
  addResult('ESLint Code Quality', lintCheck.success, lintCheck.error);

  // Build check
  const buildCheck = runCommand('npm run build', 'Next.js Build Verification');
  addResult('Next.js Build', buildCheck.success, buildCheck.error);

  // 2. File Structure Verification
  log('\n' + '='.repeat(50), 'blue');
  log('2. FILE STRUCTURE VERIFICATION', 'bold');
  log('='.repeat(50), 'blue');

  // Check key files exist
  const keyFiles = [
    { path: 'package.json', desc: 'Package.json' },
    { path: 'src/app/layout.tsx', desc: 'Root Layout' },
    { path: 'src/lib/supabase.ts', desc: 'Supabase Configuration' },
    { path: 'src/types/index.ts', desc: 'TypeScript Types' },
    { path: '.env.local', desc: 'Environment Variables' },
  ];

  keyFiles.forEach((file) => {
    const exists = checkFileExists(file.path, file.desc);
    addResult(file.desc, exists);
  });

  // 3. Portuguese Language Check
  log('\n' + '='.repeat(50), 'blue');
  log('3. PORTUGUESE LANGUAGE CONSISTENCY', 'bold');
  log('='.repeat(50), 'blue');

  // Check for English words in user-facing content only
  let languageIssues = [];

  try {
    // Search for user-facing English content using simpler, more reliable patterns
    const englishUIWords = [
      'Login',
      'Email',
      'Password',
      'Submit',
      'Cancel',
      'Loading',
      'Save',
      'Delete',
      'Edit',
      'Create',
      'Update',
      'Search',
      'Filter',
      'Sort',
      'View',
      'Show',
      'Hide',
      'Open',
      'Close',
      'Back',
      'Next',
      'Previous',
      'Continue',
      'Finish',
      'Start',
      'Stop',
      'Play',
      'Pause',
      'Add',
      'Remove',
      'Clear',
      'Reset',
      'Confirm',
      'Reject',
      'Accept',
      'Decline',
      'Yes',
      'No',
      'OK',
      'Help',
      'About',
      'Contact',
      'Home',
      'Profile',
      'Settings',
      'Dashboard',
      'Account',
      'Logout',
      'Register',
      'Welcome',
      'Hello',
      'Goodbye',
      'Please',
      'Sorry',
      'Congratulations',
    ];

    // Search for English words in JSX text content and string literals
    for (const word of englishUIWords) {
      try {
        // Search for the word in JSX content (between > and <) or in string literals
        const patterns = [
          `>${word}<`, // JSX text content
          `"${word}"`, // Double quoted strings
          `'${word}'`, // Single quoted strings
          `alt="${word}"`, // Alt text
          `title="${word}"`, // Title attribute
          `placeholder="${word}"`, // Placeholder text
          `aria-label="${word}"`, // Aria label
        ];

        for (const pattern of patterns) {
          const searchCommand = `grep -r -l "${pattern}" src/components/ src/app/ --include="*.tsx" --include="*.ts" 2>/dev/null || true`;
          const searchResult = execSync(searchCommand, { encoding: 'utf8' });

          if (searchResult.trim()) {
            // Get the actual lines containing the pattern
            const files = searchResult
              .split('\n')
              .filter((line) => line.trim());
            for (const file of files) {
              const lineCommand = `grep -n "${pattern}" "${file}" 2>/dev/null || true`;
              const lineResult = execSync(lineCommand, { encoding: 'utf8' });
              if (lineResult.trim()) {
                languageIssues.push(`${file}: ${lineResult.trim()}`);
              }
            }
          }
        }
      } catch (error) {
        // Individual search failed, continue with next word
      }
    }

    // Remove duplicates and filter out false positives
    languageIssues = [...new Set(languageIssues)].filter((issue) => {
      // Filter out programming terms that are not user-facing
      const programmingPatterns = [
        /\bisSubmitting\b/i,
        /\bsetIsSubmitting\b/i,
        /\bhandle[A-Z]\w+/i, // handleSubmit, handleClick, etc.
        /\buse[A-Z]\w+/i, // useState, useEffect, etc.
        /\bconsole\./i, // console.log, console.error
        /\b(const|let|var|function|class|interface|type|enum)\s/i,
        /\b(import|export|default|extends|implements)\b/i,
        /\b(className|onClick|onChange|onSubmit|onError)\b/i,
        /\b(try|catch|throw|async|await)\b/i,
      ];

      // Check if the issue line contains only programming terms (not user-facing content)
      const isOnlyProgrammingTerm = programmingPatterns.some(
        (pattern) =>
          pattern.test(issue) &&
          !issue.includes('>') && // Not JSX content
          !issue.includes('alt=') && // Not alt text
          !issue.includes('title=') && // Not title
          !issue.includes('placeholder=') && // Not placeholder
          !issue.includes('aria-label=') // Not aria label
      );

      return !isOnlyProgrammingTerm;
    });
  } catch (error) {
    // Grep command failed, but that's okay
  }

  if (languageIssues.length === 0) {
    log('✅ Portuguese Language Consistency - GOOD', 'green');
    addResult('Portuguese Language Consistency', true);
  } else {
    log('⚠️  Portuguese Language Consistency - ISSUES FOUND', 'yellow');
    log('Found English text in user-facing content:', 'yellow');
    languageIssues.slice(0, 5).forEach((issue) => {
      log(`  ${issue}`, 'yellow');
    });
    if (languageIssues.length > 5) {
      log(`  ... and ${languageIssues.length - 5} more issues`, 'yellow');
    }
    log(
      '\nNote: Programming variable names and function names are ignored.',
      'blue'
    );
    log(
      'Only user-facing text (JSX content, strings, alt text, etc.) is checked.',
      'blue'
    );
    addResult(
      'Portuguese Language Consistency',
      false,
      `${languageIssues.length} user-facing content issues found`
    );
  }

  // 4. Environment Configuration
  log('\n' + '='.repeat(50), 'blue');
  log('4. ENVIRONMENT CONFIGURATION', 'bold');
  log('='.repeat(50), 'blue');

  try {
    const envContent = fs.readFileSync('.env.local', 'utf8');
    const hasSupabaseUrl = envContent.includes('NEXT_PUBLIC_SUPABASE_URL=');
    const hasSupabaseKey = envContent.includes(
      'NEXT_PUBLIC_SUPABASE_ANON_KEY='
    );

    if (hasSupabaseUrl && hasSupabaseKey) {
      log('✅ Supabase Configuration - CONFIGURED', 'green');
      addResult('Supabase Configuration', true);
    } else {
      log('❌ Supabase Configuration - INCOMPLETE', 'red');
      addResult(
        'Supabase Configuration',
        false,
        'Missing Supabase environment variables'
      );
    }
  } catch (error) {
    log('❌ Environment File - NOT FOUND', 'red');
    addResult('Environment Configuration', false, '.env.local file not found');
  }

  // 5. Summary and Manual Checklist
  log('\n' + '='.repeat(50), 'blue');
  log('5. VERIFICATION SUMMARY', 'bold');
  log('='.repeat(50), 'blue');

  log(
    `\n📊 Results: ${results.passed} passed, ${results.failed} failed\n`,
    'bold'
  );

  if (results.failed === 0) {
    log('🎉 All automated checks PASSED!', 'green');
  } else {
    log(
      '⚠️  Some checks FAILED. Please review and fix issues before proceeding.',
      'yellow'
    );
  }

  // Manual checklist
  log('\n📋 MANUAL VERIFICATION CHECKLIST:', 'bold');
  log('Please verify the following manually:\n', 'blue');

  const manualChecks = [
    '[ ] Development server starts without errors (npm run dev)',
    '[ ] All new functionality works correctly in browser',
    '[ ] Responsive design works on mobile and desktop',
    '[ ] No console errors in browser developer tools',
    '[ ] All user flows related to the task work correctly',
    '[ ] Edge cases and error scenarios are handled properly',
    '[ ] Portuguese language is consistent across all UI text',
    '[ ] Form validation works with appropriate error messages',
    '[ ] Loading states and disabled inputs work correctly',
    '[ ] Navigation and routing work as expected',
  ];

  manualChecks.forEach((check) => {
    log(check, 'blue');
  });

  log('\n📝 NEXT STEPS:', 'bold');
  log('1. Complete the manual verification checklist above', 'blue');
  log('2. Fix any issues found during verification', 'blue');
  log('3. Stage and commit changes using conventional commit format', 'blue');
  log('4. Update documentation if needed', 'blue');
  log('5. Mark the task as complete in task management', 'blue');

  log('\n🔗 HELPFUL COMMANDS:', 'bold');
  log('npm run dev          # Start development server', 'blue');
  log('npm run lint -- --fix # Fix linting issues', 'blue');
  log('git status           # Check git status', 'blue');
  log('git add .            # Stage all changes', 'blue');
  log('git commit -m "feat: description (Task X)" # Commit changes', 'blue');

  // Exit with appropriate code
  process.exit(results.failed > 0 ? 1 : 0);
}

main().catch((error) => {
  log(`\n❌ Verification script failed: ${error.message}`, 'red');
  process.exit(1);
});
