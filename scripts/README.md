# 🚀 Deployment Scripts - Vintage Marketing Portugal

This directory contains automated setup scripts for deploying your Vintage Marketing Portugal MVP.

## 📋 Available Scripts

### 1. `setup-deployment.sh` - Complete Setup

**Full deployment setup with all configurations**

```bash
./scripts/setup-deployment.sh
```

**What it does:**

- ✅ Checks prerequisites (Node.js, npm, git)
- ✅ Installs dependencies
- ✅ Sets up environment files
- ✅ Runs quality checks (TypeScript, ESLint, tests)
- ✅ Collects all secrets (essential + optional)
- ✅ Configures GitHub repository secrets
- ✅ Sets up Vercel environment variables
- ✅ Configures pre-commit hooks (optional)
- ✅ Shows functionality summary

**Best for:** First-time setup or complete reconfiguration

### 2. `setup-github-secrets.sh` - Quick Secrets Setup

**GitHub secrets only (fastest MVP deployment)**

```bash
./scripts/setup-github-secrets.sh
```

**What it does:**

- ✅ Collects essential MVP secrets
- ✅ Optionally collects additional secrets
- ✅ Sets secrets in GitHub repository
- ✅ Shows functionality summary
- ✅ Provides next steps

**Best for:** Quick MVP deployment or updating secrets

### 3. `validate-ci-compatibility.sh` - CI Environment Simulation

**Local validation that simulates exact CI environment**

```bash
./scripts/validate-ci-compatibility.sh
# or
npm run validate-ci
```

**What it does:**

- ✅ Simulates fresh CI environment (removes node_modules)
- ✅ Runs `npm ci` exactly as CI does
- ✅ Validates TypeScript compilation
- ✅ Checks ESLint code quality
- ✅ Verifies Prettier formatting
- ✅ Executes Jest test suite
- ✅ Performs Next.js build verification
- ✅ Runs security audit

**Best for:** Pre-push validation to catch CI issues locally

### 4. `restore-packages-backup.sh` - Package Restoration

**Restore packages from backup after updates**

```bash
./scripts/restore-packages-backup.sh
```

**What it does:**

- ✅ Restores package.json and package-lock.json from backup
- ✅ Creates backup of current state before restoration
- ✅ Cleans and reinstalls dependencies
- ✅ Verifies restoration with TypeScript and build tests
- ✅ Provides detailed restoration report

**Best for:** Rolling back package updates if issues are discovered

## 🔑 Secret Categories

### Essential MVP Secrets (Required)

These secrets are **required** for basic deployment:

| Secret                          | Purpose                  | Where to find                           |
| ------------------------------- | ------------------------ | --------------------------------------- |
| `NEXT_PUBLIC_SUPABASE_URL`      | Database connection      | Supabase Dashboard → Settings → API     |
| `NEXT_PUBLIC_SUPABASE_ANON_KEY` | Public database access   | Supabase Dashboard → Settings → API     |
| `NEXTAUTH_SECRET`               | Authentication security  | Generate with `openssl rand -base64 32` |
| `VERCEL_TOKEN`                  | Deployment authorization | https://vercel.com/account/tokens       |
| `VERCEL_ORG_ID`                 | Vercel organization      | Run `vercel teams list`                 |
| `VERCEL_PROJECT_ID`             | Vercel project           | Run `vercel projects list`              |

### Optional Secrets (Full Functionality)

These secrets enable additional features:

| Secret                      | Purpose           | Impact if missing               |
| --------------------------- | ----------------- | ------------------------------- |
| `RESEND_API_KEY`            | Email delivery    | Contact forms won't send emails |
| `SUPABASE_SERVICE_ROLE_KEY` | Admin operations  | Admin panel won't work          |
| `NEXTAUTH_URL`              | Production domain | Uses Vercel default domain      |

## 🚀 Quick Start (MVP Deployment)

**Option 1: Fastest (Secrets Only)**

```bash
# Just set up GitHub secrets and deploy
./scripts/setup-github-secrets.sh
npm run validate-ci  # Validate before pushing
git push origin main
```

**Option 2: Complete Setup**

```bash
# Full setup with all configurations
./scripts/setup-deployment.sh
npm run validate-ci  # Validate before pushing
git push origin main
```

## 🔍 Recommended Development Workflow

### Before Every Push

```bash
# Validate your changes will pass CI
npm run validate-ci

# If validation passes, push safely
git add .
git commit -m "your commit message"
git push origin main
```

### Pre-commit Hook (Automatic)

The pre-commit hook automatically runs `validate-ci-compatibility.sh` to catch issues before commits.

### Manual Validation Commands

```bash
# Full CI simulation
npm run validate-ci

# Individual checks
npm run type-check    # TypeScript
npm run lint         # ESLint
npm run format:check # Prettier
npm test            # Jest tests
npm run build       # Next.js build
```

## 📋 Prerequisites

### Required Tools

- **Node.js 18+** - `node --version`
- **npm** - `npm --version`
- **Git** - `git --version`
- **GitHub CLI** - `gh --version` (for automated secret setup)

### Optional Tools

- **Vercel CLI** - `vercel --version` (for environment variables)
- **OpenSSL** - `openssl version` (for secret generation)

### Installation Commands

```bash
# macOS (Homebrew)
brew install node gh vercel-cli

# Ubuntu/Debian
sudo apt install nodejs npm gh
npm install -g vercel

# Windows (Chocolatey)
choco install nodejs gh vercel-cli
```

## 🔧 Manual Setup (Alternative)

If you prefer manual setup or scripts fail:

### 1. GitHub Secrets

Go to: `https://github.com/YOUR_USERNAME/vintage-marketing-portugal/settings/secrets/actions`

Add each secret with "New repository secret"

### 2. Vercel Environment Variables

Go to: Vercel Dashboard → Project → Settings → Environment Variables

Add the same secrets (except VERCEL\_\* ones)

### 3. Local Environment

```bash
cp .env.local.example .env.local
# Edit .env.local with your values
```

## 🔍 Troubleshooting

### Common Issues

**❌ "GitHub CLI not found"**

```bash
# Install GitHub CLI
brew install gh  # macOS
sudo apt install gh  # Ubuntu
# Then login: gh auth login
```

**❌ "Repository not found"**

```bash
# Check git remote
git remote -v
# Should show GitHub repository URL
```

**❌ "Vercel CLI not found"**

```bash
# Install Vercel CLI
npm install -g vercel
# Then login: vercel login
```

**❌ "Permission denied"**

```bash
# Make scripts executable
chmod +x scripts/*.sh
```

### Script Debugging

```bash
# Run with debug output
bash -x ./scripts/setup-github-secrets.sh

# Check script permissions
ls -la scripts/
```

## 📊 What Each Script Validates

### Essential Validations

- ✅ Supabase URL format (`https://*.supabase.co`)
- ✅ Secret length (NextAuth secret ≥ 32 chars)
- ✅ Non-empty essential secrets
- ✅ GitHub repository access
- ✅ Vercel token validity

### Quality Checks

- ✅ TypeScript compilation
- ✅ ESLint code quality
- ✅ Jest test execution
- ✅ Prettier formatting
- ✅ Build verification

## 🎯 Functionality Matrix

| Feature             | Essential Secrets | Optional Secrets      |
| ------------------- | ----------------- | --------------------- |
| Website hosting     | ✅                | ✅                    |
| Vehicle browsing    | ✅                | ✅                    |
| Responsive design   | ✅                | ✅                    |
| SEO optimization    | ✅                | ✅                    |
| Reservation form UI | ✅                | ✅                    |
| Contact form emails | ❌                | ✅ (RESEND_API_KEY)   |
| Admin panel         | ❌                | ✅ (SERVICE_ROLE_KEY) |
| Custom domain auth  | ❌                | ✅ (NEXTAUTH_URL)     |

## 📦 Package Management

### Dependency Updates

**Check for outdated packages:**

```bash
npm outdated
```

**Update packages safely:**

```bash
# See latest package updates documentation
cat Docs/Maintenance/package-updates-2025-01-21.md

# Restore from backup if needed
./scripts/restore-packages-backup.sh
```

**Monthly maintenance routine:**

1. Check for security vulnerabilities: `npm audit`
2. Review outdated packages: `npm outdated`
3. Update packages incrementally with testing
4. Document changes and create backups

## 📞 Support

### Documentation

- 📖 [Full Deployment Guide](../DEPLOYMENT.md)
- 🚀 [Quick Reference](../CI-CD-QUICK-REFERENCE.md)
- 🔧 [GitHub Actions Workflow](../.github/workflows/ci-cd.yml)
- 📦 [Package Updates Log](../Docs/Maintenance/)

### Getting Help

1. Check script output for specific error messages
2. Verify all prerequisites are installed
3. Ensure GitHub repository access
4. Check Supabase and Vercel dashboard access
5. Review the troubleshooting section above

---

**💡 Pro Tip:** Start with `setup-github-secrets.sh` for fastest MVP deployment, then add optional secrets later as needed!
