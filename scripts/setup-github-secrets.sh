#!/bin/bash

# 🔑 GitHub Secrets Setup - Vintage Marketing Portugal MVP
# Quick script to set up only the GitHub repository secrets

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() { echo -e "${GREEN}✅ $1${NC}"; }
print_warning() { echo -e "${YELLOW}⚠️  $1${NC}"; }
print_error() { echo -e "${RED}❌ $1${NC}"; }
print_info() { echo -e "${BLUE}ℹ️  $1${NC}"; }
print_header() { echo -e "${PURPLE}🔧 $1${NC}"; }
print_instruction() { echo -e "${CYAN}📋 $1${NC}"; }

echo "🔑 GitHub Secrets Setup - Vintage Marketing Portugal MVP"
echo "========================================================"
echo

# Check GitHub CLI
if ! command -v gh &> /dev/null; then
    print_error "GitHub CLI (gh) is required but not installed."
    echo
    print_info "Install GitHub CLI:"
    echo "• macOS: brew install gh"
    echo "• Ubuntu: sudo apt install gh"
    echo "• Windows: winget install GitHub.cli"
    echo "• Or visit: https://cli.github.com/"
    exit 1
fi

# Check if logged in
if ! gh auth status &> /dev/null; then
    print_info "Please log in to GitHub CLI first:"
    gh auth login
fi

# Get repository name
REPO_NAME=$(git remote get-url origin | sed 's/.*github\.com[:/]\([^/]*\/[^/]*\)\.git/\1/' | sed 's/\.git$//')
if [ -z "$REPO_NAME" ]; then
    print_error "Could not determine GitHub repository name"
    exit 1
fi

print_status "Repository: $REPO_NAME"
echo

# Essential secrets collection
print_header "Essential Secrets for MVP Deployment"
echo "These are the minimum secrets needed for deployment:"
echo

declare -A SECRETS

# Supabase URL
print_instruction "1. NEXT_PUBLIC_SUPABASE_URL"
echo "   📍 Supabase Dashboard → Settings → API → Project URL"
echo "   📝 Format: https://your-project.supabase.co"
read -p "   Enter: " SECRETS[NEXT_PUBLIC_SUPABASE_URL]
echo

# Supabase Anon Key
print_instruction "2. NEXT_PUBLIC_SUPABASE_ANON_KEY"
echo "   📍 Supabase Dashboard → Settings → API → Project API keys → anon public"
read -p "   Enter: " SECRETS[NEXT_PUBLIC_SUPABASE_ANON_KEY]
echo

# NextAuth Secret
print_instruction "3. NEXTAUTH_SECRET"
echo "   📍 Generate with: openssl rand -base64 32"
read -p "   Enter (or press Enter to generate): " nextauth_secret
if [ -z "$nextauth_secret" ]; then
    if command -v openssl &> /dev/null; then
        nextauth_secret=$(openssl rand -base64 32)
        print_status "Generated automatically"
    else
        nextauth_secret=$(date +%s | sha256sum | base64 | head -c 32)
        print_status "Generated using fallback method"
    fi
fi
SECRETS[NEXTAUTH_SECRET]="$nextauth_secret"
echo

# Vercel Token
print_instruction "4. VERCEL_TOKEN"
echo "   📍 https://vercel.com/account/tokens → Create Token"
read -p "   Enter: " SECRETS[VERCEL_TOKEN]
echo

# Vercel Org ID
print_instruction "5. VERCEL_ORG_ID"
echo "   📍 Run: vercel teams list (or check Vercel dashboard URL)"
read -p "   Enter: " SECRETS[VERCEL_ORG_ID]
echo

# Vercel Project ID
print_instruction "6. VERCEL_PROJECT_ID"
echo "   📍 Run: vercel projects list (or check Vercel dashboard)"
read -p "   Enter: " SECRETS[VERCEL_PROJECT_ID]
echo

# Optional secrets
print_header "Optional Secrets (for full functionality)"
read -p "Add optional secrets now? (y/n): " -n 1 -r
echo
if [[ $REPLY =~ ^[Yy]$ ]]; then
    # Resend API Key
    print_instruction "7. RESEND_API_KEY (for contact forms)"
    echo "   📍 https://resend.com/api-keys"
    read -p "   Enter (or press Enter to skip): " resend_key
    if [ -n "$resend_key" ]; then
        SECRETS[RESEND_API_KEY]="$resend_key"
    fi
    echo
    
    # Supabase Service Role Key
    print_instruction "8. SUPABASE_SERVICE_ROLE_KEY (for admin features)"
    echo "   📍 Supabase Dashboard → Settings → API → service_role (secret!)"
    read -p "   Enter (or press Enter to skip): " service_key
    if [ -n "$service_key" ]; then
        SECRETS[SUPABASE_SERVICE_ROLE_KEY]="$service_key"
    fi
    echo
    
    # NextAuth URL
    print_instruction "9. NEXTAUTH_URL (production domain)"
    echo "   📍 Your custom domain (can use Vercel default initially)"
    read -p "   Enter (or press Enter to skip): " nextauth_url
    if [ -n "$nextauth_url" ]; then
        SECRETS[NEXTAUTH_URL]="$nextauth_url"
    fi
    echo
fi

# Validation
print_header "Validating secrets..."
essential_secrets=("NEXT_PUBLIC_SUPABASE_URL" "NEXT_PUBLIC_SUPABASE_ANON_KEY" "NEXTAUTH_SECRET" "VERCEL_TOKEN" "VERCEL_ORG_ID" "VERCEL_PROJECT_ID")

for secret in "${essential_secrets[@]}"; do
    if [ -z "${SECRETS[$secret]}" ]; then
        print_error "Essential secret $secret is missing"
        exit 1
    fi
done

print_status "All essential secrets provided"
echo

# Set secrets in GitHub
print_header "Setting secrets in GitHub repository..."

for secret_name in "${!SECRETS[@]}"; do
    if [ -n "${SECRETS[$secret_name]}" ]; then
        echo "${SECRETS[$secret_name]}" | gh secret set "$secret_name" --repo "$REPO_NAME"
        print_status "Set $secret_name"
    fi
done

echo
print_status "🎉 All secrets configured successfully!"
echo

# Show functionality summary
print_header "Functionality Summary"
echo
print_status "✅ ENABLED FEATURES:"
echo "  🌐 Website deployment and hosting"
echo "  📱 Responsive design and animations"
echo "  🚗 Vehicle display and browsing"
echo "  📋 Basic reservation form (UI)"
echo "  🔍 SEO and Portuguese content"
echo

if [ -z "${SECRETS[RESEND_API_KEY]:-}" ] || [ -z "${SECRETS[SUPABASE_SERVICE_ROLE_KEY]:-}" ]; then
    print_warning "⚠️  LIMITED FEATURES (add these secrets later):"
    [ -z "${SECRETS[RESEND_API_KEY]:-}" ] && echo "  📧 Contact form email delivery"
    [ -z "${SECRETS[SUPABASE_SERVICE_ROLE_KEY]:-}" ] && echo "  👤 Admin panel functionality"
    echo
    print_info "💡 Add missing secrets anytime with:"
    echo "   gh secret set SECRET_NAME --repo $REPO_NAME"
fi

echo
print_header "Next Steps:"
echo "1. 🚀 Push to main branch to trigger deployment"
echo "2. 🔍 Monitor progress in GitHub Actions tab"
echo "3. 🌐 Check your live site on Vercel"
echo "4. 📧 Test functionality and add missing secrets as needed"
echo
print_status "Your MVP is ready to deploy! 🚀"
