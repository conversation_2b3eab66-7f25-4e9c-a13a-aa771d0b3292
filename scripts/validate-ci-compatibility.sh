#!/bin/bash

# 🔍 CI Compatibility Validation Script - Vintage Marketing Portugal
# This script simulates the exact CI environment to catch issues before push

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() { echo -e "${GREEN}✅ $1${NC}"; }
print_warning() { echo -e "${YELLOW}⚠️  $1${NC}"; }
print_error() { echo -e "${RED}❌ $1${NC}"; }
print_info() { echo -e "${BLUE}ℹ️  $1${NC}"; }
print_header() { echo -e "${PURPLE}🔧 $1${NC}"; }
print_step() { echo -e "${CYAN}📋 $1${NC}"; }

# Track validation results
VALIDATION_ERRORS=0
START_TIME=$(date +%s)

echo "🔍 CI Compatibility Validation - Vintage Marketing Portugal"
echo "=========================================================="
echo

print_header "Simulating CI Environment Locally"
echo "This script runs the exact same checks as the GitHub Actions workflow"
echo

# Step 1: Verify package-lock.json exists and is up to date
print_step "Step 1: Verifying package-lock.json integrity"
if [ ! -f "package-lock.json" ]; then
    print_error "package-lock.json not found!"
    print_info "Run 'npm install' to generate it"
    exit 1
fi

# Check if package-lock.json is newer than package.json
if [ "package.json" -nt "package-lock.json" ]; then
    print_warning "package.json is newer than package-lock.json"
    print_info "This might cause npm ci to fail. Consider running 'npm install' first."
fi

print_status "package-lock.json exists and appears current"
echo

# Step 2: Clean install (simulate CI environment)
print_step "Step 2: Clean dependency installation (npm ci)"
print_info "Removing node_modules to simulate fresh CI environment..."

if [ -d "node_modules" ]; then
    rm -rf node_modules
    print_info "Removed existing node_modules"
fi

print_info "Running npm ci (exactly as CI does)..."
if npm ci; then
    print_status "Dependencies installed successfully with npm ci"
else
    print_error "npm ci failed - this will cause CI/CD pipeline to fail!"
    print_info "Common fixes:"
    print_info "1. Run 'npm install' to update package-lock.json"
    print_info "2. Check for peer dependency conflicts"
    print_info "3. Verify React version compatibility"
    VALIDATION_ERRORS=$((VALIDATION_ERRORS + 1))
fi
echo

# Step 3: TypeScript compilation check
print_step "Step 3: TypeScript type checking"
print_info "Running npm run type-check..."
if npm run type-check; then
    print_status "TypeScript compilation successful"
else
    print_error "TypeScript compilation failed!"
    VALIDATION_ERRORS=$((VALIDATION_ERRORS + 1))
fi
echo

# Step 4: ESLint code quality check
print_step "Step 4: ESLint code quality validation"
print_info "Running npm run lint..."
if npm run lint; then
    print_status "ESLint validation passed"
else
    print_error "ESLint validation failed!"
    print_info "Run 'npm run lint:fix' to auto-fix issues"
    VALIDATION_ERRORS=$((VALIDATION_ERRORS + 1))
fi
echo

# Step 5: Prettier code formatting check
print_step "Step 5: Prettier code formatting validation"
print_info "Running npm run format:check..."
if npm run format:check; then
    print_status "Code formatting is correct"
else
    print_error "Code formatting issues found!"
    print_info "Run 'npm run format' to fix formatting"
    VALIDATION_ERRORS=$((VALIDATION_ERRORS + 1))
fi
echo

# Step 6: Jest test suite
print_step "Step 6: Jest test suite execution"
print_info "Running npm test..."
if npm test -- --watchAll=false --passWithNoTests; then
    print_status "All tests passed"
else
    print_error "Tests failed!"
    VALIDATION_ERRORS=$((VALIDATION_ERRORS + 1))
fi
echo

# Step 7: Next.js build verification
print_step "Step 7: Next.js build verification"
print_info "Running npm run build..."
if npm run build; then
    print_status "Next.js build successful"
    
    # Verify build output
    if [ -d ".next" ]; then
        print_status "Build output directory (.next) created successfully"
    else
        print_error "Build output directory (.next) not found!"
        VALIDATION_ERRORS=$((VALIDATION_ERRORS + 1))
    fi
else
    print_error "Next.js build failed!"
    VALIDATION_ERRORS=$((VALIDATION_ERRORS + 1))
fi
echo

# Step 8: Dependency audit (security check)
print_step "Step 8: Security audit (npm audit)"
print_info "Running npm audit..."
if npm audit --audit-level=high; then
    print_status "No high or critical security vulnerabilities found"
else
    print_warning "Security vulnerabilities detected"
    print_info "Review npm audit output and update dependencies as needed"
    # Don't fail validation for audit issues, just warn
fi
echo

# Calculate execution time
END_TIME=$(date +%s)
EXECUTION_TIME=$((END_TIME - START_TIME))

# Final results
print_header "Validation Results Summary"
echo "Execution time: ${EXECUTION_TIME}s"
echo

if [ $VALIDATION_ERRORS -eq 0 ]; then
    print_status "🎉 All validations passed! CI/CD pipeline should succeed."
    echo
    print_info "Your code is ready to push:"
    echo "  git add ."
    echo "  git commit -m 'your commit message'"
    echo "  git push origin main"
    echo
    exit 0
else
    print_error "❌ $VALIDATION_ERRORS validation(s) failed!"
    echo
    print_info "Fix the issues above before pushing to prevent CI/CD failures"
    echo
    print_header "Common fixes:"
    echo "• Dependencies: npm install && npm ci"
    echo "• TypeScript: Check type errors and fix them"
    echo "• Linting: npm run lint:fix"
    echo "• Formatting: npm run format"
    echo "• Tests: Fix failing tests"
    echo "• Build: Check build errors and resolve them"
    echo
    exit 1
fi
