#!/bin/bash

# 🚀 Vintage Marketing Portugal - MVP Deployment Setup Script
# This script automates the setup for CI/CD pipeline with essential secrets first

set -e

echo "🎯 Vintage Marketing Portugal - MVP Deployment Setup"
echo "===================================================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

print_header() {
    echo -e "${PURPLE}🔧 $1${NC}"
}

print_instruction() {
    echo -e "${CYAN}📋 $1${NC}"
}

# Global variables for secrets
declare -A ESSENTIAL_SECRETS
declare -A OPTIONAL_SECRETS
GITHUB_CLI_AVAILABLE=false
REPO_NAME=""

# Check if required tools are installed
check_prerequisites() {
    print_info "Checking prerequisites..."

    # Check Node.js
    if ! command -v node &> /dev/null; then
        print_error "Node.js is not installed. Please install Node.js 18+ first."
        exit 1
    fi

    # Check npm
    if ! command -v npm &> /dev/null; then
        print_error "npm is not installed. Please install npm first."
        exit 1
    fi

    # Check git
    if ! command -v git &> /dev/null; then
        print_error "Git is not installed. Please install Git first."
        exit 1
    fi

    # Check GitHub CLI (optional)
    if command -v gh &> /dev/null; then
        GITHUB_CLI_AVAILABLE=true
        print_status "GitHub CLI is available - will use automated secret setup"
    else
        print_warning "GitHub CLI not found - will provide manual instructions"
    fi

    # Get repository name
    REPO_NAME=$(git remote get-url origin | sed 's/.*github\.com[:/]\([^/]*\/[^/]*\)\.git/\1/' | sed 's/\.git$//')
    if [ -z "$REPO_NAME" ]; then
        print_error "Could not determine GitHub repository name"
        exit 1
    fi

    print_status "Repository: $REPO_NAME"
    print_status "All prerequisites checked"
}

# Install dependencies
install_dependencies() {
    print_info "Installing dependencies..."
    npm ci
    print_status "Dependencies installed successfully"
}

# Validate secret input
validate_secret() {
    local secret_name="$1"
    local secret_value="$2"
    local is_essential="$3"

    if [ -z "$secret_value" ] && [ "$is_essential" = "true" ]; then
        print_error "Essential secret '$secret_name' cannot be empty"
        return 1
    fi

    # Basic validation for specific secrets
    case "$secret_name" in
        "NEXT_PUBLIC_SUPABASE_URL")
            if [[ ! "$secret_value" =~ ^https://.*\.supabase\.co$ ]]; then
                print_warning "Supabase URL should match format: https://your-project.supabase.co"
            fi
            ;;
        "VERCEL_TOKEN")
            if [ ${#secret_value} -lt 20 ]; then
                print_warning "Vercel token seems too short. Make sure it's a valid token."
            fi
            ;;
        "NEXTAUTH_SECRET")
            if [ ${#secret_value} -lt 32 ]; then
                print_warning "NextAuth secret should be at least 32 characters long"
            fi
            ;;
    esac

    return 0
}

# Collect essential secrets for MVP
collect_essential_secrets() {
    print_header "Essential Secrets for MVP Deployment"
    echo "These secrets are required for basic functionality:"
    echo

    # Supabase URL
    print_instruction "1. NEXT_PUBLIC_SUPABASE_URL"
    echo "   📍 Find this in: Supabase Dashboard → Settings → API"
    echo "   📝 Format: https://your-project.supabase.co"
    echo
    read -p "Enter your Supabase URL: " supabase_url
    validate_secret "NEXT_PUBLIC_SUPABASE_URL" "$supabase_url" "true" || return 1
    ESSENTIAL_SECRETS["NEXT_PUBLIC_SUPABASE_URL"]="$supabase_url"

    # Supabase Anon Key
    print_instruction "2. NEXT_PUBLIC_SUPABASE_ANON_KEY"
    echo "   📍 Find this in: Supabase Dashboard → Settings → API"
    echo "   📝 This is the 'anon public' key (safe to expose)"
    echo
    read -p "Enter your Supabase Anon Key: " supabase_anon
    validate_secret "NEXT_PUBLIC_SUPABASE_ANON_KEY" "$supabase_anon" "true" || return 1
    ESSENTIAL_SECRETS["NEXT_PUBLIC_SUPABASE_ANON_KEY"]="$supabase_anon"

    # NextAuth Secret
    print_instruction "3. NEXTAUTH_SECRET"
    echo "   📍 Generate with: openssl rand -base64 32"
    echo "   📝 Or use any secure random string (32+ characters)"
    echo
    read -p "Enter NextAuth Secret (or press Enter to generate): " nextauth_secret
    if [ -z "$nextauth_secret" ]; then
        if command -v openssl &> /dev/null; then
            nextauth_secret=$(openssl rand -base64 32)
            print_status "Generated NextAuth secret automatically"
        else
            nextauth_secret=$(date +%s | sha256sum | base64 | head -c 32)
            print_status "Generated NextAuth secret using fallback method"
        fi
    fi
    validate_secret "NEXTAUTH_SECRET" "$nextauth_secret" "true" || return 1
    ESSENTIAL_SECRETS["NEXTAUTH_SECRET"]="$nextauth_secret"

    print_status "Essential secrets collected successfully"
}

# Collect Vercel secrets
collect_vercel_secrets() {
    print_header "Vercel Deployment Secrets"
    echo "These are required for automated deployment:"
    echo

    # Check if Vercel CLI is available
    if ! command -v vercel &> /dev/null; then
        print_warning "Vercel CLI not found. Installing..."
        npm install -g vercel
    fi

    print_instruction "Setting up Vercel project..."
    echo "1. First, let's log in to Vercel and link the project"
    echo

    # Vercel login and link
    vercel login
    vercel link

    print_instruction "Now we need to get your Vercel IDs:"
    echo

    # Get Vercel Token
    print_instruction "1. VERCEL_TOKEN"
    echo "   📍 Get this from: https://vercel.com/account/tokens"
    echo "   📝 Create a new token with appropriate scope"
    echo
    read -p "Enter your Vercel Token: " vercel_token
    validate_secret "VERCEL_TOKEN" "$vercel_token" "true" || return 1
    ESSENTIAL_SECRETS["VERCEL_TOKEN"]="$vercel_token"

    # Get Organization ID
    print_instruction "2. VERCEL_ORG_ID"
    echo "   📍 Run: vercel teams list"
    echo "   📝 Copy the ID of your organization/team"
    echo
    vercel teams list
    echo
    read -p "Enter your Vercel Org ID: " vercel_org_id
    validate_secret "VERCEL_ORG_ID" "$vercel_org_id" "true" || return 1
    ESSENTIAL_SECRETS["VERCEL_ORG_ID"]="$vercel_org_id"

    # Get Project ID
    print_instruction "3. VERCEL_PROJECT_ID"
    echo "   📍 Run: vercel projects list"
    echo "   📝 Copy the ID of your project"
    echo
    vercel projects list
    echo
    read -p "Enter your Vercel Project ID: " vercel_project_id
    validate_secret "VERCEL_PROJECT_ID" "$vercel_project_id" "true" || return 1
    ESSENTIAL_SECRETS["VERCEL_PROJECT_ID"]="$vercel_project_id"

    print_status "Vercel secrets collected successfully"
}

# Collect optional secrets
collect_optional_secrets() {
    print_header "Optional Secrets (Can be added later)"
    echo "These secrets enable additional functionality:"
    echo

    read -p "Do you want to configure optional secrets now? (y/n): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        print_warning "Skipping optional secrets - you can add these later"
        return 0
    fi

    # Resend API Key
    print_instruction "1. RESEND_API_KEY (for contact forms)"
    echo "   📍 Get this from: https://resend.com/api-keys"
    echo "   📝 Required for email functionality"
    echo
    read -p "Enter Resend API Key (or press Enter to skip): " resend_key
    if [ -n "$resend_key" ]; then
        OPTIONAL_SECRETS["RESEND_API_KEY"]="$resend_key"
        print_status "Resend API key added"
    else
        print_warning "Skipping Resend API key - contact forms won't work"
    fi

    # Supabase Service Role Key
    print_instruction "2. SUPABASE_SERVICE_ROLE_KEY (for admin features)"
    echo "   📍 Find this in: Supabase Dashboard → Settings → API"
    echo "   📝 This is the 'service_role' key (keep secret!)"
    echo
    read -p "Enter Supabase Service Role Key (or press Enter to skip): " supabase_service
    if [ -n "$supabase_service" ]; then
        OPTIONAL_SECRETS["SUPABASE_SERVICE_ROLE_KEY"]="$supabase_service"
        print_status "Supabase service role key added"
    else
        print_warning "Skipping service role key - admin features won't work"
    fi

    # NextAuth URL
    print_instruction "3. NEXTAUTH_URL (production domain)"
    echo "   📍 Your custom domain or Vercel domain"
    echo "   📝 Can use Vercel default initially"
    echo
    read -p "Enter production URL (or press Enter to use Vercel default): " nextauth_url
    if [ -n "$nextauth_url" ]; then
        OPTIONAL_SECRETS["NEXTAUTH_URL"]="$nextauth_url"
        print_status "NextAuth URL added"
    else
        print_warning "Using Vercel default domain"
    fi
}

# Set up GitHub secrets using CLI
setup_github_secrets_cli() {
    print_header "Setting up GitHub Secrets (Automated)"

    # Check if user is logged in to GitHub CLI
    if ! gh auth status &> /dev/null; then
        print_info "Logging in to GitHub CLI..."
        gh auth login
    fi

    print_info "Setting essential secrets..."

    # Set essential secrets
    for secret_name in "${!ESSENTIAL_SECRETS[@]}"; do
        echo "${ESSENTIAL_SECRETS[$secret_name]}" | gh secret set "$secret_name" --repo "$REPO_NAME"
        print_status "Set $secret_name"
    done

    # Set optional secrets
    if [ ${#OPTIONAL_SECRETS[@]} -gt 0 ]; then
        print_info "Setting optional secrets..."
        for secret_name in "${!OPTIONAL_SECRETS[@]}"; do
            echo "${OPTIONAL_SECRETS[$secret_name]}" | gh secret set "$secret_name" --repo "$REPO_NAME"
            print_status "Set $secret_name"
        done
    fi

    print_status "All secrets configured in GitHub repository"
}

# Provide manual instructions for GitHub secrets
setup_github_secrets_manual() {
    print_header "Manual GitHub Secrets Setup"
    print_warning "GitHub CLI not available - please set secrets manually"
    echo
    print_instruction "Go to: https://github.com/$REPO_NAME/settings/secrets/actions"
    echo
    print_info "Essential secrets to add:"

    for secret_name in "${!ESSENTIAL_SECRETS[@]}"; do
        echo "  🔑 $secret_name"
        echo "     Value: ${ESSENTIAL_SECRETS[$secret_name]}"
        echo
    done

    if [ ${#OPTIONAL_SECRETS[@]} -gt 0 ]; then
        print_info "Optional secrets to add:"
        for secret_name in "${!OPTIONAL_SECRETS[@]}"; do
            echo "  🔑 $secret_name"
            echo "     Value: ${OPTIONAL_SECRETS[$secret_name]}"
            echo
        done
    fi

    echo "Steps:"
    echo "1. Click 'New repository secret'"
    echo "2. Enter the secret name exactly as shown"
    echo "3. Paste the corresponding value"
    echo "4. Click 'Add secret'"
    echo "5. Repeat for all secrets"
    echo
    read -p "Press Enter when you've added all secrets..."
}

# Set up GitHub secrets (automated or manual)
setup_github_secrets() {
    if [ "$GITHUB_CLI_AVAILABLE" = true ]; then
        setup_github_secrets_cli
    else
        setup_github_secrets_manual
    fi
}

# Show functionality summary
show_functionality_summary() {
    print_header "Functionality Summary"
    echo

    print_status "✅ WORKING FEATURES (with current secrets):"
    echo "  🌐 Website deployment and hosting"
    echo "  📱 Responsive design and animations"
    echo "  🚗 Vehicle display and browsing"
    echo "  📋 Basic reservation form (UI only)"
    echo "  🔍 SEO optimization and Portuguese content"
    echo "  🔒 Basic authentication setup"
    echo

    # Check what's missing
    local missing_features=()

    if [ -z "${OPTIONAL_SECRETS[RESEND_API_KEY]:-}" ]; then
        missing_features+=("📧 Contact form email delivery")
    fi

    if [ -z "${OPTIONAL_SECRETS[SUPABASE_SERVICE_ROLE_KEY]:-}" ]; then
        missing_features+=("👤 Admin panel functionality")
        missing_features+=("📊 Database write operations")
    fi

    if [ -z "${OPTIONAL_SECRETS[NEXTAUTH_URL]:-}" ]; then
        missing_features+=("🔗 Custom domain authentication")
    fi

    if [ ${#missing_features[@]} -gt 0 ]; then
        print_warning "⚠️  LIMITED FEATURES (missing optional secrets):"
        for feature in "${missing_features[@]}"; do
            echo "  $feature"
        done
        echo
        print_info "💡 You can add these secrets later to enable full functionality"
    else
        print_status "🎉 ALL FEATURES ENABLED!"
    fi
}

# Setup environment file
setup_environment_file() {
    print_info "Setting up local environment file..."

    if [ ! -f ".env.local" ]; then
        if [ -f ".env.local.example" ]; then
            cp .env.local.example .env.local
            print_status "Created .env.local from example"
        else
            print_warning ".env.local.example not found"
        fi
    fi

    # Update .env.local with collected secrets
    if [ -f ".env.local" ]; then
        # Backup existing file
        cp .env.local .env.local.backup

        # Update with new values
        for secret_name in "${!ESSENTIAL_SECRETS[@]}"; do
            if grep -q "^$secret_name=" .env.local; then
                sed -i.bak "s|^$secret_name=.*|$secret_name=${ESSENTIAL_SECRETS[$secret_name]}|" .env.local
            else
                echo "$secret_name=${ESSENTIAL_SECRETS[$secret_name]}" >> .env.local
            fi
        done

        for secret_name in "${!OPTIONAL_SECRETS[@]}"; do
            if grep -q "^$secret_name=" .env.local; then
                sed -i.bak "s|^$secret_name=.*|$secret_name=${OPTIONAL_SECRETS[$secret_name]}|" .env.local
            else
                echo "$secret_name=${OPTIONAL_SECRETS[$secret_name]}" >> .env.local
            fi
        done

        # Clean up backup files
        rm -f .env.local.bak

        print_status "Updated .env.local with your secrets"
    fi
}

# Run tests to verify setup
run_tests() {
    print_info "Running tests to verify setup..."

    # Type checking
    print_info "Running TypeScript check..."
    npm run type-check

    # Linting
    print_info "Running ESLint..."
    npm run lint

    # Tests
    print_info "Running Jest tests..."
    npm test -- --watchAll=false --passWithNoTests

    print_status "All tests passed successfully"
}

# Setup Husky (optional)
setup_husky() {
    read -p "Do you want to set up pre-commit hooks with Husky? (y/n): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        print_info "Setting up Husky..."
        npm install --save-dev husky
        npx husky-init
        chmod +x .husky/pre-commit
        print_status "Husky pre-commit hooks configured"
    else
        print_warning "Skipping Husky setup"
    fi
}

# Setup Vercel environment variables
setup_vercel_environment() {
    print_header "Setting up Vercel Environment Variables"

    read -p "Do you want to set up Vercel environment variables now? (y/n): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        print_warning "Skipping Vercel environment setup"
        print_info "Remember to add environment variables in Vercel dashboard later"
        return 0
    fi

    print_info "Setting environment variables in Vercel..."

    # Set essential secrets in Vercel
    for secret_name in "${!ESSENTIAL_SECRETS[@]}"; do
        if [[ "$secret_name" != "VERCEL_"* ]]; then
            echo "${ESSENTIAL_SECRETS[$secret_name]}" | vercel env add "$secret_name" production
            print_status "Set $secret_name in Vercel production"
        fi
    done

    # Set optional secrets in Vercel
    for secret_name in "${!OPTIONAL_SECRETS[@]}"; do
        echo "${OPTIONAL_SECRETS[$secret_name]}" | vercel env add "$secret_name" production
        print_status "Set $secret_name in Vercel production"
    done

    print_status "Vercel environment variables configured"
}

# Display next steps
show_next_steps() {
    echo
    print_status "🎉 MVP Deployment Setup Completed!"
    echo "=================================="
    echo

    print_header "What happens next:"
    echo "1. � Push to main branch to trigger first deployment"
    echo "2. � Monitor deployment in GitHub Actions tab"
    echo "3. 🌐 Check your live site on Vercel"
    echo "4. 📧 Test functionality and add missing secrets as needed"
    echo

    print_header "Useful commands:"
    echo "• Test locally: npm run dev"
    echo "• Run all checks: npm run pre-commit"
    echo "• Deploy manually: npm run deploy"
    echo "• Check health: curl https://your-domain.vercel.app/api/health-check"
    echo

    print_header "Documentation:"
    echo "• 📖 Full deployment guide: DEPLOYMENT.md"
    echo "• 🔧 GitHub Actions workflow: .github/workflows/ci-cd.yml"
    echo "• ⚙️  Vercel configuration: vercel.json"
    echo "• 🚀 Quick reference: CI-CD-QUICK-REFERENCE.md"
    echo

    print_header "Adding missing secrets later:"
    if [ "$GITHUB_CLI_AVAILABLE" = true ]; then
        echo "• Use: gh secret set SECRET_NAME --repo $REPO_NAME"
    else
        echo "• Go to: https://github.com/$REPO_NAME/settings/secrets/actions"
    fi
    echo "• Update Vercel: vercel env add SECRET_NAME production"
    echo

    print_status "Your Vintage Marketing Portugal MVP is ready to deploy! 🚀"
}

# Main execution
main() {
    echo
    print_header "Starting MVP Deployment Setup..."
    echo "This script will help you set up the essential secrets needed"
    echo "to deploy your Vintage Marketing Portugal application."
    echo

    check_prerequisites
    install_dependencies
    setup_environment_file
    run_tests

    echo
    print_header "Now let's collect your secrets..."
    collect_essential_secrets
    collect_vercel_secrets
    collect_optional_secrets

    echo
    setup_github_secrets
    setup_vercel_environment
    setup_husky

    show_functionality_summary
    show_next_steps
}

# Run main function
main
