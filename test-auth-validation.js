// Test script to demonstrate improved authentication validation
const { createClient } = require('@supabase/supabase-js');
require('dotenv').config({ path: '.env.local' });

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;

console.log('🧪 Testing Authentication Validation System...\n');

if (!supabaseUrl || !supabaseAnonKey) {
  console.error('❌ Missing Supabase credentials in .env.local');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseAnonKey);

// Import the error translation function (simulated)
function translateAuthError(error) {
  if (!error) {
    return {
      message: 'Erro desconhecido',
      type: 'auth',
    };
  }

  const errorMessage = error.message?.toLowerCase() || '';

  if (errorMessage.includes('invalid login credentials')) {
    return {
      message:
        'Email ou palavra-passe incorretos. Verifique os seus dados e tente novamente.',
      type: 'auth',
    };
  }

  if (errorMessage.includes('too many requests')) {
    return {
      message:
        'Demasiadas tentativas de login. Aguarde alguns minutos antes de tentar novamente.',
      type: 'auth',
    };
  }

  return {
    message: 'Erro de autenticação. Tente novamente ou contacte o suporte.',
    type: 'auth',
  };
}

async function testAuthValidation() {
  console.log('📋 Testing different authentication scenarios:\n');

  // Test 1: Invalid email format
  console.log('1️⃣ Testing invalid email format...');
  try {
    const { error } = await supabase.auth.signInWithPassword({
      email: 'invalid-email',
      password: 'password123',
    });

    if (error) {
      const translated = translateAuthError(error);
      console.log('   ❌ Error:', translated.message);
      console.log('   📝 Type:', translated.type);
    }
  } catch (e) {
    console.log('   ❌ Caught error:', e.message);
  }

  console.log('');

  // Test 2: Non-existent email
  console.log('2️⃣ Testing non-existent email...');
  try {
    const { error } = await supabase.auth.signInWithPassword({
      email: '<EMAIL>',
      password: 'password123',
    });

    if (error) {
      const translated = translateAuthError(error);
      console.log('   ❌ Error:', translated.message);
      console.log('   📝 Type:', translated.type);
      console.log('   🔍 Original error:', error.message);
    }
  } catch (e) {
    console.log('   ❌ Caught error:', e.message);
  }

  console.log('');

  // Test 3: Empty credentials
  console.log('3️⃣ Testing empty credentials...');
  try {
    const { error } = await supabase.auth.signInWithPassword({
      email: '',
      password: '',
    });

    if (error) {
      const translated = translateAuthError(error);
      console.log('   ❌ Error:', translated.message);
      console.log('   📝 Type:', translated.type);
    }
  } catch (e) {
    console.log('   ❌ Caught error:', e.message);
  }

  console.log('\n✅ Authentication validation testing complete!\n');

  console.log('📝 Validation Features Implemented:');
  console.log('   ✅ Portuguese error messages');
  console.log('   ✅ Client-side form validation');
  console.log('   ✅ Field-specific error display');
  console.log('   ✅ Real-time validation feedback');
  console.log('   ✅ Improved error categorization');
  console.log('   ✅ User-friendly error descriptions');
  console.log('   ✅ Loading states and disabled inputs');
  console.log('   ✅ Admin role verification');

  console.log('\n🎯 Test the improved login form at:');
  console.log('   http://localhost:3000/admin/login');

  console.log('\n🧪 Try these test scenarios:');
  console.log('   1. Empty email/password fields');
  console.log('   2. Invalid email format (e.g., "invalid-email")');
  console.log('   3. Non-existent email (e.g., "<EMAIL>")');
  console.log('   4. Valid email with wrong password');
  console.log('   5. Valid credentials but non-admin user');
}

testAuthValidation();
