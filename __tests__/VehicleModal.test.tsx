import {
  render,
  screen,
  fireEvent,
  act,
  waitFor,
} from '@testing-library/react';
import '@testing-library/jest-dom';
import { useRouter } from 'next/navigation';
import VehicleModal from '@/components/VehicleModal';
import { Vehicle } from '@/types';

// Mock Next.js router
jest.mock('next/navigation', () => ({
  useRouter: jest.fn(),
}));

const mockPush = jest.fn();
const mockRouter = {
  push: mockPush,
  replace: jest.fn(),
  back: jest.fn(),
  forward: jest.fn(),
  refresh: jest.fn(),
  prefetch: jest.fn(),
};

(useRouter as jest.Mock).mockReturnValue(mockRouter);

const mockVehicle: Vehicle = {
  id: 'test-vehicle-456',
  name: 'Test Modal Vehicle',
  year: 1970,
  description: 'A vintage vehicle for modal testing',
  price: 200,
  photo_url: 'https://example.com/modal-car.jpg',
  availability: null,
  created_at: '2024-01-01T00:00:00Z',
  updated_at: '2024-01-01T00:00:00Z',
};

describe('VehicleModal Navigation Fix', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('should use router.push instead of window.location.href for "Reservar Este Veículo" button', async () => {
    const mockOnClose = jest.fn();

    // Wrap render in act() to handle Headless UI Transition state updates
    await act(async () => {
      render(
        <VehicleModal
          vehicle={mockVehicle}
          isOpen={true}
          onClose={mockOnClose}
        />
      );
    });

    // Wait for any transition state updates to complete
    await waitFor(() => {
      const reservarButton = screen.getByText('Reservar Este Veículo');
      expect(reservarButton).toBeInTheDocument();
    });

    // Find the "Reservar Este Veículo" button
    const reservarButton = screen.getByText('Reservar Este Veículo');

    // Click the button
    await act(async () => {
      fireEvent.click(reservarButton);
    });

    // Verify that router.push was called with the correct URL
    expect(mockPush).toHaveBeenCalledWith('/reservas?vehicle=test-vehicle-456');
    expect(mockPush).toHaveBeenCalledTimes(1);
  });

  it('should not render when vehicle is null', async () => {
    const mockOnClose = jest.fn();

    // Wrap render in act() even for null vehicle case to be consistent
    let container: any;
    await act(async () => {
      const result = render(
        <VehicleModal vehicle={null} isOpen={true} onClose={mockOnClose} />
      );
      container = result.container;
    });

    // Modal should not render when vehicle is null
    expect(container.firstChild).toBeNull();
    expect(mockPush).not.toHaveBeenCalled();
  });

  it('should not render when isOpen is false', async () => {
    const mockOnClose = jest.fn();

    // Wrap render in act() to handle any potential state updates
    await act(async () => {
      render(
        <VehicleModal
          vehicle={mockVehicle}
          isOpen={false}
          onClose={mockOnClose}
        />
      );
    });

    // Wait for any potential state updates to complete
    await waitFor(() => {
      const reservarButton = screen.queryByText('Reservar Este Veículo');
      expect(reservarButton).not.toBeInTheDocument();
    });
  });
});
