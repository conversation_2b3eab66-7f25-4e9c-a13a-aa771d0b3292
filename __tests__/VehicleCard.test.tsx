import { render, screen, fireEvent } from '@testing-library/react';
import '@testing-library/jest-dom';
import { useRouter } from 'next/navigation';
import VehicleCard from '@/components/VehicleCard';
import { Vehicle } from '@/types';

// Mock Next.js router
jest.mock('next/navigation', () => ({
  useRouter: jest.fn(),
}));

// Mock the animations component
jest.mock('@/components/animations', () => ({
  HoverCard: ({
    children,
    className,
  }: {
    children: React.ReactNode;
    className?: string;
  }) => <div className={className}>{children}</div>,
}));

const mockPush = jest.fn();
const mockRouter = {
  push: mockPush,
  replace: jest.fn(),
  back: jest.fn(),
  forward: jest.fn(),
  refresh: jest.fn(),
  prefetch: jest.fn(),
};

(useRouter as jest.Mock).mockReturnValue(mockRouter);

const mockVehicle: Vehicle = {
  id: 'test-vehicle-123',
  name: 'Test Vintage Car',
  year: 1965,
  description: 'A beautiful vintage car for testing',
  price: 150,
  photo_url: 'https://example.com/car.jpg',
  availability: null,
  created_at: '2024-01-01T00:00:00Z',
  updated_at: '2024-01-01T00:00:00Z',
};

describe('VehicleCard Navigation Fix', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('should use router.push instead of window.location.href for "Reservar Agora" button', () => {
    const mockOnClick = jest.fn();

    render(
      <div onClick={mockOnClick}>
        <VehicleCard vehicle={mockVehicle} />
      </div>
    );

    // Find the "Reservar Agora" button
    const reservarButton = screen.getByText('Reservar Agora');
    expect(reservarButton).toBeInTheDocument();

    // Click the button
    fireEvent.click(reservarButton);

    // Verify that router.push was called with the correct URL
    expect(mockPush).toHaveBeenCalledWith('/reservas?vehicle=test-vehicle-123');
    expect(mockPush).toHaveBeenCalledTimes(1);
  });

  it('should prevent event propagation when clicking "Reservar Agora" button', () => {
    const mockOnClick = jest.fn();

    render(
      <div onClick={mockOnClick}>
        <VehicleCard vehicle={mockVehicle} />
      </div>
    );

    const reservarButton = screen.getByText('Reservar Agora');
    fireEvent.click(reservarButton);

    // The card's onClick should not be called due to stopPropagation
    expect(mockOnClick).not.toHaveBeenCalled();

    // But router.push should still be called
    expect(mockPush).toHaveBeenCalledWith('/reservas?vehicle=test-vehicle-123');
  });

  it('should call card onClick when clicking other parts of the card', () => {
    const mockOnClick = jest.fn();

    render(
      <div onClick={mockOnClick}>
        <VehicleCard vehicle={mockVehicle} />
      </div>
    );

    // Click on the vehicle name (which should trigger the card onClick)
    const vehicleName = screen.getByText('Test Vintage Car');
    fireEvent.click(vehicleName);

    // The card's onClick should be called
    expect(mockOnClick).toHaveBeenCalledTimes(1);

    // router.push should not be called for card clicks
    expect(mockPush).not.toHaveBeenCalled();
  });
});
