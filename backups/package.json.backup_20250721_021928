{"name": "vintage-marketing-portugal", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "lint:fix": "next lint --fix", "type-check": "tsc --noEmit", "format": "prettier --write .", "format:check": "prettier --check .", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "verify-task": "node scripts/verify-task.js", "pre-commit": "npm run type-check && npm run lint && npm run format:check && npm run test", "validate-ci": "./scripts/validate-ci-compatibility.sh", "pre-push": "./scripts/validate-ci-compatibility.sh", "deploy": "vercel --prod", "deploy:preview": "vercel"}, "dependencies": {"@headlessui/react": "^2.2.4", "@heroicons/react": "^2.0.18", "@supabase/supabase-js": "^2.39.0", "date-fns": "^3.0.0", "dotenv": "^17.2.0", "motion": "^12.23.6", "next": "^15.0.0", "react": "^19.1.0", "react-datepicker": "^7.5.0", "react-dom": "^19.1.0", "resend": "^3.2.0"}, "devDependencies": {"@eslint/eslintrc": "^3.3.1", "@eslint/js": "^9.31.0", "@testing-library/jest-dom": "^6.0.0", "@testing-library/react": "^16.0.0", "@types/jest": "^30.0.0", "@types/node": "^20.0.0", "@types/react": "^19.0.0", "@types/react-dom": "^19.0.0", "autoprefixer": "^10.4.0", "eslint": "^9.31.0", "eslint-config-next": "^15.0.0", "jest": "^30.0.4", "jest-environment-jsdom": "^30.0.4", "postcss": "^8.4.0", "prettier": "^3.0.0", "tailwindcss": "^3.4.0", "typescript": "^5.0.0"}, "overrides": {"glob": "^10.4.5", "rimraf": "^5.0.10", "test-exclude": "^7.0.1", "babel-plugin-istanbul": "^7.0.0", "inflight": "npm:@isaacs/inflight@^1.0.6", "osenv": "npm:@npmcli/config@^8.3.4", "npmlog": "npm:proc-log@^4.2.0", "gauge": "npm:@npmcli/arborist@^8.0.0", "are-we-there-yet": "npm:@npmcli/arborist@^8.0.0", "node-pre-gyp": "npm:@mapbox/node-pre-gyp@^1.0.11"}}