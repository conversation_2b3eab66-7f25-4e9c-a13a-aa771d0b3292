// Test script to create admin user and test authentication
const { createClient } = require('@supabase/supabase-js');
require('dotenv').config({ path: '.env.local' });

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;

console.log('Testing Admin Authentication System...');
console.log('Supabase URL:', supabaseUrl);
console.log('Supabase Key:', supabaseAnonKey ? 'Present' : 'Missing');

if (!supabaseUrl || !supabaseAnonKey) {
  console.error('❌ Missing Supabase credentials in .env.local');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseAnonKey);

async function testAdminAuth() {
  try {
    console.log('\n🔍 Testing Supabase connection...');

    // Test basic connection by fetching vehicles
    const { data: vehicles, error: vehiclesError } = await supabase
      .from('vehicles')
      .select('*')
      .limit(1);

    if (vehiclesError) {
      console.error('❌ Error connecting to Supabase:', vehiclesError.message);
      return;
    }

    console.log('✅ Supabase connection successful');
    console.log(`📊 Found ${vehicles?.length || 0} vehicles in database`);

    // Test authentication endpoints
    console.log('\n🔐 Testing authentication system...');

    // Try to get current session (should be null for this script)
    const {
      data: { session },
      error: sessionError,
    } = await supabase.auth.getSession();

    if (sessionError) {
      console.error('❌ Error getting session:', sessionError.message);
      return;
    }

    console.log('✅ Auth system accessible');
    console.log(
      'Current session:',
      session ? 'Active' : 'None (expected for this test)'
    );

    // Test bookings access (should work for public read)
    const { data: bookings, error: bookingsError } = await supabase
      .from('bookings')
      .select('*')
      .limit(1);

    if (bookingsError) {
      console.log(
        'ℹ️  Bookings access restricted (expected for non-admin):',
        bookingsError.message
      );
    } else {
      console.log('📋 Bookings accessible:', bookings?.length || 0, 'found');
    }

    console.log('\n✅ Admin authentication system is properly configured!');
    console.log('\n📝 Next steps:');
    console.log('1. Create an admin user in Supabase Auth dashboard');
    console.log('2. Set user_metadata.role = "admin" for the admin user');
    console.log('3. Test login at http://localhost:3000/admin/login');
    console.log(
      '4. Access admin dashboard at http://localhost:3000/admin/dashboard'
    );
  } catch (error) {
    console.error('❌ Unexpected error:', error.message);
  }
}

// Instructions for creating admin user
console.log('\n📋 Instructions for creating admin user:');
console.log('1. Go to your Supabase project dashboard');
console.log('2. Navigate to Authentication > Users');
console.log('3. Click "Add user" or "Invite user"');
console.log('4. Enter email and password');
console.log('5. After creating user, click on the user to edit');
console.log('6. In "User Metadata" section, add:');
console.log('   {"role": "admin"}');
console.log('7. Save the changes');
console.log('8. The user can now login to admin panel');

testAdminAuth();
