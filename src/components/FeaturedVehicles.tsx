'use client';

import { useState, useEffect } from 'react';
import { Vehicle } from '@/types';
import { supabase } from '@/lib/supabase';
import VehicleCard from './VehicleCard';
import VehicleCarousel from './VehicleCarousel';
import VehicleModal from './VehicleModal';
import {
  ScrollReveal,
  TextReveal,
  AnimatedButton,
  HoverCard as HoverCardEnhanced,
} from '@/components/animations';

export default function FeaturedVehicles() {
  const [vehicles, setVehicles] = useState<Vehicle[]>([]);
  const [loading, setLoading] = useState(true);
  const [selectedVehicle, setSelectedVehicle] = useState<Vehicle | null>(null);
  const [isModalOpen, setIsModalOpen] = useState(false);

  useEffect(() => {
    fetchVehicles();
  }, []);

  const fetchVehicles = async () => {
    try {
      setLoading(true);

      if (!supabase) {
        setLoading(false);
        return;
      }

      const { data, error } = await supabase
        .from('vehicles')
        .select('*')
        .order('created_at', { ascending: true })
        .limit(2); // Only show first 2 vehicles on homepage

      if (error) {
        throw error;
      }

      setVehicles(data || []);
    } catch (err) {
      console.error('Error fetching vehicles:', err);
    } finally {
      setLoading(false);
    }
  };

  const handleVehicleClick = (vehicle: Vehicle) => {
    setSelectedVehicle(vehicle);
    setIsModalOpen(true);
  };

  const closeModal = () => {
    setIsModalOpen(false);
    setSelectedVehicle(null);
  };

  if (loading) {
    return (
      <section className="section-light">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <ScrollReveal className="text-center mb-16">
            <TextReveal className="font-heading text-section font-section text-primary-black tracking-section mb-6">
              Os Nossos Veículos
            </TextReveal>
            <TextReveal
              delay={0.2}
              className="text-body text-primary-mediumGray leading-body max-w-3xl mx-auto"
            >
              Conheça os nossos veículos cuidadosamente selecionados , cada um
              com a sua própria história e charme único.
            </TextReveal>
          </ScrollReveal>
          <ScrollReveal delay={0.4} className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-black mx-auto mb-4"></div>
            <p className="text-primary-mediumGray">A carregar veículos...</p>
          </ScrollReveal>
        </div>
      </section>
    );
  }

  return (
    <>
      <section className="section-light">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <ScrollReveal className="text-center mb-16">
            <TextReveal className="font-heading text-section font-section text-primary-black tracking-section mb-6">
              Os Nossos Veículos
            </TextReveal>
            <TextReveal
              delay={0.2}
              className="text-body text-primary-mediumGray leading-body max-w-3xl mx-auto"
            >
              Conheça os nossos veículos cuidadosamente selecionados , cada um
              com a sua própria história e charme único.
            </TextReveal>
          </ScrollReveal>

          {vehicles.length === 0 ? (
            <ScrollReveal delay={0.1} className="text-center py-16">
              <div className="text-primary-mediumGray mb-4">
                <svg
                  className="w-16 h-16 mx-auto mb-4"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-4m-5 0H9m0 0H5m0 0h2M7 7h10M7 11h10M7 15h10"
                  />
                </svg>
              </div>
              <h3 className="font-heading text-card font-card text-primary-black mb-4">
                Veículos em Breve
              </h3>
              <p className="text-body text-primary-mediumGray leading-body mb-8">
                Estamos a preparar a nossa frota. Volte em breve para ver os
                nossos veículos.
              </p>
              <AnimatedButton
                href="/contacto"
                variant="primary"
                className="text-lg px-8 py-4"
              >
                Contacte-nos
              </AnimatedButton>
            </ScrollReveal>
          ) : (
            <>
              {/* Desktop Grid */}
              <ScrollReveal
                delay={0.1}
                className="hidden md:grid grid-cols-1 lg:grid-cols-2 gap-12 max-w-6xl mx-auto justify-items-center"
              >
                {vehicles.map((vehicle, index) => (
                  <ScrollReveal key={vehicle.id} delay={0.2 + index * 0.1}>
                    <HoverCardEnhanced>
                      <div
                        onClick={() => handleVehicleClick(vehicle)}
                        className="cursor-pointer"
                      >
                        <VehicleCard vehicle={vehicle} />
                      </div>
                    </HoverCardEnhanced>
                  </ScrollReveal>
                ))}
              </ScrollReveal>
            </>
          )}
        </div>

        {/* Mobile Carousel - Full Width Outside Container */}
        <div className="block md:hidden">
          <VehicleCarousel
            vehicles={vehicles}
            onVehicleClick={handleVehicleClick}
            className="mb-12"
          />
        </div>
      </section>

      {/* Continue Button for Mobile - Back in Container */}
      {vehicles.length > 0 && (
        <section className="section-light pt-0">
          <div className="container mx-auto px-4 sm:px-6 lg:px-8">
            <ScrollReveal delay={0.3} className="text-center block md:hidden">
              <AnimatedButton
                href="/frota"
                variant="secondary"
                className="inline-flex items-center justify-center"
              >
                Ver Toda a Frota
              </AnimatedButton>
            </ScrollReveal>
          </div>
        </section>
      )}

      {/* Vehicle Detail Modal */}
      <VehicleModal
        vehicle={selectedVehicle}
        isOpen={isModalOpen}
        onClose={closeModal}
      />
    </>
  );
}
