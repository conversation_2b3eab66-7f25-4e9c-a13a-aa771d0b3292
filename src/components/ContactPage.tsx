'use client';

import { useState } from 'react';
import {
  supabase,
  handleSupabaseError,
  handleSupabaseSuccess,
} from '@/lib/supabase';
import { ContactFormData } from '@/types';
import { validateField } from '@/utils/validation';
import {
  ScrollReveal,
  FadeIn,
  HoverCard,
  RippleButton,
  LoadingAnimation,
} from '@/components/animations';

interface FormErrors {
  name?: string;
  email?: string;
  phone?: string;
  message?: string;
  submit?: string;
}

export default function ContactPage() {
  const [formData, setFormData] = useState<ContactFormData>({
    name: '',
    email: '',
    phone: '',
    company: '',
    subject: '',
    message: '',
  });

  const [errors, setErrors] = useState<FormErrors>({});
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isSubmitted, setIsSubmitted] = useState(false);

  const validateForm = (): boolean => {
    const newErrors: FormErrors = {};

    // Validate name
    const nameError = validateField(formData.name, {
      required: true,
      minLength: 2,
    });
    if (nameError) newErrors.name = nameError;

    // Validate email
    const emailError = validateField(formData.email, {
      required: true,
      email: true,
    });
    if (emailError) newErrors.email = emailError;

    // Validate phone (optional but if provided, must be valid)
    if (formData.phone) {
      const phoneError = validateField(formData.phone, { phone: true });
      if (phoneError) newErrors.phone = phoneError;
    }

    // Validate message
    const messageError = validateField(formData.message, {
      required: true,
      minLength: 10,
    });
    if (messageError) newErrors.message = messageError;

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    if (!supabase) {
      setErrors({
        submit: 'Erro de configuração. Tente novamente mais tarde.',
      });
      return;
    }

    setIsSubmitting(true);
    setErrors({});

    try {
      const { error } = await supabase.from('contact_inquiries').insert([
        {
          name: formData.name,
          email: formData.email,
          phone: formData.phone || null,
          company: formData.company || null,
          subject: formData.subject || null,
          message: formData.message,
          status: 'new',
        },
      ]);

      if (error) {
        throw error;
      }

      setIsSubmitted(true);
      setFormData({
        name: '',
        email: '',
        phone: '',
        company: '',
        subject: '',
        message: '',
      });
    } catch (error: any) {
      console.error('Error submitting contact form:', error);
      setErrors({
        submit:
          'Erro ao enviar mensagem. Tente novamente ou contacte-nos diretamente.',
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleInputChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>
  ) => {
    const { name, value } = e.target;
    setFormData((prev) => ({ ...prev, [name]: value }));

    // Clear error when user starts typing
    if (errors[name as keyof FormErrors]) {
      setErrors((prev) => ({ ...prev, [name]: undefined }));
    }
  };

  if (isSubmitted) {
    return (
      <div className="section-light">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="max-w-2xl mx-auto text-center">
            <div className="bg-green-50 border border-green-200 rounded-lg p-8">
              <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <svg
                  className="w-8 h-8 text-green-600"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M5 13l4 4L19 7"
                  />
                </svg>
              </div>
              <h2 className="font-heading text-section font-section text-primary-black mb-4">
                Mensagem Enviada com Sucesso!
              </h2>
              <p className="text-body text-primary-mediumGray mb-6">
                Obrigado pelo seu contacto. Responderemos à sua mensagem no
                prazo de 24 horas.
              </p>
              <button
                onClick={() => setIsSubmitted(false)}
                className="btn-secondary"
              >
                Enviar Nova Mensagem
              </button>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="section-light">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8 max-w-7xl">
        {/* Header */}
        <div className="text-center mb-16">
          <FadeIn className="font-heading text-hero font-hero text-primary-black mb-6">
            Contacte-nos
          </FadeIn>
          <FadeIn
            delay={0.2}
            className="text-body text-primary-mediumGray max-w-3xl mx-auto leading-relaxed"
          >
            Tem alguma questão sobre os nossos veículos vintage ou pretende
            fazer uma reserva? Estamos aqui para ajudar. Entre em contacto
            connosco através do formulário abaixo ou utilize as nossas
            informações de contacto direto.
          </FadeIn>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8 lg:gap-12">
          {/* Contact Form */}
          <div className="lg:col-span-2">
            <FadeIn delay={0.3}>
              <div className="card p-8">
                <h2 className="font-heading text-section font-section text-primary-black mb-8">
                  Envie-nos uma Mensagem
                </h2>

                <form onSubmit={handleSubmit} className="space-y-6">
                  {/* Name & Email Row */}
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                      <label
                        htmlFor="name"
                        className="block text-sm font-semibold text-primary-black mb-3"
                      >
                        Nome *
                      </label>
                      <input
                        type="text"
                        id="name"
                        name="name"
                        value={formData.name}
                        onChange={handleInputChange}
                        className={`w-full px-4 py-4 border-2 rounded-xl focus:ring-2 focus:ring-primary-black/20 focus:border-primary-black transition-all duration-300 bg-white ${
                          errors.name
                            ? 'border-red-400 bg-red-50'
                            : 'border-gray-200 hover:border-gray-300'
                        }`}
                        placeholder="O seu nome completo"
                      />
                      {errors.name && (
                        <p className="mt-2 text-sm text-red-600 flex items-center gap-1">
                          <svg
                            className="w-4 h-4"
                            fill="currentColor"
                            viewBox="0 0 20 20"
                          >
                            <path
                              fillRule="evenodd"
                              d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z"
                              clipRule="evenodd"
                            />
                          </svg>
                          {errors.name}
                        </p>
                      )}
                    </div>

                    <div>
                      <label
                        htmlFor="email"
                        className="block text-sm font-semibold text-primary-black mb-3"
                      >
                        Email *
                      </label>
                      <input
                        type="email"
                        id="email"
                        name="email"
                        value={formData.email}
                        onChange={handleInputChange}
                        className={`w-full px-4 py-4 border-2 rounded-xl focus:ring-2 focus:ring-primary-black/20 focus:border-primary-black transition-all duration-300 bg-white ${
                          errors.email
                            ? 'border-red-400 bg-red-50'
                            : 'border-gray-200 hover:border-gray-300'
                        }`}
                        placeholder="<EMAIL>"
                      />
                      {errors.email && (
                        <p className="mt-2 text-sm text-red-600 flex items-center gap-1">
                          <svg
                            className="w-4 h-4"
                            fill="currentColor"
                            viewBox="0 0 20 20"
                          >
                            <path
                              fillRule="evenodd"
                              d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z"
                              clipRule="evenodd"
                            />
                          </svg>
                          {errors.email}
                        </p>
                      )}
                    </div>
                  </div>

                  {/* Phone & Company Row */}
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                      <label
                        htmlFor="phone"
                        className="block text-sm font-semibold text-primary-black mb-3"
                      >
                        Telefone
                      </label>
                      <input
                        type="tel"
                        id="phone"
                        name="phone"
                        value={formData.phone}
                        onChange={handleInputChange}
                        className={`w-full px-4 py-4 border-2 rounded-xl focus:ring-2 focus:ring-primary-black/20 focus:border-primary-black transition-all duration-300 bg-white ${
                          errors.phone
                            ? 'border-red-400 bg-red-50'
                            : 'border-gray-200 hover:border-gray-300'
                        }`}
                        placeholder="+351 968 064 591"
                      />
                      {errors.phone && (
                        <p className="mt-2 text-sm text-red-600 flex items-center gap-1">
                          <svg
                            className="w-4 h-4"
                            fill="currentColor"
                            viewBox="0 0 20 20"
                          >
                            <path
                              fillRule="evenodd"
                              d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z"
                              clipRule="evenodd"
                            />
                          </svg>
                          {errors.phone}
                        </p>
                      )}
                    </div>

                    <div>
                      <label
                        htmlFor="company"
                        className="block text-sm font-semibold text-primary-black mb-3"
                      >
                        Empresa
                      </label>
                      <input
                        type="text"
                        id="company"
                        name="company"
                        value={formData.company}
                        onChange={handleInputChange}
                        className="w-full px-4 py-4 border-2 border-gray-200 rounded-xl focus:ring-2 focus:ring-primary-black/20 focus:border-primary-black transition-all duration-300 bg-white hover:border-gray-300"
                        placeholder="Nome da sua empresa (opcional)"
                      />
                    </div>
                  </div>

                  {/* Subject */}
                  <div>
                    <label
                      htmlFor="subject"
                      className="block text-sm font-semibold text-primary-black mb-3"
                    >
                      Assunto
                    </label>
                    <input
                      type="text"
                      id="subject"
                      name="subject"
                      value={formData.subject}
                      onChange={handleInputChange}
                      className="w-full px-4 py-4 border-2 border-gray-200 rounded-xl focus:ring-2 focus:ring-primary-black/20 focus:border-primary-black transition-all duration-300 bg-white hover:border-gray-300"
                      placeholder="Assunto da sua mensagem (opcional)"
                    />
                  </div>

                  {/* Message */}
                  <div>
                    <label
                      htmlFor="message"
                      className="block text-sm font-semibold text-primary-black mb-3"
                    >
                      Mensagem *
                    </label>
                    <textarea
                      id="message"
                      name="message"
                      rows={6}
                      value={formData.message}
                      onChange={handleInputChange}
                      className={`w-full px-4 py-4 border-2 rounded-xl focus:ring-2 focus:ring-primary-black/20 focus:border-primary-black transition-all duration-300 bg-white resize-vertical ${
                        errors.message
                          ? 'border-red-400 bg-red-50'
                          : 'border-gray-200 hover:border-gray-300'
                      }`}
                      placeholder="Descreva a sua necessidade, evento ou questão em detalhe..."
                    />
                    {errors.message && (
                      <p className="mt-2 text-sm text-red-600 flex items-center gap-1">
                        <svg
                          className="w-4 h-4"
                          fill="currentColor"
                          viewBox="0 0 20 20"
                        >
                          <path
                            fillRule="evenodd"
                            d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z"
                            clipRule="evenodd"
                          />
                        </svg>
                        {errors.message}
                      </p>
                    )}
                  </div>

                  {/* Submit Error */}
                  {errors.submit && (
                    <div className="bg-red-50 border-2 border-red-200 rounded-xl p-4 flex items-start gap-3">
                      <svg
                        className="w-5 h-5 text-red-600 mt-0.5 flex-shrink-0"
                        fill="currentColor"
                        viewBox="0 0 20 20"
                      >
                        <path
                          fillRule="evenodd"
                          d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z"
                          clipRule="evenodd"
                        />
                      </svg>
                      <p className="text-sm text-red-700 font-medium">
                        {errors.submit}
                      </p>
                    </div>
                  )}

                  {/* Submit Button */}
                  <div className="pt-4">
                    <RippleButton
                      type="submit"
                      disabled={isSubmitting}
                      className="btn-primary w-full py-4 text-lg font-semibold disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center gap-2 group"
                      rippleColor="rgba(255, 255, 255, 0.3)"
                    >
                      {isSubmitting ? (
                        <>
                          <svg
                            className="animate-spin w-5 h-5"
                            fill="none"
                            viewBox="0 0 24 24"
                          >
                            <circle
                              className="opacity-25"
                              cx="12"
                              cy="12"
                              r="10"
                              stroke="currentColor"
                              strokeWidth="4"
                            ></circle>
                            <path
                              className="opacity-75"
                              fill="currentColor"
                              d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                            ></path>
                          </svg>
                          A enviar...
                        </>
                      ) : (
                        <>
                          Enviar Mensagem
                          <svg
                            className="w-5 h-5 transition-transform group-hover:translate-x-1"
                            fill="none"
                            stroke="currentColor"
                            viewBox="0 0 24 24"
                          >
                            <path
                              strokeLinecap="round"
                              strokeLinejoin="round"
                              strokeWidth={2}
                              d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8"
                            />
                          </svg>
                        </>
                      )}
                    </RippleButton>

                    {/* Response Time Note */}
                    <div className="mt-4 p-4 bg-gray-50 rounded-xl border border-gray-200">
                      <div className="flex items-start gap-3">
                        <div className="w-5 h-5 text-primary-black mt-0.5 flex-shrink-0">
                          <svg
                            fill="none"
                            stroke="currentColor"
                            viewBox="0 0 24 24"
                          >
                            <path
                              strokeLinecap="round"
                              strokeLinejoin="round"
                              strokeWidth={2}
                              d="M13 10V3L4 14h7v7l9-11h-7z"
                            />
                          </svg>
                        </div>
                        <div className="flex-1">
                          <p className="text-sm text-primary-mediumGray leading-relaxed">
                            <span className="font-semibold text-primary-black">
                              Resposta rápida garantida:
                            </span>{' '}
                            Responderemos no prazo de 24 horas. Para pedidos
                            urgentes, ligue{' '}
                            <a
                              href="tel:+351968064591"
                              className="font-semibold text-primary-black hover:underline"
                            >
                              +351 968 064 591
                            </a>
                          </p>
                        </div>
                      </div>
                    </div>
                  </div>
                </form>
              </div>
            </FadeIn>
          </div>

          {/* Contact Information & Map */}
          <div className="space-y-8">
            {/* Contact Info */}
            <FadeIn delay={0.4}>
              <div className="card p-8">
                <h2 className="font-heading text-section font-section text-primary-black mb-8">
                  Informações de Contacto
                </h2>

                <div className="space-y-6">
                  <FadeIn delay={0.5}>
                    <HoverCard className="flex items-start space-x-4 p-4 rounded-xl bg-gray-50">
                      <div className="w-10 h-10 bg-primary-black text-white rounded-full flex items-center justify-center flex-shrink-0">
                        <svg
                          className="w-5 h-5"
                          fill="none"
                          stroke="currentColor"
                          viewBox="0 0 24 24"
                        >
                          <path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            strokeWidth={2}
                            d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"
                          />
                        </svg>
                      </div>
                      <div className="flex-1">
                        <p className="font-semibold text-primary-black mb-1">
                          Email
                        </p>
                        <a
                          href="mailto:<EMAIL>"
                          className="text-primary-mediumGray hover:text-primary-black transition-colors font-medium break-all"
                        >
                          <EMAIL>
                        </a>
                      </div>
                    </HoverCard>
                  </FadeIn>

                  <FadeIn delay={0.6}>
                    <HoverCard className="flex items-start space-x-4 p-4 rounded-xl bg-gray-50">
                      <div className="w-10 h-10 bg-primary-black text-white rounded-full flex items-center justify-center flex-shrink-0">
                        <svg
                          className="w-5 h-5"
                          fill="none"
                          stroke="currentColor"
                          viewBox="0 0 24 24"
                        >
                          <path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            strokeWidth={2}
                            d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"
                          />
                        </svg>
                      </div>
                      <div className="flex-1">
                        <p className="font-semibold text-primary-black mb-1">
                          Telefone
                        </p>
                        <a
                          href="tel:+351968064591"
                          className="text-primary-mediumGray hover:text-primary-black transition-colors font-medium"
                        >
                          +351 968 064 591
                        </a>
                        <p className="text-xs text-primary-lightGray mt-1">
                          Disponível 24h/dia
                        </p>
                      </div>
                    </HoverCard>
                  </FadeIn>

                  <FadeIn delay={0.7}>
                    <HoverCard className="flex items-start space-x-4 p-4 rounded-xl bg-gray-50">
                      <div className="w-10 h-10 bg-primary-black text-white rounded-full flex items-center justify-center flex-shrink-0">
                        <svg
                          className="w-5 h-5"
                          fill="none"
                          stroke="currentColor"
                          viewBox="0 0 24 24"
                        >
                          <path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            strokeWidth={2}
                            d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"
                          />
                          <path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            strokeWidth={2}
                            d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"
                          />
                        </svg>
                      </div>
                      <div className="flex-1">
                        <p className="font-semibold text-primary-black mb-1">
                          Localização
                        </p>
                        <p className="text-primary-mediumGray font-medium leading-relaxed">
                          Vila do Conde, Portugal
                        </p>
                      </div>
                    </HoverCard>
                  </FadeIn>

                  <FadeIn delay={0.8}>
                    <HoverCard className="flex items-start space-x-4 p-4 rounded-xl bg-gray-50">
                      <div className="w-10 h-10 bg-primary-black text-white rounded-full flex items-center justify-center flex-shrink-0">
                        <svg
                          className="w-5 h-5"
                          fill="none"
                          stroke="currentColor"
                          viewBox="0 0 24 24"
                        >
                          <path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            strokeWidth={2}
                            d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"
                          />
                        </svg>
                      </div>
                      <div className="flex-1">
                        <p className="font-semibold text-primary-black mb-1">
                          Horário
                        </p>
                        <div className="text-primary-mediumGray font-medium leading-relaxed">
                          <p className="text-primary-black font-semibold">Telefone disponível 24h/dia</p>
                          <p className="text-xs text-primary-lightGray mt-2">
                            Contacte-nos a qualquer hora para informações e reservas
                          </p>
                        </div>
                      </div>
                    </HoverCard>
                  </FadeIn>
                </div>
              </div>
            </FadeIn>

            {/* Google Maps */}
            <FadeIn delay={0.9}>
              <div className="card p-0 overflow-hidden">
                <div className="p-8 pb-6">
                  <h3 className="font-heading text-card font-card text-primary-black mb-3">
                    Localização
                  </h3>
                  <p className="text-sm text-primary-mediumGray leading-relaxed">
                    Contacte-nos para conhecer os nossos veículos vintage de perto
                    e descobrir toda a nossa frota em Vila do Conde.
                  </p>
                </div>
                <div className="h-72 bg-gray-100 relative">
                  <iframe
                    src="https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d11687.5!2d-8.7479!3d41.3518!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x0%3A0x0!2zNDHCsDIxJzA2LjUiTiA4wrA0NCc1Mi40Ilc!5e0!3m2!1spt!2spt!4v1234567890"
                    width="100%"
                    height="100%"
                    style={{ border: 0 }}
                    allowFullScreen
                    loading="lazy"
                    referrerPolicy="no-referrer-when-downgrade"
                    title="Localização Vintage Marketing Portugal em Vila do Conde"
                    className="rounded-b-xl"
                  />
                </div>
              </div>
            </FadeIn>
          </div>
        </div>
      </div>

      {/* Loading Animation for Form Submission */}
      <LoadingAnimation isLoading={isSubmitting} />
    </div>
  );
}
