'use client';

import { useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useAuth } from '@/contexts/AuthContext';

interface ProtectedRouteProps {
  children: React.ReactNode;
  requireAdmin?: boolean;
}

export default function ProtectedRoute({
  children,
  requireAdmin = true,
}: ProtectedRouteProps) {
  const { user, isLoading, isAdmin } = useAuth();
  const router = useRouter();

  useEffect(() => {
    if (!isLoading) {
      if (!user) {
        // User is not authenticated, redirect to login
        router.push('/admin/login');
        return;
      }

      if (requireAdmin && !isAdmin) {
        // User is authenticated but not admin, redirect to login with error
        router.push('/admin/login?error=access_denied');
        return;
      }
    }
  }, [user, isLoading, isAdmin, requireAdmin, router]);

  // Show loading state while checking authentication
  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-background-cream">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-black mx-auto mb-4"></div>
          <p className="text-primary-mediumGray">A verificar autenticação...</p>
        </div>
      </div>
    );
  }

  // Show nothing while redirecting
  if (!user || (requireAdmin && !isAdmin)) {
    return null;
  }

  // User is authenticated and authorized
  return <>{children}</>;
}
