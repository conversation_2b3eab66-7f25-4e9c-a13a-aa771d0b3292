'use client';

import Image from 'next/image';
import { motion } from 'motion/react';
import { useResponsiveParallax } from '@/hooks/useScrollAnimations';
import { useRef, useEffect, useState } from 'react';

interface ParallaxImageProps {
  src: string;
  alt: string;
  speed?: number;
  className?: string;
}

export default function ParallaxImage({
  src,
  alt,
  speed = 0.5,
  className = '',
}: ParallaxImageProps) {
  const ref = useRef<HTMLDivElement>(null);
  const { y, scale, isMobile } = useResponsiveParallax(speed, ref);

  // Track hydration to prevent SSR/client mismatch
  const [isHydrated, setIsHydrated] = useState(false);

  useEffect(() => {
    setIsHydrated(true);
  }, []);

  // Use consistent initial values for SSR/client, then apply responsive values after hydration
  const defaultHeight = '90%'; // Desktop default for consistent SSR (reduced from 95%)
  const defaultTop = '-3%';

  // Only apply responsive values after hydration
  const containerHeight = isHydrated
    ? isMobile
      ? '102%'
      : '90%'
    : defaultHeight;
  const containerTop = isHydrated ? (isMobile ? '-1%' : '-3%') : defaultTop;

  return (
    <div ref={ref} className="absolute inset-0 overflow-hidden">
      <motion.div
        style={{ y, scale }}
        className="absolute inset-0 will-change-transform"
        initial={{
          height: defaultHeight,
          top: defaultTop,
        }}
        animate={{
          height: containerHeight,
          top: containerTop,
        }}
        transition={{
          height: { duration: 0.3, ease: 'easeOut' },
          top: { duration: 0.3, ease: 'easeOut' },
        }}
      >
        <Image
          src={src}
          alt={alt}
          fill
          className={`hero-bg-image ${className}`}
          priority
          sizes="100vw"
          style={{
            transform: isHydrated
              ? isMobile
                ? 'scale(1)'
                : 'scale(0.95)'
              : 'scale(0.95)',
            transformOrigin: 'center center',
          }}
        />
      </motion.div>
    </div>
  );
}
