'use client';

import { Fragment } from 'react';
import { Dialog, Transition } from '@headlessui/react';
import { useRouter } from 'next/navigation';
import Link from 'next/link';
import Image from 'next/image';
import { Vehicle } from '@/types';

interface VehicleModalProps {
  vehicle: Vehicle | null;
  isOpen: boolean;
  onClose: () => void;
}

export default function VehicleModal({
  vehicle,
  isOpen,
  onClose,
}: VehicleModalProps) {
  const router = useRouter();

  if (!vehicle) return null;

  const formatPrice = (price: number | null) => {
    if (!price) return 'Preço sob consulta';
    return `€${price}/dia`;
  };

  const handleBookingClick = () => {
    router.push(`/reservas?vehicle=${vehicle.id}`);
  };

  return (
    <Transition appear show={isOpen} as={Fragment}>
      <Dialog as="div" className="relative z-50" onClose={onClose}>
        <Transition.Child
          as={Fragment}
          enter="ease-out duration-300"
          enterFrom="opacity-0"
          enterTo="opacity-100"
          leave="ease-in duration-200"
          leaveFrom="opacity-100"
          leaveTo="opacity-0"
        >
          <div className="fixed inset-0 bg-black bg-opacity-50" />
        </Transition.Child>

        <div className="fixed inset-0 overflow-y-auto">
          <div className="flex min-h-full items-center justify-center p-4 text-center">
            <Transition.Child
              as={Fragment}
              enter="ease-out duration-300"
              enterFrom="opacity-0 scale-95"
              enterTo="opacity-100 scale-100"
              leave="ease-in duration-200"
              leaveFrom="opacity-100 scale-100"
              leaveTo="opacity-0 scale-95"
            >
              <Dialog.Panel className="w-full max-w-4xl transform overflow-hidden rounded-xl bg-white text-left align-middle shadow-[0_8px_24px_rgba(0,0,0,0.12)] transition-all">
                {/* Close Button */}
                <div className="absolute right-4 top-4 z-10">
                  <button
                    type="button"
                    className="rounded-full bg-white/90 p-2 text-primary-black hover:bg-white hover:text-primary-darkGray transition-all duration-300 shadow-sm"
                    onClick={onClose}
                  >
                    <span className="sr-only">Fechar</span>
                    <svg
                      className="h-6 w-6"
                      fill="none"
                      viewBox="0 0 24 24"
                      strokeWidth="1.5"
                      stroke="currentColor"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        d="M6 18L18 6M6 6l12 12"
                      />
                    </svg>
                  </button>
                </div>

                <div className="grid grid-cols-1 lg:grid-cols-2">
                  {/* Vehicle Image */}
                  <div className="relative h-64 lg:h-96 bg-background-beige">
                    {vehicle.photo_url ? (
                      <Image
                        src={vehicle.photo_url}
                        alt={`${vehicle.name} - ${vehicle.year}`}
                        fill
                        className="object-cover"
                        sizes="(max-width: 1024px) 100vw, 50vw"
                        priority
                      />
                    ) : (
                      <div className="flex items-center justify-center h-full text-primary-mediumGray">
                        <div className="text-center">
                          <svg
                            className="w-20 h-20 mx-auto mb-4"
                            fill="none"
                            stroke="currentColor"
                            viewBox="0 0 24 24"
                          >
                            <path
                              strokeLinecap="round"
                              strokeLinejoin="round"
                              strokeWidth={2}
                              d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-4m-5 0H9m0 0H5m0 0h2M7 7h10M7 11h10M7 15h10"
                            />
                          </svg>
                          <p className="text-lg font-medium">Imagem em breve</p>
                        </div>
                      </div>
                    )}

                    {/* Year Badge */}
                    <div className="absolute top-4 left-4 bg-primary-black text-primary-white px-4 py-2 rounded-full text-lg font-semibold">
                      {vehicle.year}
                    </div>
                  </div>

                  {/* Vehicle Details */}
                  <div className="p-6 lg:p-8">
                    <Dialog.Title
                      as="h3"
                      className="font-heading text-2xl lg:text-3xl font-bold text-primary-black mb-4"
                    >
                      {vehicle.name}
                    </Dialog.Title>

                    {/* Price */}
                    <div className="mb-6">
                      <span className="text-2xl font-bold text-primary-black">
                        {formatPrice(vehicle.price)}
                      </span>
                      {vehicle.price && (
                        <p className="text-sm text-primary-mediumGray mt-1">
                          Preços podem variar conforme a duração e tipo de
                          evento
                        </p>
                      )}
                    </div>

                    {/* Description */}
                    {vehicle.description && (
                      <div className="mb-6">
                        <h4 className="font-heading text-lg font-semibold text-primary-black mb-3">
                          Descrição
                        </h4>
                        <p className="text-primary-mediumGray leading-relaxed">
                          {vehicle.description}
                        </p>
                      </div>
                    )}

                    {/* Vehicle Specifications */}
                    <div className="mb-6">
                      <h4 className="font-heading text-lg font-semibold text-primary-black mb-3">
                        Especificações
                      </h4>
                      <div className="grid grid-cols-2 gap-4 text-sm">
                        <div>
                          <span className="text-primary-mediumGray font-medium">
                            Ano:
                          </span>
                          <span className="ml-2 text-primary-black">
                            {vehicle.year}
                          </span>
                        </div>
                        <div>
                          <span className="text-primary-mediumGray font-medium">
                            Estado:
                          </span>
                          <span className="ml-2 text-primary-black">
                            Restaurado
                          </span>
                        </div>
                      </div>
                    </div>

                    {/* Features */}
                    <div className="mb-6">
                      <h4 className="font-heading text-lg font-semibold text-primary-black mb-3">
                        Ideal Para
                      </h4>
                      <ul className="text-primary-mediumGray text-sm space-y-2">
                        <li className="flex items-center">
                          <svg
                            className="w-4 h-4 text-primary-black mr-2"
                            fill="currentColor"
                            viewBox="0 0 20 20"
                          >
                            <path
                              fillRule="evenodd"
                              d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                              clipRule="evenodd"
                            />
                          </svg>
                          Sessões fotográficas profissionais
                        </li>
                        <li className="flex items-center">
                          <svg
                            className="w-4 h-4 text-primary-black mr-2"
                            fill="currentColor"
                            viewBox="0 0 20 20"
                          >
                            <path
                              fillRule="evenodd"
                              d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                              clipRule="evenodd"
                            />
                          </svg>
                          Eventos corporativos e lançamentos
                        </li>
                        <li className="flex items-center">
                          <svg
                            className="w-4 h-4 text-primary-black mr-2"
                            fill="currentColor"
                            viewBox="0 0 20 20"
                          >
                            <path
                              fillRule="evenodd"
                              d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                              clipRule="evenodd"
                            />
                          </svg>
                          Campanhas publicitárias
                        </li>
                        <li className="flex items-center">
                          <svg
                            className="w-4 h-4 text-primary-black mr-2"
                            fill="currentColor"
                            viewBox="0 0 20 20"
                          >
                            <path
                              fillRule="evenodd"
                              d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                              clipRule="evenodd"
                            />
                          </svg>
                          Eventos especiais e celebrações
                        </li>
                      </ul>
                    </div>

                    {/* Usage Conditions */}
                    <div className="mb-8 p-4 bg-background-beige border border-primary-lightGray/20 rounded-lg">
                      <h4 className="font-heading text-lg font-semibold text-primary-black mb-3 flex items-center">
                        <svg
                          className="w-5 h-5 text-accent-vintage mr-2"
                          fill="currentColor"
                          viewBox="0 0 20 20"
                        >
                          <path
                            fillRule="evenodd"
                            d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z"
                            clipRule="evenodd"
                          />
                        </svg>
                        Condições de Utilização
                      </h4>
                      <div className="space-y-4 text-sm">
                        {/* Driver Included Section */}
                        <div className="space-y-2">
                          <div className="flex items-center justify-between">
                            <div className="bg-accent-vintage text-white px-3 py-1.5 rounded-full text-xs font-semibold">
                              Motorista Incluído
                            </div>
                          </div>
                          <p className="text-primary-mediumGray leading-relaxed pl-0">
                            Este veículo está disponível <strong className="text-primary-black">apenas para exposição e sessões fotográficas</strong>, sempre acompanhado por motorista profissional.
                          </p>
                        </div>

                        {/* Warning Section */}
                        <div className="flex items-start space-x-3">
                          <svg
                            className="w-4 h-4 text-accent-vintage mt-1 flex-shrink-0"
                            fill="currentColor"
                            viewBox="0 0 20 20"
                          >
                            <path
                              fillRule="evenodd"
                              d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z"
                              clipRule="evenodd"
                            />
                          </svg>
                          <p className="text-primary-mediumGray leading-relaxed">
                            <strong className="text-primary-black">Não é permitida a condução independente</strong> por questões de segurança e preservação do veículo histórico.
                          </p>
                        </div>
                      </div>
                    </div>

                    {/* Action Buttons */}
                    <div className="flex flex-col sm:flex-row gap-3">
                      <button
                        type="button"
                        className="btn-primary flex-1 text-center py-3"
                        onClick={handleBookingClick}
                      >
                        Reservar Este Veículo
                      </button>
                      <Link
                        href="/contacto"
                        className="btn-secondary flex-1 text-center py-3"
                      >
                        Mais Informações
                      </Link>
                    </div>

                    {/* Contact Info */}
                    <div className="mt-6 p-4 bg-background-beige rounded-lg">
                      <p className="text-sm text-primary-mediumGray text-center">
                        <strong className="text-primary-black">
                          Precisa de ajuda?
                        </strong>{' '}
                        Entre em contacto connosco para mais informações sobre
                        disponibilidade e preços especiais.
                      </p>
                    </div>
                  </div>
                </div>
              </Dialog.Panel>
            </Transition.Child>
          </div>
        </div>
      </Dialog>
    </Transition>
  );
}
