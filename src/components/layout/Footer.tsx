import Link from 'next/link';

export default function Footer() {
  return (
    <footer className="section-dark">
      <div className="container mx-auto px-8">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
          {/* Company Info */}
          <div>
            <h3 className="font-logo text-card font-card text-primary-white mb-4">
              Vintage Marketing Portugal
            </h3>
            <p className="text-body-secondary text-primary-lightGray mb-4">
              Especialistas em aluguer de veículos vintage para eventos de
              marketing e campanhas publicitárias.
            </p>
            <p className="text-sm text-primary-lightGray">
              © 2025 Vintage Marketing Portugal. Todos os direitos reservados.
            </p>
          </div>

          {/* Contact Information */}
          <div>
            <h4 className="font-heading text-lg font-semibold text-primary-white mb-4">
              Contacto
            </h4>
            <div className="space-y-2 text-primary-lightGray">
              <p>
                <span className="font-medium">Email:</span>{' '}
                <a
                  href="mailto:<EMAIL>"
                  className="hover:text-primary-white transition-colors duration-300"
                >
                  <EMAIL>
                </a>
              </p>
              <p>
                <span className="font-medium">Telefone:</span>{' '}
                <a
                  href="tel:+351968064591"
                  className="hover:text-primary-white transition-colors duration-300"
                >
                  +351 968 064 591
                </a>
                <span className="text-xs text-primary-lightGray block">
                  (24h/dia)
                </span>
              </p>
              <p>
                <span className="font-medium">Localização:</span>
                <br />
                Vila do Conde, Portugal
              </p>
            </div>
          </div>

          {/* Quick Links */}
          <div>
            <h4 className="font-heading text-lg font-semibold text-primary-white mb-4">
              Links Rápidos
            </h4>
            <nav className="space-y-2">
              <Link
                href="/servicos"
                className="block text-primary-lightGray hover:text-primary-white transition-colors duration-300"
              >
                Serviços
              </Link>
              <Link
                href="/frota"
                className="block text-primary-lightGray hover:text-primary-white transition-colors duration-300"
              >
                A Nossa Frota
              </Link>
              <Link
                href="/reservas"
                className="block text-primary-lightGray hover:text-primary-white transition-colors duration-300"
              >
                Fazer Reserva
              </Link>
              <Link
                href="/contacto"
                className="block text-primary-lightGray hover:text-primary-white transition-colors duration-300"
              >
                Contacte-nos
              </Link>
              <Link
                href="/termos"
                className="block text-primary-lightGray hover:text-primary-white transition-colors duration-300"
              >
                Termos e Condições
              </Link>
            </nav>
          </div>
        </div>

        {/* Bottom Bar */}
        <div className="border-t border-primary-darkGray mt-8 pt-6">
          <div className="flex flex-col md:flex-row justify-between items-center">
            <p className="text-sm text-primary-lightGray mb-4 md:mb-0">
              Telefone disponível 24h/dia
            </p>
            <div className="flex space-x-4">
              {/* Social media links will be added when accounts are created */}
              <span className="text-primary-lightGray text-sm">
                Siga-nos nas redes sociais em breve
              </span>
            </div>
          </div>
        </div>
      </div>
    </footer>
  );
}
