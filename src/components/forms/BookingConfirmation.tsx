'use client';

import { useState } from 'react';
import { format, parseISO, differenceInDays } from 'date-fns';
import { pt } from 'date-fns/locale';
import {
  CalendarIcon,
  UserIcon,
  EnvelopeIcon,
  PhoneIcon,
  TruckIcon,
  ExclamationTriangleIcon,
  CheckCircleIcon,
} from '@heroicons/react/24/outline';
import { BookingFormData, Vehicle } from '@/types';
import { supabase } from '@/lib/supabase';
import Image from 'next/image';
import { RippleButton, LoadingAnimation } from '@/components/animations';

interface BookingConfirmationProps {
  bookingData: BookingFormData & { selectedVehicle: Vehicle };
  onConfirm: () => void;
  isSubmitting: boolean;
}

export default function BookingConfirmation({
  bookingData,
  onConfirm,
  isSubmitting,
}: BookingConfirmationProps) {
  const [submitError, setSubmitError] = useState<string | null>(null);
  const [isSubmitted, setIsSubmitted] = useState(false);

  const startDate = parseISO(bookingData.start_date);
  const endDate = parseISO(bookingData.end_date);
  const numberOfDays = differenceInDays(endDate, startDate) + 1;
  const totalPrice = bookingData.selectedVehicle.price
    ? bookingData.selectedVehicle.price * numberOfDays
    : null;

  const handleConfirmBooking = async () => {
    if (!supabase) {
      setSubmitError('Configuração da base de dados não disponível');
      return;
    }

    setSubmitError(null);

    try {
      const bookingInsert = {
        vehicle_id: bookingData.vehicle_id,
        customer_name: bookingData.customer_name,
        customer_email: bookingData.customer_email,
        customer_phone: bookingData.customer_phone || null,
        start_date: bookingData.start_date,
        end_date: bookingData.end_date,
        notes: bookingData.notes || null,
        status: 'pendente',
        total_price: totalPrice,
      };

      const { error } = await supabase.from('bookings').insert([bookingInsert]);

      if (error) throw error;

      setIsSubmitted(true);
      onConfirm();
    } catch (error: any) {
      console.error('Error submitting booking:', error);
      setSubmitError(
        error.message || 'Erro ao submeter a reserva. Tente novamente.'
      );
    }
  };

  if (isSubmitted) {
    return (
      <div className="text-center py-12">
        <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-6">
          <CheckCircleIcon className="w-8 h-8 text-green-600" />
        </div>
        <h2 className="font-heading text-2xl font-bold text-black mb-4">
          Reserva Enviada com Sucesso!
        </h2>
        <p className="text-[#666666] max-w-md mx-auto">
          Recebemos a sua reserva e entraremos em contacto consigo em breve para
          confirmar todos os detalhes.
        </p>
      </div>
    );
  }

  return (
    <div>
      <h2 className="font-heading text-2xl font-bold text-black mb-2">
        Confirme a Sua Reserva
      </h2>
      <p className="text-[#666666] mb-8">
        Revise todos os detalhes da sua reserva antes de confirmar.
      </p>

      <div className="space-y-6">
        {/* Vehicle Details */}
        <div className="bg-[#F5F1E8] rounded-lg p-6">
          <h3 className="font-heading text-lg font-semibold text-black mb-4 flex items-center">
            <TruckIcon className="w-5 h-5 mr-2" />
            Veículo Selecionado
          </h3>

          <div className="flex flex-col md:flex-row gap-4">
            {bookingData.selectedVehicle.photo_url && (
              <div className="relative w-full md:w-48 h-32 rounded-lg overflow-hidden bg-white">
                <Image
                  src={bookingData.selectedVehicle.photo_url}
                  alt={`${bookingData.selectedVehicle.name} - ${bookingData.selectedVehicle.year}`}
                  fill
                  className="object-cover"
                  sizes="(max-width: 768px) 100vw, 192px"
                />
              </div>
            )}

            <div className="flex-1">
              <h4 className="font-heading text-xl font-bold text-black mb-1">
                {bookingData.selectedVehicle.name}
              </h4>
              <p className="text-[#666666] mb-2">
                Ano: {bookingData.selectedVehicle.year}
              </p>
              {bookingData.selectedVehicle.description && (
                <p className="text-[#666666] text-sm">
                  {bookingData.selectedVehicle.description}
                </p>
              )}
              {bookingData.selectedVehicle.price && (
                <p className="text-lg font-bold text-black mt-2">
                  €{bookingData.selectedVehicle.price} por dia
                </p>
              )}
            </div>
          </div>
        </div>

        {/* Booking Dates */}
        <div className="bg-white border border-[#E8E0D0] rounded-lg p-6">
          <h3 className="font-heading text-lg font-semibold text-black mb-4 flex items-center">
            <CalendarIcon className="w-5 h-5 mr-2" />
            Datas da Reserva
          </h3>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
              <p className="text-sm font-semibold text-black mb-1">
                Data de Início
              </p>
              <p className="text-[#666666]">
                {format(startDate, "dd 'de' MMMM 'de' yyyy", { locale: pt })}
              </p>
            </div>
            <div>
              <p className="text-sm font-semibold text-black mb-1">
                Data de Fim
              </p>
              <p className="text-[#666666]">
                {format(endDate, "dd 'de' MMMM 'de' yyyy", { locale: pt })}
              </p>
            </div>
            <div>
              <p className="text-sm font-semibold text-black mb-1">Duração</p>
              <p className="text-[#666666]">
                {numberOfDays} {numberOfDays === 1 ? 'dia' : 'dias'}
              </p>
            </div>
          </div>
        </div>

        {/* Customer Details */}
        <div className="bg-white border border-[#E8E0D0] rounded-lg p-6">
          <h3 className="font-heading text-lg font-semibold text-black mb-4 flex items-center">
            <UserIcon className="w-5 h-5 mr-2" />
            Dados de Contacto
          </h3>

          <div className="space-y-3">
            <div className="flex items-center">
              <UserIcon className="w-4 h-4 text-[#666666] mr-3" />
              <span className="text-[#666666]">
                {bookingData.customer_name}
              </span>
            </div>
            <div className="flex items-center">
              <EnvelopeIcon className="w-4 h-4 text-[#666666] mr-3" />
              <span className="text-[#666666]">
                {bookingData.customer_email}
              </span>
            </div>
            {bookingData.customer_phone && (
              <div className="flex items-center">
                <PhoneIcon className="w-4 h-4 text-[#666666] mr-3" />
                <span className="text-[#666666]">
                  {bookingData.customer_phone}
                </span>
              </div>
            )}
          </div>
        </div>

        {/* Additional Notes */}
        {bookingData.notes && (
          <div className="bg-white border border-[#E8E0D0] rounded-lg p-6">
            <h3 className="font-heading text-lg font-semibold text-black mb-4">
              Notas Adicionais
            </h3>
            <p className="text-[#666666]">{bookingData.notes}</p>
          </div>
        )}

        {/* Price Summary */}
        {totalPrice && (
          <div className="bg-black text-white rounded-lg p-6">
            <h3 className="text-lg font-semibold mb-4">Resumo de Preços</h3>
            <div className="space-y-2">
              <div className="flex justify-between">
                <span>
                  €{bookingData.selectedVehicle.price} × {numberOfDays}{' '}
                  {numberOfDays === 1 ? 'dia' : 'dias'}
                </span>
                <span>€{totalPrice}</span>
              </div>
              <div className="border-t border-gray-600 pt-2">
                <div className="flex justify-between text-xl font-bold">
                  <span>Total</span>
                  <span>€{totalPrice}</span>
                </div>
              </div>
            </div>
            <p className="text-sm text-gray-300 mt-4">
              * Preço final sujeito a confirmação. Podem aplicar-se taxas
              adicionais.
            </p>
          </div>
        )}

        {/* Error Message */}
        {submitError && (
          <div className="bg-red-50 border border-red-200 rounded-lg p-4">
            <div className="flex items-center">
              <ExclamationTriangleIcon className="w-5 h-5 text-red-600 mr-2" />
              <p className="text-red-800">{submitError}</p>
            </div>
          </div>
        )}

        {/* Important Notice */}
        <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
          <div className="flex items-start">
            <ExclamationTriangleIcon className="w-5 h-5 text-yellow-600 mr-2 mt-0.5" />
            <div>
              <p className="text-yellow-800 font-semibold mb-1">Importante:</p>
              <p className="text-yellow-700 text-sm">
                Esta é uma solicitação de reserva. Entraremos em contacto
                consigo para confirmar a disponibilidade e finalizar todos os
                detalhes antes da confirmação final.
              </p>
            </div>
          </div>
        </div>

        {/* Confirm Button */}
        <div className="pt-4">
          <RippleButton
            onClick={handleConfirmBooking}
            disabled={isSubmitting}
            className={`w-full py-4 px-6 rounded-full font-semibold text-lg transition-all duration-300 ${
              isSubmitting
                ? 'bg-[#E8E0D0] text-[#999999] cursor-not-allowed'
                : 'bg-black text-white hover:bg-[#1A1A1A] hover:-translate-y-0.5'
            }`}
            rippleColor="rgba(255, 255, 255, 0.3)"
          >
            {isSubmitting ? (
              <div className="flex items-center justify-center">
                <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-[#999999] mr-3"></div>
                A enviar reserva...
              </div>
            ) : (
              'Confirmar Reserva'
            )}
          </RippleButton>
        </div>

        {/* Terms Notice */}
        <p className="text-xs text-[#666666] text-center">
          Ao confirmar esta reserva, aceita os nossos{' '}
          <a href="/termos" className="text-black hover:underline">
            termos e condições
          </a>
          .
        </p>
      </div>

      {/* Loading Animation for Booking Submission */}
      <LoadingAnimation isLoading={isSubmitting} />
    </div>
  );
}
