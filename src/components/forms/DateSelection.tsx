'use client';

import { useState, useEffect } from 'react';
import {
  format,
  isBefore,
  isAfter,
  isSameDay,
  parseISO,
  differenceInDays,
} from 'date-fns';
import { pt } from 'date-fns/locale';
import {
  CalendarIcon,
  ExclamationTriangleIcon,
} from '@heroicons/react/24/outline';
import { supabase } from '@/lib/supabase';
import { BookingFormState } from './BookingForm';

interface DateSelectionProps {
  vehicleId: string;
  selectedDates: {
    start_date?: string;
    end_date?: string;
  };
  onDatesSelect: (data: Partial<BookingFormState>) => void;
}

interface BookedDate {
  start_date: string;
  end_date: string;
}

export default function DateSelection({
  vehicleId,
  selectedDates,
  onDatesSelect,
}: DateSelectionProps) {
  const [startDate, setStartDate] = useState<Date | null>(
    selectedDates.start_date ? parseISO(selectedDates.start_date) : null
  );
  const [endDate, setEndDate] = useState<Date | null>(
    selectedDates.end_date ? parseISO(selectedDates.end_date) : null
  );
  const [bookedDates, setBookedDates] = useState<BookedDate[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [validationError, setValidationError] = useState<string | null>(null);

  useEffect(() => {
    fetchBookedDates();
  }, [vehicleId]); // eslint-disable-line react-hooks/exhaustive-deps

  useEffect(() => {
    validateDates();
  }, [startDate, endDate, bookedDates]); // eslint-disable-line react-hooks/exhaustive-deps

  const fetchBookedDates = async () => {
    if (!supabase) {
      setError('Configuração da base de dados não disponível');
      setLoading(false);
      return;
    }

    try {
      const { data, error } = await supabase
        .from('bookings')
        .select('start_date, end_date')
        .eq('vehicle_id', vehicleId)
        .in('status', ['pendente', 'confirmado']);

      if (error) throw error;

      setBookedDates(data || []);
    } catch (err) {
      console.error('Error fetching booked dates:', err);
      setError('Erro ao carregar datas reservadas. Tente novamente.');
    } finally {
      setLoading(false);
    }
  };

  const validateDates = () => {
    setValidationError(null);

    if (!startDate || !endDate) return;

    // Check if end date is before start date
    if (isBefore(endDate, startDate)) {
      setValidationError('A data de fim deve ser posterior à data de início.');
      return;
    }

    // Check if dates are in the past
    const today = new Date();
    today.setHours(0, 0, 0, 0);

    if (isBefore(startDate, today)) {
      setValidationError('Não é possível reservar datas no passado.');
      return;
    }

    // Enhanced conflict checking - more comprehensive overlap detection
    const hasConflict = bookedDates.some((booking) => {
      const bookingStart = parseISO(booking.start_date);
      const bookingEnd = parseISO(booking.end_date);

      // Comprehensive overlap detection:
      // 1. Selected start date falls within existing booking
      // 2. Selected end date falls within existing booking
      // 3. Selected dates completely encompass existing booking
      // 4. Any exact date matches (start/end combinations)
      return (
        // Start date conflicts
        (isAfter(startDate, bookingStart) && isBefore(startDate, bookingEnd)) ||
        isSameDay(startDate, bookingStart) ||
        isSameDay(startDate, bookingEnd) ||
        // End date conflicts
        (isAfter(endDate, bookingStart) && isBefore(endDate, bookingEnd)) ||
        isSameDay(endDate, bookingStart) ||
        isSameDay(endDate, bookingEnd) ||
        // Selected period encompasses existing booking
        (isBefore(startDate, bookingStart) && isAfter(endDate, bookingEnd)) ||
        // Existing booking encompasses selected period
        (isBefore(bookingStart, startDate) && isAfter(bookingEnd, endDate))
      );
    });

    if (hasConflict) {
      setValidationError(
        'As datas selecionadas conflituam com uma reserva existente. Por favor, escolha outras datas.'
      );
      return;
    }

    // Additional validation: minimum booking period (optional)
    const daysDifference = differenceInDays(endDate, startDate);
    if (daysDifference < 0) {
      setValidationError('Período de reserva inválido.');
      return;
    }
  };

  const isDateBooked = (date: Date): boolean => {
    return bookedDates.some((booking) => {
      const bookingStart = parseISO(booking.start_date);
      const bookingEnd = parseISO(booking.end_date);
      return (
        (isAfter(date, bookingStart) && isBefore(date, bookingEnd)) ||
        isSameDay(date, bookingStart) ||
        isSameDay(date, bookingEnd)
      );
    });
  };

  const isDateDisabled = (date: Date): boolean => {
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    return isBefore(date, today) || isDateBooked(date);
  };

  const handleDateChange = (date: Date, type: 'start' | 'end') => {
    if (type === 'start') {
      setStartDate(date);
      // If end date is before new start date, clear it
      if (endDate && isBefore(endDate, date)) {
        setEndDate(null);
      }
    } else {
      setEndDate(date);
    }
  };

  const handleConfirmDates = () => {
    if (!startDate || !endDate || validationError) return;

    onDatesSelect({
      start_date: format(startDate, 'yyyy-MM-dd'),
      end_date: format(endDate, 'yyyy-MM-dd'),
    });
  };

  const generateCalendarDays = (month: Date) => {
    const firstDay = new Date(month.getFullYear(), month.getMonth(), 1);
    const lastDay = new Date(month.getFullYear(), month.getMonth() + 1, 0);
    const startCalendar = new Date(firstDay);
    startCalendar.setDate(startCalendar.getDate() - firstDay.getDay());

    const days = [];
    const current = new Date(startCalendar);

    for (let i = 0; i < 42; i++) {
      days.push(new Date(current));
      current.setDate(current.getDate() + 1);
    }

    return days;
  };

  const [currentMonth, setCurrentMonth] = useState(new Date());

  if (loading) {
    return (
      <div className="text-center py-12">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-black mx-auto mb-4"></div>
        <p className="text-[#666666]">A carregar disponibilidade...</p>
      </div>
    );
  }

  if (error) {
    return (
      <div className="text-center py-12">
        <p className="text-red-600 mb-4">{error}</p>
        <button
          onClick={fetchBookedDates}
          className="bg-black text-white px-4 py-2 rounded-full font-semibold hover:bg-[#1A1A1A] transition-all duration-300"
        >
          Tentar Novamente
        </button>
      </div>
    );
  }

  const calendarDays = generateCalendarDays(currentMonth);

  return (
    <div>
      <h2 className="font-heading text-2xl font-bold text-black mb-2">
        Selecione as Datas
      </h2>
      <p className="text-[#666666] mb-8">
        Escolha as datas de início e fim da sua reserva. As datas já reservadas
        aparecem a cinzento.
      </p>

      {/* Selected Dates Display */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-8">
        <div className="bg-[#F5F1E8] rounded-lg p-4">
          <div className="flex items-center mb-2">
            <CalendarIcon className="w-5 h-5 text-[#666666] mr-2" />
            <span className="font-semibold text-black">Data de Início</span>
          </div>
          <p className="text-lg text-black">
            {startDate
              ? format(startDate, "dd 'de' MMMM 'de' yyyy", { locale: pt })
              : 'Selecione uma data'}
          </p>
        </div>

        <div className="bg-[#F5F1E8] rounded-lg p-4">
          <div className="flex items-center mb-2">
            <CalendarIcon className="w-5 h-5 text-[#666666] mr-2" />
            <span className="font-semibold text-black">Data de Fim</span>
          </div>
          <p className="text-lg text-black">
            {endDate
              ? format(endDate, "dd 'de' MMMM 'de' yyyy", { locale: pt })
              : 'Selecione uma data'}
          </p>
        </div>
      </div>

      {/* Validation Error */}
      {validationError && (
        <div className="bg-red-50 border border-red-200 rounded-lg p-4 mb-6">
          <div className="flex items-center">
            <ExclamationTriangleIcon className="w-5 h-5 text-red-600 mr-2" />
            <p className="text-red-800">{validationError}</p>
          </div>
        </div>
      )}

      {/* Calendar */}
      <div className="bg-white border border-[#E8E0D0] rounded-lg p-6">
        {/* Calendar Header */}
        <div className="flex items-center justify-between mb-6">
          <button
            onClick={() =>
              setCurrentMonth(
                new Date(
                  currentMonth.getFullYear(),
                  currentMonth.getMonth() - 1
                )
              )
            }
            className="p-2 hover:bg-[#F5F1E8] rounded-lg transition-colors duration-200"
          >
            ←
          </button>
          <h3 className="font-heading text-lg font-semibold text-black">
            {format(currentMonth, 'MMMM yyyy', { locale: pt })}
          </h3>
          <button
            onClick={() =>
              setCurrentMonth(
                new Date(
                  currentMonth.getFullYear(),
                  currentMonth.getMonth() + 1
                )
              )
            }
            className="p-2 hover:bg-[#F5F1E8] rounded-lg transition-colors duration-200"
          >
            →
          </button>
        </div>

        {/* Days of Week */}
        <div className="grid grid-cols-7 gap-1 mb-2">
          {['Dom', 'Seg', 'Ter', 'Qua', 'Qui', 'Sex', 'Sáb'].map((day) => (
            <div
              key={day}
              className="p-2 text-center text-sm font-semibold text-[#666666]"
            >
              {day}
            </div>
          ))}
        </div>

        {/* Calendar Days */}
        <div className="grid grid-cols-7 gap-1">
          {calendarDays.map((day, index) => {
            const isCurrentMonth = day.getMonth() === currentMonth.getMonth();
            const isDisabled = isDateDisabled(day);
            const isSelected =
              (startDate && isSameDay(day, startDate)) ||
              (endDate && isSameDay(day, endDate));
            const isInRange =
              startDate &&
              endDate &&
              isAfter(day, startDate) &&
              isBefore(day, endDate);

            return (
              <button
                key={index}
                onClick={() => {
                  if (isDisabled || !isCurrentMonth) return;

                  if (!startDate || (startDate && endDate)) {
                    // Start new selection
                    setStartDate(day);
                    setEndDate(null);
                  } else if (isAfter(day, startDate)) {
                    // Set end date
                    setEndDate(day);
                  } else {
                    // Set new start date
                    setStartDate(day);
                    setEndDate(null);
                  }
                }}
                disabled={isDisabled || !isCurrentMonth}
                className={`
                  p-2 text-sm rounded-lg transition-all duration-200
                  ${!isCurrentMonth ? 'text-[#E8E0D0]' : ''}
                  ${isDisabled ? 'text-[#E8E0D0] cursor-not-allowed' : 'hover:bg-[#F5F1E8]'}
                  ${isSelected ? 'bg-black text-white' : ''}
                  ${isInRange ? 'bg-[#F5F1E8]' : ''}
                  ${isCurrentMonth && !isDisabled && !isSelected ? 'text-black' : ''}
                `}
              >
                {day.getDate()}
              </button>
            );
          })}
        </div>
      </div>

      {/* Legend */}
      <div className="flex flex-wrap gap-4 mt-6 text-sm">
        <div className="flex items-center">
          <div className="w-4 h-4 bg-black rounded mr-2"></div>
          <span className="text-[#666666]">Data selecionada</span>
        </div>
        <div className="flex items-center">
          <div className="w-4 h-4 bg-[#F5F1E8] rounded mr-2"></div>
          <span className="text-[#666666]">Período selecionado</span>
        </div>
        <div className="flex items-center">
          <div className="w-4 h-4 bg-[#E8E0D0] rounded mr-2"></div>
          <span className="text-[#666666]">Data indisponível</span>
        </div>
      </div>

      {/* Confirm Button */}
      {startDate && endDate && !validationError && (
        <div className="mt-8 text-center">
          <button
            onClick={handleConfirmDates}
            className="bg-black text-white px-8 py-3 rounded-full font-semibold hover:bg-[#1A1A1A] transition-all duration-300"
          >
            Confirmar Datas
          </button>
        </div>
      )}
    </div>
  );
}
