'use client';

import {
  ChevronLeftIcon,
  ChevronRightIcon,
  CheckIcon,
} from '@heroicons/react/24/outline';
import VehicleSelection from './VehicleSelection';
import DateSelection from './DateSelection';
import CustomerDetails from './CustomerDetails';
import BookingConfirmation from './BookingConfirmation';
import { BookingFormData, Vehicle } from '@/types';
import { useBookingForm } from '@/hooks/useBookingForm';
import { FadeIn } from '@/components/animations';

interface BookingFormProps {}

export type BookingStep = 'vehicle' | 'dates' | 'details' | 'confirmation';

export interface BookingFormState extends Partial<BookingFormData> {
  selectedVehicle?: Vehicle;
}

export default function BookingForm({}: BookingFormProps) {
  const {
    currentStep,
    formData,
    isSubmitting,
    isCompleted,
    isLoadingPreselection,
    steps,
    currentStepIndex,
    isFirstStep,
    goToNextStep,
    goToPreviousStep,
    canProceedToNext,
    handleStepComplete,
    setIsSubmitting,
    setIsCompleted,
    resetForm,
  } = useBookingForm();

  const handleBookingSubmit = async () => {
    setIsSubmitting(true);
    try {
      // This will be handled by the BookingConfirmation component
      setIsCompleted(true);
    } catch (error) {
      console.error('Booking submission error:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  if (isLoadingPreselection) {
    return (
      <div className="bg-white rounded-xl shadow-lg p-8 text-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-black mx-auto mb-4"></div>
        <h2 className="font-heading text-xl font-bold text-black mb-2">
          A preparar a sua reserva...
        </h2>
        <p className="text-primary-mediumGray">
          Estamos a carregar o veículo selecionado.
        </p>
      </div>
    );
  }

  if (isCompleted) {
    return (
      <div className="bg-white rounded-xl shadow-lg p-8 text-center">
        <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-6">
          <CheckIcon className="w-8 h-8 text-green-600" />
        </div>
        <h2 className="font-heading text-2xl font-bold text-black mb-4">
          Reserva Enviada com Sucesso!
        </h2>
        <p className="text-primary-mediumGray mb-6">
          Recebemos a sua reserva e entraremos em contacto consigo em breve para
          confirmar todos os detalhes.
        </p>
        <button
          onClick={resetForm}
          className="bg-primary-black text-primary-white px-6 py-3 rounded-full font-semibold hover:bg-primary-darkGray transition-all duration-300"
        >
          Fazer Nova Reserva
        </button>
      </div>
    );
  }

  return (
    <div className="bg-white rounded-xl shadow-lg overflow-hidden">
      {/* Progress Steps */}
      <div className="bg-background-cream px-6 py-4">
        <div className="flex items-center justify-between">
          {steps.map((step, index) => (
            <div key={step.key} className="flex items-center">
              <div className="flex items-center">
                <div
                  className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-semibold ${
                    index <= currentStepIndex
                      ? 'bg-primary-black text-primary-white'
                      : 'bg-background-beige text-primary-lightGray'
                  }`}
                >
                  {index < currentStepIndex ? (
                    <CheckIcon className="w-4 h-4" />
                  ) : (
                    index + 1
                  )}
                </div>
                <div className="ml-3 hidden md:block">
                  <p
                    className={`text-sm font-semibold ${
                      index <= currentStepIndex
                        ? 'text-primary-black'
                        : 'text-primary-lightGray'
                    }`}
                  >
                    {step.title}
                  </p>
                  <p className="text-xs text-primary-mediumGray">
                    {step.description}
                  </p>
                </div>
              </div>
              {index < steps.length - 1 && (
                <ChevronRightIcon className="w-4 h-4 text-primary-lightGray mx-4" />
              )}
            </div>
          ))}
        </div>
      </div>

      {/* Step Content */}
      <div className="p-6">
        {currentStep === 'vehicle' && (
          <FadeIn key="vehicle" duration={0.4}>
            <VehicleSelection
              selectedVehicle={formData.selectedVehicle}
              onVehicleSelect={handleStepComplete}
            />
          </FadeIn>
        )}

        {currentStep === 'dates' && (
          <FadeIn key="dates" duration={0.4}>
            <DateSelection
              vehicleId={formData.vehicle_id!}
              selectedDates={{
                start_date: formData.start_date,
                end_date: formData.end_date,
              }}
              onDatesSelect={handleStepComplete}
            />
          </FadeIn>
        )}

        {currentStep === 'details' && (
          <FadeIn key="details" duration={0.4}>
            <CustomerDetails
              customerData={{
                customer_name: formData.customer_name,
                customer_email: formData.customer_email,
                customer_phone: formData.customer_phone,
                notes: formData.notes,
              }}
              onDetailsSubmit={handleStepComplete}
            />
          </FadeIn>
        )}

        {currentStep === 'confirmation' && (
          <FadeIn key="confirmation" duration={0.4}>
            <BookingConfirmation
              bookingData={
                formData as BookingFormData & { selectedVehicle: Vehicle }
              }
              onConfirm={handleBookingSubmit}
              isSubmitting={isSubmitting}
            />
          </FadeIn>
        )}
      </div>

      {/* Navigation Buttons */}
      {currentStep !== 'confirmation' && (
        <div className="bg-background-cream px-6 py-4 flex justify-between">
          <button
            onClick={goToPreviousStep}
            disabled={isFirstStep}
            className={`flex items-center px-4 py-2 rounded-full font-semibold transition-all duration-300 ${
              isFirstStep
                ? 'text-primary-lightGray cursor-not-allowed'
                : 'text-primary-black border-2 border-primary-black hover:bg-primary-black hover:text-primary-white'
            }`}
          >
            <ChevronLeftIcon className="w-4 h-4 mr-2" />
            Anterior
          </button>

          <button
            onClick={goToNextStep}
            disabled={!canProceedToNext()}
            className={`flex items-center px-6 py-2 rounded-full font-semibold transition-all duration-300 ${
              canProceedToNext()
                ? 'bg-primary-black text-primary-white hover:bg-primary-darkGray'
                : 'bg-background-beige text-primary-lightGray cursor-not-allowed'
            }`}
          >
            Próximo
            <ChevronRightIcon className="w-4 h-4 ml-2" />
          </button>
        </div>
      )}
    </div>
  );
}
