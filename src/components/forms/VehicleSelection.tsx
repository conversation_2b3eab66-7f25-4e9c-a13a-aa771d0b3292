'use client';

import { useState, useEffect } from 'react';
import { CheckIcon } from '@heroicons/react/24/outline';
import { Vehicle } from '@/types';
import { supabase } from '@/lib/supabase';
import { BookingFormState } from './BookingForm';
import Image from 'next/image';

interface VehicleSelectionProps {
  selectedVehicle?: Vehicle;
  onVehicleSelect: (data: Partial<BookingFormState>) => void;
}

export default function VehicleSelection({
  selectedVehicle,
  onVehicleSelect,
}: VehicleSelectionProps) {
  const [vehicles, setVehicles] = useState<Vehicle[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    fetchVehicles();
  }, []);

  const fetchVehicles = async () => {
    if (!supabase) {
      setError('Configuração da base de dados não disponível');
      setLoading(false);
      return;
    }

    try {
      const { data, error } = await supabase
        .from('vehicles')
        .select('*')
        .order('name');

      if (error) throw error;

      setVehicles(data || []);
    } catch (err) {
      console.error('Error fetching vehicles:', err);
      setError('Erro ao carregar veículos. Tente novamente.');
    } finally {
      setLoading(false);
    }
  };

  const handleVehicleSelect = (vehicle: Vehicle) => {
    onVehicleSelect({
      vehicle_id: vehicle.id,
      selectedVehicle: vehicle,
    });
  };

  if (loading) {
    return (
      <div className="text-center py-12">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-black mx-auto mb-4"></div>
        <p className="text-[#666666]">A carregar veículos...</p>
      </div>
    );
  }

  if (error) {
    return (
      <div className="text-center py-12">
        <p className="text-red-600 mb-4">{error}</p>
        <button
          onClick={fetchVehicles}
          className="bg-black text-white px-4 py-2 rounded-full font-semibold hover:bg-[#1A1A1A] transition-all duration-300"
        >
          Tentar Novamente
        </button>
      </div>
    );
  }

  if (vehicles.length === 0) {
    return (
      <div className="text-center py-12">
        <p className="text-[#666666]">Nenhum veículo disponível no momento.</p>
      </div>
    );
  }

  return (
    <div>
      <h2 className="font-heading text-2xl font-bold text-black mb-2">
        Escolha o Seu Veículo Vintage
      </h2>
      <p className="text-[#666666] mb-8">
        Selecione o veículo perfeito para o seu evento de marketing.
      </p>

      {/* Vehicle Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
        {vehicles.map((vehicle) => (
          <div
            key={vehicle.id}
            onClick={() => handleVehicleSelect(vehicle)}
            className={`bg-white border-2 rounded-xl p-6 cursor-pointer transition-all duration-300 hover:shadow-lg hover:-translate-y-1 ${
              selectedVehicle?.id === vehicle.id
                ? 'border-black shadow-lg'
                : 'border-[#E8E0D0] hover:border-[#999999]'
            }`}
          >
            {/* Vehicle Image */}
            {vehicle.photo_url && (
              <div className="relative w-full h-48 mb-4 rounded-lg overflow-hidden bg-[#F5F1E8]">
                <Image
                  src={vehicle.photo_url}
                  alt={`${vehicle.name} - ${vehicle.year}`}
                  fill
                  className="object-cover"
                  sizes="(max-width: 768px) 100vw, 50vw"
                />
              </div>
            )}

            {/* Vehicle Info */}
            <div className="flex justify-between items-start mb-3">
              <div>
                <h3 className="font-heading text-xl font-bold text-black mb-1">
                  {vehicle.name}
                </h3>
                <p className="text-[#666666] text-sm">Ano: {vehicle.year}</p>
              </div>
              {vehicle.price && (
                <div className="text-right">
                  <p className="text-2xl font-bold text-black">
                    €{vehicle.price}
                  </p>
                  <p className="text-xs text-[#666666]">por dia</p>
                </div>
              )}
            </div>

            {/* Vehicle Description */}
            {vehicle.description && (
              <p className="text-[#666666] text-sm mb-4 line-clamp-3">
                {vehicle.description}
              </p>
            )}

            {/* Selection Indicator */}
            <div className="flex items-center justify-between">
              <div className="flex items-center">
                {selectedVehicle?.id === vehicle.id && (
                  <div className="flex items-center text-black">
                    <CheckIcon className="w-5 h-5 mr-2" />
                    <span className="font-semibold">Selecionado</span>
                  </div>
                )}
              </div>
              <button
                className={`px-4 py-2 rounded-full font-semibold transition-all duration-300 ${
                  selectedVehicle?.id === vehicle.id
                    ? 'bg-black text-white'
                    : 'border-2 border-black text-black hover:bg-black hover:text-white'
                }`}
              >
                {selectedVehicle?.id === vehicle.id
                  ? 'Selecionado'
                  : 'Selecionar'}
              </button>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
}
