'use client';

import { useState } from 'react';
import {
  UserIcon,
  EnvelopeIcon,
  PhoneIcon,
  ChatBubbleLeftRightIcon,
} from '@heroicons/react/24/outline';
import { BookingFormState } from './BookingForm';

interface CustomerDetailsProps {
  customerData: {
    customer_name?: string;
    customer_email?: string;
    customer_phone?: string;
    notes?: string;
  };
  onDetailsSubmit: (data: Partial<BookingFormState>) => void;
}

interface FormErrors {
  customer_name?: string;
  customer_email?: string;
  customer_phone?: string;
}

export default function CustomerDetails({
  customerData,
  onDetailsSubmit,
}: CustomerDetailsProps) {
  const [formData, setFormData] = useState({
    customer_name: customerData.customer_name || '',
    customer_email: customerData.customer_email || '',
    customer_phone: customerData.customer_phone || '',
    notes: customerData.notes || '',
  });
  const [errors, setErrors] = useState<FormErrors>({});
  const [isSubmitting, setIsSubmitting] = useState(false);

  const validateForm = (): boolean => {
    const newErrors: FormErrors = {};

    // Name validation
    if (!formData.customer_name.trim()) {
      newErrors.customer_name = 'O nome é obrigatório';
    } else if (formData.customer_name.trim().length < 2) {
      newErrors.customer_name = 'O nome deve ter pelo menos 2 caracteres';
    }

    // Email validation
    if (!formData.customer_email.trim()) {
      newErrors.customer_email = 'O email é obrigatório';
    } else {
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      if (!emailRegex.test(formData.customer_email)) {
        newErrors.customer_email = 'Por favor, insira um email válido';
      }
    }

    // Phone validation (optional but if provided, should be valid)
    if (formData.customer_phone.trim()) {
      const phoneRegex = /^[+]?[\d\s\-\(\)]{9,}$/;
      if (!phoneRegex.test(formData.customer_phone.trim())) {
        newErrors.customer_phone =
          'Por favor, insira um número de telefone válido';
      }
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleInputChange = (field: keyof typeof formData, value: string) => {
    setFormData((prev) => ({ ...prev, [field]: value }));

    // Clear error when user starts typing
    if (errors[field as keyof FormErrors]) {
      setErrors((prev) => ({ ...prev, [field]: undefined }));
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) return;

    setIsSubmitting(true);

    try {
      // Simulate a brief delay for better UX
      await new Promise((resolve) => setTimeout(resolve, 500));

      onDetailsSubmit({
        customer_name: formData.customer_name.trim(),
        customer_email: formData.customer_email.trim(),
        customer_phone: formData.customer_phone.trim() || undefined,
        notes: formData.notes.trim() || undefined,
      });
    } catch (error) {
      console.error('Error submitting customer details:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div>
      <h2 className="font-heading text-2xl font-bold text-black mb-2">
        Os Seus Dados de Contacto
      </h2>
      <p className="text-[#666666] mb-8">
        Preencha os seus dados para que possamos entrar em contacto consigo para
        confirmar a reserva.
      </p>

      <form onSubmit={handleSubmit} className="space-y-6">
        {/* Name Field */}
        <div>
          <label
            htmlFor="customer_name"
            className="block text-sm font-semibold text-black mb-2"
          >
            Nome Completo *
          </label>
          <div className="relative">
            <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <UserIcon className="h-5 w-5 text-[#666666]" />
            </div>
            <input
              type="text"
              id="customer_name"
              value={formData.customer_name}
              onChange={(e) =>
                handleInputChange('customer_name', e.target.value)
              }
              className={`block w-full pl-10 pr-3 py-3 border-2 rounded-lg focus:outline-none transition-colors duration-200 ${
                errors.customer_name
                  ? 'border-red-500 focus:border-red-500'
                  : 'border-[#E8E0D0] focus:border-black'
              }`}
              placeholder="Introduza o seu nome completo"
            />
          </div>
          {errors.customer_name && (
            <p className="mt-1 text-sm text-red-600">{errors.customer_name}</p>
          )}
        </div>

        {/* Email Field */}
        <div>
          <label
            htmlFor="customer_email"
            className="block text-sm font-semibold text-black mb-2"
          >
            Email *
          </label>
          <div className="relative">
            <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <EnvelopeIcon className="h-5 w-5 text-[#666666]" />
            </div>
            <input
              type="email"
              id="customer_email"
              value={formData.customer_email}
              onChange={(e) =>
                handleInputChange('customer_email', e.target.value)
              }
              className={`block w-full pl-10 pr-3 py-3 border-2 rounded-lg focus:outline-none transition-colors duration-200 ${
                errors.customer_email
                  ? 'border-red-500 focus:border-red-500'
                  : 'border-[#E8E0D0] focus:border-black'
              }`}
              placeholder="<EMAIL>"
            />
          </div>
          {errors.customer_email && (
            <p className="mt-1 text-sm text-red-600">{errors.customer_email}</p>
          )}
        </div>

        {/* Phone Field */}
        <div>
          <label
            htmlFor="customer_phone"
            className="block text-sm font-semibold text-black mb-2"
          >
            Telefone{' '}
            <span className="text-[#666666] font-normal">(opcional)</span>
          </label>
          <div className="relative">
            <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <PhoneIcon className="h-5 w-5 text-[#666666]" />
            </div>
            <input
              type="tel"
              id="customer_phone"
              value={formData.customer_phone}
              onChange={(e) =>
                handleInputChange('customer_phone', e.target.value)
              }
              className={`block w-full pl-10 pr-3 py-3 border-2 rounded-lg focus:outline-none transition-colors duration-200 ${
                errors.customer_phone
                  ? 'border-red-500 focus:border-red-500'
                  : 'border-[#E8E0D0] focus:border-black'
              }`}
              placeholder="+351 912 345 678"
            />
          </div>
          {errors.customer_phone && (
            <p className="mt-1 text-sm text-red-600">{errors.customer_phone}</p>
          )}
        </div>

        {/* Notes Field */}
        <div>
          <label
            htmlFor="notes"
            className="block text-sm font-semibold text-black mb-2"
          >
            Notas Adicionais{' '}
            <span className="text-[#666666] font-normal">(opcional)</span>
          </label>
          <div className="relative">
            <div className="absolute top-3 left-3 pointer-events-none">
              <ChatBubbleLeftRightIcon className="h-5 w-5 text-[#666666]" />
            </div>
            <textarea
              id="notes"
              rows={4}
              value={formData.notes}
              onChange={(e) => handleInputChange('notes', e.target.value)}
              className="block w-full pl-10 pr-3 py-3 border-2 border-[#E8E0D0] rounded-lg focus:outline-none focus:border-black transition-colors duration-200 resize-none"
              placeholder="Conte-nos mais sobre o seu evento ou necessidades especiais..."
            />
          </div>
          <p className="mt-1 text-xs text-[#666666]">
            Máximo 500 caracteres ({formData.notes.length}/500)
          </p>
        </div>

        {/* Privacy Notice */}
        <div className="bg-[#F5F1E8] rounded-lg p-4">
          <p className="text-sm text-[#666666]">
            <strong className="text-black">Proteção de Dados:</strong> Os seus
            dados pessoais serão utilizados apenas para processar a sua reserva
            e entrar em contacto consigo. Não partilhamos as suas informações
            com terceiros.
          </p>
        </div>

        {/* Submit Button */}
        <div className="pt-4">
          <button
            type="submit"
            disabled={isSubmitting}
            className={`w-full py-3 px-6 rounded-full font-semibold transition-all duration-300 ${
              isSubmitting
                ? 'bg-[#E8E0D0] text-[#999999] cursor-not-allowed'
                : 'bg-black text-white hover:bg-[#1A1A1A] hover:-translate-y-0.5'
            }`}
          >
            {isSubmitting ? (
              <div className="flex items-center justify-center">
                <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-[#999999] mr-2"></div>
                A processar...
              </div>
            ) : (
              'Continuar para Confirmação'
            )}
          </button>
        </div>
      </form>

      {/* Required Fields Note */}
      <p className="text-xs text-[#666666] text-center mt-4">
        * Campos obrigatórios
      </p>
    </div>
  );
}
