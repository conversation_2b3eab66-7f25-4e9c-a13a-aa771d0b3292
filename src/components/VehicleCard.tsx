'use client';

import Image from 'next/image';
import { useRouter } from 'next/navigation';
import { Vehicle } from '@/types';
import { HoverCard } from '@/components/animations';

interface VehicleCardProps {
  vehicle: Vehicle;
  className?: string;
}

export default function VehicleCard({
  vehicle,
  className = 'card-animated',
}: VehicleCardProps) {
  const router = useRouter();

  const formatPrice = (price: number | null) => {
    if (!price) return 'Preço sob consulta';
    return `€${price}/dia`;
  };

  const handleReserveClick = (e: React.MouseEvent) => {
    e.stopPropagation(); // Prevent modal from opening
    router.push(`/reservas?vehicle=${vehicle.id}`);
  };

  return (
    <HoverCard className={className}>
      {/* Vehicle Image - Clickable */}
      <div
        className="relative h-64 bg-background-beige overflow-hidden cursor-pointer rounded-image"
        data-click-target="image"
      >
        {vehicle.photo_url ? (
          <Image
            src={vehicle.photo_url}
            alt={`${vehicle.name} - ${vehicle.year}`}
            fill
            className="object-cover rounded-image transition-opacity duration-300 hover:opacity-90"
            sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
          />
        ) : (
          <div className="flex items-center justify-center h-full text-primary-mediumGray">
            <div className="text-center">
              <svg
                className="w-16 h-16 mx-auto mb-4"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-4m-5 0H9m0 0H5m0 0h2M7 7h10M7 11h10M7 15h10"
                />
              </svg>
              <p className="text-sm font-medium">Imagem em breve</p>
            </div>
          </div>
        )}

        {/* Year Badge */}
        <div className="absolute top-4 left-4 bg-primary-black text-primary-white px-3 py-1 rounded-full text-sm font-semibold">
          {vehicle.year}
        </div>

        {/* Driver Included Badge */}
        <div className="absolute top-4 right-4 bg-gradient-to-r from-amber-500 to-yellow-600 text-white px-3 py-1 rounded-full text-xs font-semibold shadow-lg">
          Motorista Incluído
        </div>
      </div>

      {/* Vehicle Info */}
      <div className="p-6">
        <h3
          className="font-heading text-card font-card text-primary-black mb-3 line-clamp-2 cursor-pointer hover:text-primary-mediumGray transition-colors duration-300"
          data-click-target="title"
        >
          {vehicle.name}
        </h3>

        {vehicle.description && (
          <p className="text-body text-primary-mediumGray leading-body mb-4 line-clamp-3">
            {vehicle.description}
          </p>
        )}

        <div className="flex justify-between items-center mb-4">
          <span className="text-xl font-bold text-primary-black">
            {formatPrice(vehicle.price)}
          </span>
          <button
            className="text-primary-mediumGray hover:text-primary-black font-medium transition-colors duration-300 flex items-center gap-1"
            data-click-target="details"
          >
            Ver Detalhes
            <svg
              className="w-4 h-4"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M9 5l7 7-7 7"
              />
            </svg>
          </button>
        </div>

        {/* Book Now Button */}
        <div className="pt-4 border-t border-primary-lightGray/30">
          <button
            className="btn-primary w-full"
            onClick={handleReserveClick}
            data-click-target="book"
          >
            Reservar Agora
          </button>
        </div>
      </div>
    </HoverCard>
  );
}
