'use client';

import { useAuth } from '@/contexts/AuthContext';
import Link from 'next/link';
import { useRouter } from 'next/navigation';

interface AdminLayoutProps {
  children: React.ReactNode;
}

export default function AdminLayout({ children }: AdminLayoutProps) {
  const { user, signOut } = useAuth();
  const router = useRouter();

  const handleSignOut = async () => {
    await signOut();
    router.push('/admin/login');
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Admin Header */}
      <header className="bg-white shadow-sm border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-4">
            <div className="flex items-center">
              <Link href="/admin/dashboard" className="flex items-center">
                <h1 className="font-logo text-xl font-bold text-primary-black">
                  Vintage Marketing Portugal
                </h1>
                <span className="ml-3 px-2 py-1 text-xs font-medium bg-primary-black text-white rounded">
                  ADMIN
                </span>
              </Link>
            </div>

            <div className="flex items-center space-x-4">
              <span className="text-sm text-primary-mediumGray">
                Olá, {user?.email}
              </span>
              <button
                onClick={handleSignOut}
                className="text-sm text-primary-mediumGray hover:text-primary-black"
              >
                Sair
              </button>
            </div>
          </div>
        </div>
      </header>

      {/* Admin Navigation */}
      <nav className="bg-white shadow-sm border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex space-x-8">
            <Link
              href="/admin/dashboard"
              className="border-b-2 border-transparent hover:border-primary-black py-4 px-1 text-sm font-medium text-primary-mediumGray hover:text-primary-black"
            >
              Dashboard
            </Link>
            <Link
              href="/admin/bookings"
              className="border-b-2 border-transparent hover:border-primary-black py-4 px-1 text-sm font-medium text-primary-mediumGray hover:text-primary-black"
            >
              Reservas
            </Link>
            <Link
              href="/admin/vehicles"
              className="border-b-2 border-transparent hover:border-primary-black py-4 px-1 text-sm font-medium text-primary-mediumGray hover:text-primary-black"
            >
              Veículos
            </Link>
            <Link
              href="/admin/email-templates"
              className="border-b-2 border-transparent hover:border-primary-black py-4 px-1 text-sm font-medium text-primary-mediumGray hover:text-primary-black"
            >
              Templates Email
            </Link>
            <Link
              href="/admin/seo"
              className="border-b-2 border-transparent hover:border-primary-black py-4 px-1 text-sm font-medium text-primary-mediumGray hover:text-primary-black"
            >
              SEO
            </Link>
          </div>
        </div>
      </nav>

      {/* Main Content */}
      <main className="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">{children}</main>

      {/* Quick Link to Main Site */}
      <div className="fixed bottom-4 right-4">
        <Link
          href="/"
          className="bg-primary-black text-white px-4 py-2 rounded-lg shadow-lg hover:bg-primary-mediumGray transition-colors text-sm"
        >
          ← Ver Site Principal
        </Link>
      </div>
    </div>
  );
}
