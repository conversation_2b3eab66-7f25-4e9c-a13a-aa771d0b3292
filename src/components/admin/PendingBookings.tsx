'use client';

import { useState, useEffect } from 'react';
import { supabase } from '@/lib/supabase';
import { Booking, Vehicle } from '@/types';

interface BookingWithVehicle extends Booking {
  vehicle?: Vehicle;
}

export default function PendingBookings() {
  const [bookings, setBookings] = useState<BookingWithVehicle[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    loadPendingBookings();
  }, []);

  const loadPendingBookings = async () => {
    if (!supabase) {
      setError('Supabase não está configurado');
      setIsLoading(false);
      return;
    }

    try {
      // Load pending bookings with vehicle information
      const { data: bookingsData, error: bookingsError } = await supabase
        .from('bookings')
        .select(
          `
          *,
          vehicles (
            id,
            name,
            year,
            photo_url
          )
        `
        )
        .eq('status', 'pending')
        .order('created_at', { ascending: false });

      if (bookingsError) {
        console.error('Error loading pending bookings:', bookingsError);
        setError('Erro ao carregar reservas pendentes');
        return;
      }

      // Transform the data to match our interface
      const bookingsWithVehicles: BookingWithVehicle[] =
        bookingsData?.map((booking) => ({
          ...booking,
          vehicle: booking.vehicles as Vehicle,
        })) || [];

      setBookings(bookingsWithVehicles);
    } catch (error) {
      console.error('Unexpected error loading pending bookings:', error);
      setError('Erro inesperado ao carregar reservas');
    } finally {
      setIsLoading(false);
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('pt-PT', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric',
    });
  };

  const formatDateTime = (dateString: string) => {
    return new Date(dateString).toLocaleString('pt-PT', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center py-8">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-black"></div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="bg-red-50 border border-red-200 rounded-md p-4">
        <p className="text-red-700">{error}</p>
      </div>
    );
  }

  if (bookings.length === 0) {
    return (
      <div className="text-center py-8">
        <p className="text-gray-500">Não há reservas pendentes no momento.</p>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      <h3 className="text-lg font-medium text-gray-900 mb-4">
        Reservas Pendentes ({bookings.length})
      </h3>

      <div className="grid gap-4">
        {bookings.map((booking) => (
          <div
            key={booking.id}
            className="bg-white border border-gray-200 rounded-lg p-6 shadow-sm hover:shadow-md transition-shadow"
          >
            <div className="flex items-start justify-between">
              <div className="flex-1">
                <div className="flex items-center space-x-3 mb-3">
                  <div className="flex-shrink-0">
                    <div className="w-10 h-10 bg-background-beige rounded-full flex items-center justify-center">
                      <span className="text-accent-vintage font-medium text-sm">
                        ⏳
                      </span>
                    </div>
                  </div>
                  <div>
                    <h4 className="text-lg font-medium text-gray-900">
                      {booking.customer_name}
                    </h4>
                    <p className="text-sm text-gray-500">
                      Reserva criada em{' '}
                      {formatDateTime(booking.created_at || '')}
                    </p>
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <h5 className="text-sm font-medium text-gray-700 mb-2">
                      Dados do Cliente
                    </h5>
                    <div className="space-y-1 text-sm text-gray-600">
                      <p>
                        <span className="font-medium">Email:</span>{' '}
                        {booking.customer_email}
                      </p>
                      {booking.customer_phone && (
                        <p>
                          <span className="font-medium">Telefone:</span>{' '}
                          {booking.customer_phone}
                        </p>
                      )}
                    </div>
                  </div>

                  <div>
                    <h5 className="text-sm font-medium text-gray-700 mb-2">
                      Detalhes da Reserva
                    </h5>
                    <div className="space-y-1 text-sm text-gray-600">
                      <p>
                        <span className="font-medium">Veículo:</span>{' '}
                        {booking.vehicle?.name || 'N/A'}
                      </p>
                      <p>
                        <span className="font-medium">Período:</span>{' '}
                        {formatDate(booking.start_date)} -{' '}
                        {formatDate(booking.end_date)}
                      </p>
                      {booking.total_price && (
                        <p>
                          <span className="font-medium">Preço:</span> €
                          {booking.total_price.toFixed(2)}
                        </p>
                      )}
                    </div>
                  </div>
                </div>

                {booking.notes && (
                  <div className="mt-4">
                    <h5 className="text-sm font-medium text-gray-700 mb-1">
                      Notas
                    </h5>
                    <p className="text-sm text-gray-600 bg-gray-50 p-3 rounded-md">
                      {booking.notes}
                    </p>
                  </div>
                )}
              </div>

              <div className="flex-shrink-0 ml-4">
                <div className="flex flex-col space-y-2">
                  <button
                    className="px-4 py-2 bg-green-600 text-white text-sm font-medium rounded-md hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-500"
                    onClick={() => {
                      // TODO: Implement approve booking functionality
                      console.log('Approve booking:', booking.id);
                    }}
                  >
                    Aprovar
                  </button>
                  <button
                    className="px-4 py-2 bg-red-600 text-white text-sm font-medium rounded-md hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-500"
                    onClick={() => {
                      // TODO: Implement cancel booking functionality
                      console.log('Cancel booking:', booking.id);
                    }}
                  >
                    Cancelar
                  </button>
                  <button
                    className="px-4 py-2 bg-gray-600 text-white text-sm font-medium rounded-md hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-gray-500"
                    onClick={() => {
                      // TODO: Implement view details functionality
                      console.log('View details:', booking.id);
                    }}
                  >
                    Ver Detalhes
                  </button>
                </div>
              </div>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
}
