'use client';

import { useState, useEffect } from 'react';
import { Tab } from '@headlessui/react';
import { supabase } from '@/lib/supabase';
import { Booking } from '@/types';
import PendingBookings from './PendingBookings';
import { CounterAnimation } from '@/components/animations';

interface DashboardStats {
  totalBookings: number;
  pendingBookings: number;
  confirmedBookings: number;
  totalRevenue: number;
}

function classNames(...classes: string[]) {
  return classes.filter(Boolean).join(' ');
}

export default function AdminDashboard() {
  const [stats, setStats] = useState<DashboardStats>({
    totalBookings: 0,
    pendingBookings: 0,
    confirmedBookings: 0,
    totalRevenue: 0,
  });
  const [recentBookings, setRecentBookings] = useState<Booking[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    loadDashboardData();
  }, []);

  const loadDashboardData = async () => {
    if (!supabase) return;

    try {
      // Load booking statistics
      const { data: bookings, error: bookingsError } = await supabase
        .from('bookings')
        .select('*');

      if (bookingsError) {
        console.error('Error loading bookings:', bookingsError);
        return;
      }

      // Calculate statistics
      const totalBookings = bookings?.length || 0;
      const pendingBookings =
        bookings?.filter((b) => b.status === 'pending').length || 0;
      const confirmedBookings =
        bookings?.filter((b) => b.status === 'confirmed').length || 0;
      const totalRevenue =
        bookings?.reduce((sum, booking) => {
          return sum + (booking.total_price || 0);
        }, 0) || 0;

      setStats({
        totalBookings,
        pendingBookings,
        confirmedBookings,
        totalRevenue,
      });

      // Get recent bookings (last 5)
      const recentBookingsData =
        bookings
          ?.sort(
            (a, b) =>
              new Date(b.created_at).getTime() -
              new Date(a.created_at).getTime()
          )
          .slice(0, 5) || [];

      setRecentBookings(recentBookingsData);
    } catch (error) {
      console.error('Error loading dashboard data:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const tabs = [
    { name: 'Visão Geral', content: 'overview' },
    { name: 'Reservas Pendentes', content: 'pending-bookings' },
    { name: 'Atividade Recente', content: 'recent-activity' },
  ];

  if (isLoading) {
    return (
      <div className="flex items-center justify-center py-12">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-black"></div>
      </div>
    );
  }

  return (
    <div className="px-4 sm:px-6 lg:px-8">
      <div className="sm:flex sm:items-center">
        <div className="sm:flex-auto">
          <h1 className="text-2xl font-semibold text-gray-900">
            Dashboard Administrativo
          </h1>
          <p className="mt-2 text-sm text-gray-700">
            Visão geral das reservas e atividade do sistema.
          </p>
        </div>
      </div>

      {/* Statistics Cards */}
      <div className="mt-8 grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4">
        <div className="bg-white overflow-hidden shadow rounded-lg">
          <div className="p-5">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <div className="w-8 h-8 bg-blue-500 rounded-md flex items-center justify-center">
                  <span className="text-white text-sm font-medium">📊</span>
                </div>
              </div>
              <div className="ml-5 w-0 flex-1">
                <dl>
                  <dt className="text-sm font-medium text-gray-500 truncate">
                    Total de Reservas
                  </dt>
                  <dd className="text-lg font-medium text-gray-900">
                    <CounterAnimation
                      from={0}
                      to={stats.totalBookings}
                      duration={1.5}
                    />
                  </dd>
                </dl>
              </div>
            </div>
          </div>
        </div>

        <div className="bg-white overflow-hidden shadow rounded-lg">
          <div className="p-5">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <div className="w-8 h-8 bg-accent-vintage rounded-md flex items-center justify-center">
                  <span className="text-white text-sm font-medium">⏳</span>
                </div>
              </div>
              <div className="ml-5 w-0 flex-1">
                <dl>
                  <dt className="text-sm font-medium text-gray-500 truncate">
                    Reservas Pendentes
                  </dt>
                  <dd className="text-lg font-medium text-gray-900">
                    <CounterAnimation
                      from={0}
                      to={stats.pendingBookings}
                      duration={1.8}
                    />
                  </dd>
                </dl>
              </div>
            </div>
          </div>
        </div>

        <div className="bg-white overflow-hidden shadow rounded-lg">
          <div className="p-5">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <div className="w-8 h-8 bg-green-500 rounded-md flex items-center justify-center">
                  <span className="text-white text-sm font-medium">✅</span>
                </div>
              </div>
              <div className="ml-5 w-0 flex-1">
                <dl>
                  <dt className="text-sm font-medium text-gray-500 truncate">
                    Reservas Confirmadas
                  </dt>
                  <dd className="text-lg font-medium text-gray-900">
                    <CounterAnimation
                      from={0}
                      to={stats.confirmedBookings}
                      duration={2.1}
                    />
                  </dd>
                </dl>
              </div>
            </div>
          </div>
        </div>

        <div className="bg-white overflow-hidden shadow rounded-lg">
          <div className="p-5">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <div className="w-8 h-8 bg-purple-500 rounded-md flex items-center justify-center">
                  <span className="text-white text-sm font-medium">💰</span>
                </div>
              </div>
              <div className="ml-5 w-0 flex-1">
                <dl>
                  <dt className="text-sm font-medium text-gray-500 truncate">
                    Receita Total
                  </dt>
                  <dd className="text-lg font-medium text-gray-900">
                    €
                    <CounterAnimation
                      from={0}
                      to={Math.round(stats.totalRevenue * 100) / 100}
                      duration={2.4}
                    />
                  </dd>
                </dl>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Tabbed Interface */}
      <div className="mt-8">
        <Tab.Group>
          <Tab.List className="flex space-x-1 rounded-xl bg-blue-900/20 p-1">
            {tabs.map((tab) => (
              <Tab
                key={tab.name}
                className={({ selected }) =>
                  classNames(
                    'w-full rounded-lg py-2.5 text-sm font-medium leading-5',
                    'ring-white ring-opacity-60 ring-offset-2 ring-offset-blue-400 focus:outline-none focus:ring-2',
                    selected
                      ? 'bg-white text-blue-700 shadow'
                      : 'text-blue-100 hover:bg-white/[0.12] hover:text-white'
                  )
                }
              >
                {tab.name}
              </Tab>
            ))}
          </Tab.List>
          <Tab.Panels className="mt-2">
            <Tab.Panel className="rounded-xl bg-white p-3">
              <div className="text-sm text-gray-500">
                <h3 className="text-lg font-medium text-gray-900 mb-4">
                  Visão Geral do Sistema
                </h3>
                <p>
                  Bem-vindo ao painel administrativo do Vintage Marketing
                  Portugal.
                </p>
                <p className="mt-2">
                  Use as abas acima para navegar entre diferentes secções ou
                  utilize o menu principal para aceder a funcionalidades
                  específicas como gestão de reservas, veículos, templates de
                  email e configurações de SEO.
                </p>
              </div>
            </Tab.Panel>

            <Tab.Panel className="rounded-xl bg-white p-3">
              <PendingBookings />
            </Tab.Panel>

            <Tab.Panel className="rounded-xl bg-white p-3">
              <h3 className="text-lg font-medium text-gray-900 mb-4">
                Atividade Recente
              </h3>
              {recentBookings.length === 0 ? (
                <p className="text-gray-500">Nenhuma atividade recente.</p>
              ) : (
                <div className="space-y-3">
                  {recentBookings.map((booking) => (
                    <div
                      key={booking.id}
                      className="border-l-4 border-blue-400 pl-4"
                    >
                      <p className="text-sm font-medium text-gray-900">
                        Nova reserva de {booking.customer_name}
                      </p>
                      <p className="text-sm text-gray-500">
                        {booking.customer_email} •{' '}
                        {new Date(booking.created_at).toLocaleDateString(
                          'pt-PT'
                        )}
                      </p>
                    </div>
                  ))}
                </div>
              )}
            </Tab.Panel>
          </Tab.Panels>
        </Tab.Group>
      </div>
    </div>
  );
}
