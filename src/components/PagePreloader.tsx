'use client';

import { useEffect } from 'react';
import { useRouter } from 'next/navigation';

/**
 * PagePreloader component that preloads main pages during development
 * to minimize Next.js compilation delays on first navigation
 */
export default function PagePreloader() {
  const router = useRouter();

  useEffect(() => {
    // Only preload in development mode
    if (process.env.NODE_ENV === 'development') {
      const pagesToPreload = [
        '/servicos',
        '/frota',
        '/reservas',
        '/contacto',
        '/termos',
      ];

      // Preload pages with a small delay to avoid blocking initial page load
      const preloadTimer = setTimeout(() => {
        pagesToPreload.forEach((page) => {
          router.prefetch(page);
        });
      }, 2000); // Wait 2 seconds after initial page load

      return () => clearTimeout(preloadTimer);
    }
  }, [router]);

  // This component doesn't render anything
  return null;
}
