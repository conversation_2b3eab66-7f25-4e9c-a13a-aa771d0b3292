'use client';

import { useState } from 'react';
import {
  FadeIn,
  SlideIn,
  ScaleIn,
  StaggerContainer,
  ScrollReveal,
  TextReveal,
  StaggeredCard,
  HoverCard,
  AnimatedButton,
  RippleButton,
  CounterAnimation,
  LoadingAnimation,
  PageTransition,
} from '@/components/animations';
import { useAnimationOptimization } from '@/hooks/usePerformanceOptimization';

export default function AnimationTestSuite() {
  const [showLoading, setShowLoading] = useState(false);
  const [counter, setCounter] = useState(0);
  const { shouldOptimize, prefersReducedMotion, animationConfig } =
    useAnimationOptimization();

  const testAnimations = () => {
    setCounter((prev) => prev + 10);
    setShowLoading(true);
    setTimeout(() => setShowLoading(false), 2000);
  };

  return (
    <div className="min-h-screen bg-background-cream p-8">
      <div className="max-w-6xl mx-auto">
        {/* Performance Status */}
        <div className="mb-8 p-4 bg-white rounded-lg shadow-sm">
          <h2 className="text-xl font-bold mb-4">
            Animation Performance Status
          </h2>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
            <div>
              <strong>Should Optimize:</strong> {shouldOptimize ? 'Yes' : 'No'}
            </div>
            <div>
              <strong>Reduced Motion:</strong>{' '}
              {prefersReducedMotion ? 'Yes' : 'No'}
            </div>
            <div>
              <strong>Duration:</strong> {animationConfig.duration}s
            </div>
          </div>
        </div>

        {/* Basic Animations */}
        <section className="mb-12">
          <h2 className="text-2xl font-bold mb-6">Basic Animations</h2>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <FadeIn delay={0.1}>
              <div className="card p-6 text-center">
                <h3 className="font-semibold mb-2">FadeIn</h3>
                <p className="text-sm text-gray-600">Opacity + Y transform</p>
              </div>
            </FadeIn>

            <SlideIn direction="left" delay={0.2}>
              <div className="card p-6 text-center">
                <h3 className="font-semibold mb-2">SlideIn Left</h3>
                <p className="text-sm text-gray-600">X transform + opacity</p>
              </div>
            </SlideIn>

            <ScaleIn delay={0.3}>
              <div className="card p-6 text-center">
                <h3 className="font-semibold mb-2">ScaleIn</h3>
                <p className="text-sm text-gray-600">
                  Scale transform + opacity
                </p>
              </div>
            </ScaleIn>
          </div>
        </section>

        {/* Stagger Animations */}
        <section className="mb-12">
          <h2 className="text-2xl font-bold mb-6">Stagger Animations</h2>
          <StaggerContainer staggerDelay={0.1}>
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
              {[1, 2, 3, 4].map((item) => (
                <div key={item} className="card p-4 text-center">
                  <h4 className="font-semibold">Item {item}</h4>
                  <p className="text-sm text-gray-600">Staggered reveal</p>
                </div>
              ))}
            </div>
          </StaggerContainer>
        </section>

        {/* Scroll-based Animations */}
        <section className="mb-12">
          <h2 className="text-2xl font-bold mb-6">Scroll-based Animations</h2>
          <div className="space-y-8">
            <ScrollReveal delay={0.1}>
              <div className="card p-6">
                <h3 className="font-semibold mb-2">ScrollReveal</h3>
                <p>This animates when it comes into view during scroll.</p>
              </div>
            </ScrollReveal>

            <TextReveal className="text-3xl font-bold text-center">
              Text Reveal Animation
            </TextReveal>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <StaggeredCard index={0}>
                <HoverCard className="card p-6">
                  <h3 className="font-semibold mb-2">Staggered + Hover Card</h3>
                  <p>Combines stagger timing with hover effects</p>
                </HoverCard>
              </StaggeredCard>

              <StaggeredCard index={1}>
                <HoverCard className="card p-6">
                  <h3 className="font-semibold mb-2">Second Card</h3>
                  <p>Delayed by stagger timing</p>
                </HoverCard>
              </StaggeredCard>
            </div>
          </div>
        </section>

        {/* Micro-interactions */}
        <section className="mb-12">
          <h2 className="text-2xl font-bold mb-6">Micro-interactions</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            <div className="card p-6 text-center">
              <div className="w-12 h-12 bg-primary-black text-white rounded-full flex items-center justify-center mx-auto mb-4">
                <span>🎯</span>
              </div>
              <h3 className="font-semibold">Static Icon</h3>
            </div>

            <div className="card p-6 text-center">
              <RippleButton
                onClick={testAnimations}
                className="btn-primary w-full mb-2"
              >
                RippleButton
              </RippleButton>
              <p className="text-sm text-gray-600">Click to test</p>
            </div>

            <div className="card p-6 text-center">
              <AnimatedButton
                href="#"
                variant="secondary"
                className="w-full"
                enableGlow={true}
                glowColor="rgba(0, 0, 0, 0.15)"
              >
                GlowEffect
              </AnimatedButton>
            </div>

            <div className="card p-6 text-center">
              <h3 className="font-semibold mb-2">CounterAnimation</h3>
              <div className="text-2xl font-bold text-primary-black">
                <CounterAnimation from={0} to={counter} duration={1.5} />
              </div>
            </div>
          </div>
        </section>

        {/* Performance Test */}
        <section className="mb-12">
          <h2 className="text-2xl font-bold mb-6">Performance Test</h2>
          <div className="card p-6">
            <p className="mb-4">
              This section tests animation performance with multiple
              simultaneous animations.
            </p>
            <button onClick={testAnimations} className="btn-primary mb-4">
              Trigger Performance Test
            </button>
            <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-8 gap-2">
              {Array.from({ length: 16 }, (_, i) => (
                <FadeIn key={`perf-${counter}-${i}`} delay={i * 0.05}>
                  <div className="aspect-square bg-primary-black rounded-lg flex items-center justify-center text-white text-sm">
                    {i + 1}
                  </div>
                </FadeIn>
              ))}
            </div>
          </div>
        </section>

        {/* Loading Animation Test */}
        <LoadingAnimation isLoading={showLoading} />
      </div>
    </div>
  );
}
