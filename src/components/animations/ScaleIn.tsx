'use client';

import { motion } from 'motion/react';
import { ReactNode } from 'react';
import { useAnimationOptimization } from '@/hooks/usePerformanceOptimization';

interface ScaleInProps {
  children: ReactNode;
  delay?: number;
  duration?: number;
  className?: string;
}

export default function ScaleIn({
  children,
  delay = 0,
  duration = 0.5,
  className = '',
}: ScaleInProps) {
  const { animationConfig, prefersReducedMotion } = useAnimationOptimization();

  // Skip animation if user prefers reduced motion
  if (prefersReducedMotion) {
    return <div className={className}>{children}</div>;
  }

  return (
    <motion.div
      initial={{ scale: 0.8, opacity: 0 }}
      animate={{ scale: 1, opacity: 1 }}
      transition={{
        duration: duration * (animationConfig.duration / 0.5),
        delay,
        ease:
          animationConfig.ease === 'linear'
            ? 'linear'
            : [0.25, 0.46, 0.45, 0.94],
      }}
      className={className}
    >
      {children}
    </motion.div>
  );
}
