'use client';

import { motion } from 'motion/react';
import { ReactNode } from 'react';
import { useAnimationOptimization } from '@/hooks/usePerformanceOptimization';

interface SlideInProps {
  children: ReactNode;
  direction?: 'left' | 'right' | 'up' | 'down';
  delay?: number;
  duration?: number;
  className?: string;
}

export default function SlideIn({
  children,
  direction = 'left',
  delay = 0,
  duration = 0.6,
  className = '',
}: SlideInProps) {
  const { animationConfig, prefersReducedMotion } = useAnimationOptimization();

  // Skip animation if user prefers reduced motion
  if (prefersReducedMotion) {
    return <div className={className}>{children}</div>;
  }

  const getInitialPosition = () => {
    const distance = animationConfig.duration < 0.3 ? 20 : 50; // Reduce distance for low-end devices
    switch (direction) {
      case 'left':
        return { x: -distance, opacity: 0 };
      case 'right':
        return { x: distance, opacity: 0 };
      case 'up':
        return { y: -distance, opacity: 0 };
      case 'down':
        return { y: distance, opacity: 0 };
      default:
        return { x: -distance, opacity: 0 };
    }
  };

  return (
    <motion.div
      initial={getInitialPosition()}
      animate={{ x: 0, y: 0, opacity: 1 }}
      transition={{
        duration: duration * (animationConfig.duration / 0.6),
        delay,
        ease:
          animationConfig.ease === 'linear'
            ? 'linear'
            : [0.25, 0.46, 0.45, 0.94],
      }}
      className={className}
    >
      {children}
    </motion.div>
  );
}
