'use client';

import { motion } from 'motion/react';
import { ReactNode } from 'react';
import { useAnimationOptimization } from '@/hooks/usePerformanceOptimization';

interface FadeInProps {
  children: ReactNode;
  delay?: number;
  duration?: number;
  className?: string;
}

export default function FadeIn({
  children,
  delay = 0,
  duration = 0.6,
  className = '',
}: FadeInProps) {
  const { animationConfig, prefersReducedMotion } = useAnimationOptimization();

  // Skip animation if user prefers reduced motion
  if (prefersReducedMotion) {
    return <div className={className}>{children}</div>;
  }

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{
        duration: duration * (animationConfig.duration / 0.6),
        delay,
        ease:
          animationConfig.ease === 'linear'
            ? 'linear'
            : [0.25, 0.46, 0.45, 0.94],
      }}
      className={className}
    >
      {children}
    </motion.div>
  );
}
