'use client';

import { motion } from 'motion/react';
import { ReactNode, useState, useEffect } from 'react';
import Link from 'next/link';

interface AnimatedLinkProps {
  children: ReactNode;
  href: string;
  className?: string;
  underlineColor?: string;
}

export function AnimatedLink({
  children,
  href,
  className = '',
  underlineColor = 'bg-primary-black',
}: AnimatedLinkProps) {
  // Check if href is internal (starts with /) or external
  const isInternalLink = href.startsWith('/');

  if (isInternalLink) {
    return (
      <Link href={href} className={`relative inline-block ${className}`}>
        <motion.div whileHover="hover" initial="initial">
          <motion.span
            variants={{
              initial: { color: 'inherit' },
              hover: { color: 'inherit' },
            }}
            transition={{ duration: 0.2 }}
          >
            {children}
          </motion.span>
          <motion.div
            className={`absolute bottom-0 left-0 h-0.5 ${underlineColor}`}
            variants={{
              initial: { width: 0 },
              hover: { width: '100%' },
            }}
            transition={{ duration: 0.3, ease: 'easeOut' }}
          />
        </motion.div>
      </Link>
    );
  }

  return (
    <motion.a
      href={href}
      className={`relative inline-block ${className}`}
      target="_blank"
      rel="noopener noreferrer"
      whileHover="hover"
      initial="initial"
    >
      <motion.span
        variants={{
          initial: { color: 'inherit' },
          hover: { color: 'inherit' },
        }}
        transition={{ duration: 0.2 }}
      >
        {children}
      </motion.span>
      <motion.div
        className={`absolute bottom-0 left-0 h-0.5 ${underlineColor}`}
        variants={{
          initial: { width: 0 },
          hover: { width: '100%' },
        }}
        transition={{ duration: 0.3, ease: 'easeOut' }}
      />
    </motion.a>
  );
}

interface FloatingButtonProps {
  children: ReactNode;
  onClick?: () => void;
  className?: string;
  variant?: 'primary' | 'secondary' | 'cta';
}

export function FloatingButton({
  children,
  onClick,
  className = '',
  variant = 'primary',
}: FloatingButtonProps) {
  const baseClasses = {
    primary: 'btn-primary',
    secondary: 'btn-secondary',
    cta: 'btn-cta',
  };

  return (
    <motion.button
      onClick={onClick}
      className={`${baseClasses[variant]} ${className} relative overflow-hidden`}
      whileHover={{
        scale: 1.02,
        boxShadow: '0 6px 15px rgba(0,0,0,0.08)',
      }}
      whileTap={{ scale: 0.98 }}
      initial={{ y: 0 }}
      animate={{
        y: [0, -1, 0],
        transition: {
          duration: 3,
          repeat: Infinity,
          ease: 'easeInOut',
        },
      }}
    >
      <motion.div
        className="absolute inset-0 bg-white opacity-0"
        whileHover={{ opacity: 0.1 }}
        transition={{ duration: 0.2 }}
      />
      {children}
    </motion.button>
  );
}

interface MagneticButtonProps {
  children: ReactNode;
  className?: string;
  strength?: number;
}

export function MagneticButton({
  children,
  className = '',
  strength = 0.15,
}: MagneticButtonProps) {
  return (
    <motion.div
      className={`cursor-pointer ${className}`}
      whileHover={{
        scale: 1.02,
        transition: { duration: 0.3 },
      }}
      onMouseMove={(e) => {
        const rect = e.currentTarget.getBoundingClientRect();
        const x = e.clientX - rect.left - rect.width / 2;
        const y = e.clientY - rect.top - rect.height / 2;

        e.currentTarget.style.transform = `translate(${x * strength}px, ${y * strength}px) scale(1.02)`;
      }}
      onMouseLeave={(e) => {
        e.currentTarget.style.transform = 'translate(0px, 0px) scale(1)';
      }}
    >
      {children}
    </motion.div>
  );
}

interface RippleButtonProps {
  children: ReactNode;
  onClick?: () => void;
  className?: string;
  disabled?: boolean;
  type?: 'button' | 'submit' | 'reset';
  rippleColor?: string;
}

export function RippleButton({
  children,
  onClick,
  className = '',
  disabled = false,
  type = 'button',
  rippleColor = 'rgba(255, 255, 255, 0.3)',
}: RippleButtonProps) {
  return (
    <motion.button
      type={type}
      onClick={onClick}
      disabled={disabled}
      className={`relative overflow-hidden ${className}`}
      whileTap={disabled ? undefined : 'tap'}
      variants={{
        tap: {
          scale: 0.95,
          transition: { duration: 0.1 },
        },
      }}
    >
      <motion.div
        className="absolute inset-0 rounded-full opacity-0"
        style={{
          backgroundColor: rippleColor.replace(
            'rgba(255, 255, 255, 0.3)',
            'white'
          ),
        }}
        variants={{
          tap: {
            scale: [0, 2],
            opacity: [0.3, 0],
            transition: { duration: 0.4 },
          },
        }}
      />
      {children}
    </motion.button>
  );
}

interface GlowEffectProps {
  children: ReactNode;
  className?: string;
  glowColor?: string;
}

export function GlowEffect({
  children,
  className = '',
  glowColor = 'rgba(0, 0, 0, 0.1)',
}: GlowEffectProps) {
  return (
    <motion.div
      className={`relative ${className}`}
      whileHover={{
        boxShadow: `0 0 20px ${glowColor}`,
        transition: { duration: 0.3 },
      }}
    >
      {children}
    </motion.div>
  );
}

interface CounterAnimationProps {
  from: number;
  to: number;
  duration?: number;
  className?: string;
}

export function CounterAnimation({
  from,
  to,
  duration = 2,
  className = '',
}: CounterAnimationProps) {
  const [count, setCount] = useState(from);

  useEffect(() => {
    const startTime = Date.now();
    const startValue = from;
    const endValue = to;
    const totalDuration = duration * 1000;

    const updateCount = () => {
      const elapsed = Date.now() - startTime;
      const progress = Math.min(elapsed / totalDuration, 1);
      const easeOutProgress = 1 - Math.pow(1 - progress, 3);
      const currentValue =
        startValue + (endValue - startValue) * easeOutProgress;

      setCount(Math.round(currentValue));

      if (progress < 1) {
        requestAnimationFrame(updateCount);
      }
    };

    requestAnimationFrame(updateCount);
  }, [from, to, duration]);

  return (
    <motion.span
      className={className}
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      transition={{ duration: 0.5 }}
    >
      {count}
    </motion.span>
  );
}
