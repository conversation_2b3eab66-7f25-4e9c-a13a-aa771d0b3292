'use client';

import { useState, useEffect, useRef } from 'react';
import { motion, PanInfo, useReducedMotion } from 'motion/react';
import { useRouter } from 'next/navigation';
import { Vehicle } from '@/types';
import VehicleCard from './VehicleCard';
import { ScrollReveal } from '@/components/animations';
import { useAnimationOptimization } from '@/hooks/usePerformanceOptimization';
import { getResponsiveAnimationConfig } from '@/utils/animationOptimization';

interface VehicleCarouselProps {
  vehicles: Vehicle[];
  onVehicleClick: (vehicle: Vehicle) => void;
  className?: string;
}

export default function VehicleCarousel({
  vehicles,
  onVehicleClick,
  className = '',
}: VehicleCarouselProps) {
  const router = useRouter();
  const [currentIndex, setCurrentIndex] = useState(0);
  const [isMobile, setIsMobile] = useState(false);
  const [isHydrated, setIsHydrated] = useState(false);

  const [isUserInteracting, setIsUserInteracting] = useState(false);
  const [isVisible, setIsVisible] = useState(true); // Start as visible, hide if needed
  const [isDragging, setIsDragging] = useState(false); // Track drag state for pointer events

  const [screenWidth, setScreenWidth] = useState(0);

  const containerRef = useRef<HTMLDivElement>(null);
  const indicatorRefs = useRef<(HTMLButtonElement | null)[]>([]);
  const prefersReducedMotion = useReducedMotion();
  const { animationConfig } = useAnimationOptimization();
  const responsiveConfig = getResponsiveAnimationConfig();

  // Hydration and mobile detection
  useEffect(() => {
    setIsHydrated(true);

    const checkMobile = () => {
      const mobile = window.innerWidth < 768;
      setIsMobile(mobile);

      // Update screen width for full-width calculations
      setScreenWidth(window.innerWidth);
    };

    checkMobile();

    // Debounced resize handler for performance
    let timeoutId: NodeJS.Timeout;
    const debouncedResize = () => {
      clearTimeout(timeoutId);
      timeoutId = setTimeout(checkMobile, 150);
    };

    window.addEventListener('resize', debouncedResize);
    return () => {
      window.removeEventListener('resize', debouncedResize);
      clearTimeout(timeoutId);
    };
  }, []);

  // Intersection Observer for performance optimization
  useEffect(() => {
    if (!containerRef.current || !isMobile || !isHydrated) return;

    const observer = new IntersectionObserver(
      ([entry]) => {
        setIsVisible(entry.isIntersecting);
      },
      {
        threshold: 0.1,
        rootMargin: '50px 0px',
      }
    );

    observer.observe(containerRef.current);
    return () => observer.disconnect();
  }, [isMobile, isHydrated]); // Re-run when mobile state or hydration changes

  // Shared calculation function for consistent card positioning
  const getCardDimensions = () => {
    const fullWidth =
      screenWidth > 0
        ? screenWidth
        : typeof window !== 'undefined'
          ? window.innerWidth
          : 375;
    const previewSpace = 20;
    const cardWidth = fullWidth - previewSpace * 2; // Space for balanced previews

    return { fullWidth, previewSpace, cardWidth };
  };

  // Keyboard navigation with focus management
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if (!isMobile) return;

      // Only handle keyboard navigation when carousel is focused
      const activeElement = document.activeElement;
      const isCarouselFocused = containerRef.current?.contains(activeElement);

      if (!isCarouselFocused) return;

      if (event.key === 'ArrowLeft' && currentIndex > 0) {
        event.preventDefault();
        setCurrentIndex((prev) => prev - 1);
        setIsUserInteracting(true);
      } else if (
        event.key === 'ArrowRight' &&
        currentIndex < vehicles.length - 1
      ) {
        event.preventDefault();
        setCurrentIndex((prev) => prev + 1);
        setIsUserInteracting(true);
      } else if (event.key === 'Home') {
        event.preventDefault();
        setCurrentIndex(0);
        setIsUserInteracting(true);
      } else if (event.key === 'End') {
        event.preventDefault();
        setCurrentIndex(vehicles.length - 1);
        setIsUserInteracting(true);
      }
    };

    window.addEventListener('keydown', handleKeyDown);
    return () => window.removeEventListener('keydown', handleKeyDown);
  }, [currentIndex, vehicles.length, isMobile]);

  // Reset user interaction flag after a delay
  useEffect(() => {
    if (isUserInteracting) {
      const timer = setTimeout(() => setIsUserInteracting(false), 1000);
      return () => clearTimeout(timer);
    }
  }, [isUserInteracting]);

  // Don't render carousel on desktop or before hydration
  if (!isHydrated || !isMobile) {
    return null;
  }

  if (vehicles.length === 0) {
    return null;
  }

  // Use shared calculation function for consistent positioning
  const { cardWidth } = getCardDimensions();

  // For card stacking, we don't need container translation
  // All cards will be positioned absolutely and stacked on top of each other

  return (
    <ScrollReveal className={`w-full ${className}`}>
      <div
        ref={containerRef}
        className="relative focus-within:outline-none carousel-container w-full"
        role="region"
        aria-label="Carrossel de veículos"
        aria-live={isUserInteracting ? 'polite' : 'off'}
        aria-describedby="carousel-instructions"
        tabIndex={0}
        style={{
          minHeight: '600px', // Increased height to provide more space for stacked cards
          width: '100%',
          overflow: 'visible', // Critical: Allow cards to overflow and remain visible
          // Add padding to ensure cards don't get clipped at edges
          paddingLeft: '20px',
          paddingRight: '20px',
        }}
      >
        {/* Hidden instructions for screen readers */}
        <div id="carousel-instructions" className="sr-only">
          Use as setas esquerda e direita para navegar entre os veículos.
          Pressione Home para ir ao primeiro veículo ou End para ir ao último.
        </div>
        {/* Card Stacking Container - Inspired by React Native Snap Carousel */}
        <div
          className="relative flex justify-center items-start"
          style={{
            height: '550px', // Increased height to accommodate stacked cards with overflow
            width: '100%',
            overflow: 'visible', // Allow cards to overflow container boundaries
            perspective: '1000px', // Add 3D perspective for better stacking effect
            // Ensure container provides adequate space for card stacking
            paddingTop: '20px',
            paddingBottom: '20px',
            // Center the stacking area
            paddingLeft: '20px',
            paddingRight: '20px',
            touchAction: 'pan-y pinch-zoom', // Allow vertical scroll and zoom, prevent horizontal scroll
          }}
        >
          {vehicles.map((vehicle, index) => {
            // Calculate stacking effects based on distance from active card
            const distanceFromActive = Math.abs(index - currentIndex);
            const isActive = index === currentIndex;
            const relativePosition = index - currentIndex; // Negative = left, Positive = right

            // Card stacking calculations inspired by React Native Snap Carousel "stack" layout
            const layoutCardOffset = 18; // Default offset from React Native Snap Carousel
            const horizontalOffset = relativePosition * layoutCardOffset; // Horizontal spacing between cards
            const verticalOffset = distanceFromActive * 8; // Vertical offset for depth (reduced for subtlety)
            const scaleReduction = isActive
              ? 1
              : Math.max(0.95, 1 - distanceFromActive * 0.025); // More subtle scale reduction
            const opacityReduction = isActive
              ? 1
              : Math.max(0.88, 1 - distanceFromActive * 0.06); // More subtle opacity fade
            const zIndexValue = vehicles.length - distanceFromActive; // Higher z-index for cards closer to active

            // Use full card width but allow overflow for proper stacking visibility
            const stackCardWidth = cardWidth * 0.92; // Slightly smaller for better stacking visibility

            return (
              <motion.div
                key={vehicle.id}
                className="absolute carousel-item"
                // Apply both tap and pan gestures to the active card for proper disambiguation
                {...(isActive
                  ? {
                      onTap: (event: any) => {
                        // Handle clicks on different elements within the card
                        const target = event.target.closest(
                          '[data-click-target]'
                        );
                        const clickType =
                          target?.getAttribute('data-click-target');

                        switch (clickType) {
                          case 'image':
                          case 'title':
                            // On mobile, disable image/title clicks to prevent accidental navigation during swipe gestures
                            // On desktop, allow normal click behavior
                            if (!isMobile) {
                              onVehicleClick(vehicle);
                            }
                            break;
                          case 'details':
                            onVehicleClick(vehicle);
                            break;
                          case 'book':
                            router.push(`/reservas?vehicle=${vehicle.id}`);
                            break;
                          default:
                            // Click on non-interactive area - do nothing
                            break;
                        }
                      },
                      onPanStart: (_event: any, _info: PanInfo) => {
                        // Motion's built-in 3px threshold detection triggers this
                        setIsDragging(true);
                        setIsUserInteracting(true);
                      },
                      onPan: (_event: any, _info: PanInfo) => {
                        // Track pan movement - info provides point, delta, offset, velocity
                        // Visual feedback could be added here if needed
                      },
                      onPanEnd: (_event: any, info: PanInfo) => {
                        // Reset dragging state with delay to prevent immediate clicks
                        setTimeout(() => setIsDragging(false), 100);

                        // Handle carousel navigation based on pan direction and distance
                        const deltaX = info.offset.x; // Total distance from start
                        const velocityX = info.velocity.x; // Current velocity
                        const threshold = 50; // Minimum distance to trigger navigation

                        // Consider both distance and velocity for better UX
                        const shouldNavigate =
                          Math.abs(deltaX) > threshold ||
                          Math.abs(velocityX) > 500;

                        if (shouldNavigate) {
                          if (deltaX > 0 || velocityX > 500) {
                            // Pan right or fast right swipe - go to previous
                            setCurrentIndex((prev) =>
                              prev > 0 ? prev - 1 : vehicles.length - 1
                            );
                          } else if (deltaX < 0 || velocityX < -500) {
                            // Pan left or fast left swipe - go to next
                            setCurrentIndex((prev) =>
                              prev < vehicles.length - 1 ? prev + 1 : 0
                            );
                          }
                          setIsUserInteracting(true);
                        }
                      },
                    }
                  : {})}
                style={{
                  width: `${stackCardWidth}px`,
                  left: '50%', // Center horizontally
                  top: '50px', // Adjusted top position for better spacing
                  zIndex: zIndexValue,
                  transformOrigin: 'center center',
                  // Ensure card content is not clipped
                  overflow: 'visible',
                  // Add touch action for proper touch support
                  touchAction: isActive ? 'pan-y pinch-zoom' : 'auto',
                  cursor:
                    isActive && isDragging
                      ? 'grabbing'
                      : isActive
                        ? 'grab'
                        : 'default',
                }}
                initial={{ opacity: 0, scale: 0.9, x: '-50%', y: 30 }}
                animate={{
                  opacity: isVisible ? opacityReduction : 0,
                  scale: isVisible ? scaleReduction : 0.9,
                  x: isVisible ? `calc(-50% + ${horizontalOffset}px)` : '-50%', // Center + horizontal offset
                  y: isVisible ? verticalOffset + 50 : 50, // Increased base position for better spacing
                  rotateY: isVisible ? relativePosition * 2 : 0, // Subtle 3D rotation for depth
                }}
                transition={{
                  delay: index * (responsiveConfig.staggerMultiplier * 0.03), // Reduced delay for smoother stacking
                  duration: prefersReducedMotion
                    ? 0.4
                    : animationConfig.duration * 1.0, // Balanced duration
                  type: prefersReducedMotion ? 'tween' : 'spring',
                  stiffness: 300,
                  damping: 30,
                }}
              >
                <VehicleCard vehicle={vehicle} className="card-carousel" />
              </motion.div>
            );
          })}
        </div>

        {/* Navigation Indicators */}
        <div
          className="flex justify-center mt-6 space-x-2"
          role="tablist"
          aria-label="Indicadores de navegação do carrossel"
        >
          {vehicles.map((vehicle, index) => (
            <button
              key={index}
              ref={(el) => {
                indicatorRefs.current[index] = el;
              }}
              onClick={() => {
                setCurrentIndex(index);
                setIsUserInteracting(true);
              }}
              className={`w-2 h-2 rounded-full transition-all duration-300 focus:outline-none focus:ring-2 focus:ring-primary-black focus:ring-offset-2 ${
                index === currentIndex
                  ? 'bg-primary-black w-6'
                  : 'bg-primary-lightGray hover:bg-primary-mediumGray'
              }`}
              role="tab"
              aria-label={`Ir para ${vehicle.name} (${index + 1} de ${vehicles.length})`}
              aria-selected={index === currentIndex}
              aria-controls={`carousel-item-${index}`}
              tabIndex={index === currentIndex ? 0 : -1}
            />
          ))}
        </div>

        {/* Screen Reader Information */}
        <div className="sr-only" aria-live="polite" aria-atomic="true">
          {isUserInteracting &&
            `Navegando para veículo ${currentIndex + 1} de ${vehicles.length}: ${vehicles[currentIndex]?.name}`}
        </div>
      </div>
    </ScrollReveal>
  );
}
