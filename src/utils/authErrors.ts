// Authentication error handling and translation utilities

export interface AuthError {
  message: string;
  type: 'validation' | 'auth' | 'network' | 'permission';
}

/**
 * Translates Supabase authentication errors to user-friendly Portuguese messages
 */
export function translateAuthError(error: any): AuthError {
  if (!error) {
    return {
      message: 'Erro desconhecido',
      type: 'auth',
    };
  }

  const errorMessage = error.message?.toLowerCase() || '';
  const errorCode = error.code || '';

  // Network and connection errors
  if (errorMessage.includes('network') || errorMessage.includes('fetch')) {
    return {
      message:
        'Erro de conexão. Verifique a sua ligação à internet e tente novamente.',
      type: 'network',
    };
  }

  // Invalid login credentials (most common)
  if (
    errorMessage.includes('invalid login credentials') ||
    errorMessage.includes('invalid_credentials') ||
    errorCode === 'invalid_credentials'
  ) {
    return {
      message:
        'Email ou palavra-passe incorretos. Verifique os seus dados e tente novamente.',
      type: 'auth',
    };
  }

  // Email not confirmed
  if (
    errorMessage.includes('email not confirmed') ||
    errorMessage.includes('email_not_confirmed')
  ) {
    return {
      message:
        'Email não confirmado. Verifique a sua caixa de entrada e confirme o email.',
      type: 'auth',
    };
  }

  // Too many requests / Rate limiting
  if (
    errorMessage.includes('too many requests') ||
    errorMessage.includes('rate limit') ||
    errorCode === 'too_many_requests'
  ) {
    return {
      message:
        'Demasiadas tentativas de login. Aguarde alguns minutos antes de tentar novamente.',
      type: 'auth',
    };
  }

  // User not found
  if (
    errorMessage.includes('user not found') ||
    errorMessage.includes('user_not_found')
  ) {
    return {
      message: 'Utilizador não encontrado. Verifique o endereço de email.',
      type: 'auth',
    };
  }

  // Weak password
  if (errorMessage.includes('password') && errorMessage.includes('weak')) {
    return {
      message: 'A palavra-passe é muito fraca. Use pelo menos 8 caracteres.',
      type: 'validation',
    };
  }

  // Invalid email format
  if (
    errorMessage.includes('invalid email') ||
    (errorMessage.includes('email') && errorMessage.includes('invalid'))
  ) {
    return {
      message: 'Formato de email inválido. Verifique o endereço de email.',
      type: 'validation',
    };
  }

  // Signup disabled
  if (errorMessage.includes('signup') && errorMessage.includes('disabled')) {
    return {
      message: 'Registo de novos utilizadores está desativado.',
      type: 'permission',
    };
  }

  // Session expired
  if (errorMessage.includes('session') && errorMessage.includes('expired')) {
    return {
      message: 'Sessão expirada. Faça login novamente.',
      type: 'auth',
    };
  }

  // Generic fallback for unknown errors
  return {
    message: 'Erro de autenticação. Tente novamente ou contacte o suporte.',
    type: 'auth',
  };
}

/**
 * Validates email format
 */
export function validateEmail(email: string): {
  isValid: boolean;
  error?: string;
} {
  if (!email) {
    return {
      isValid: false,
      error: 'Email é obrigatório',
    };
  }

  if (email.length < 3) {
    return {
      isValid: false,
      error: 'Email muito curto',
    };
  }

  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  if (!emailRegex.test(email)) {
    return {
      isValid: false,
      error: 'Formato de email inválido',
    };
  }

  return { isValid: true };
}

/**
 * Validates password
 */
export function validatePassword(password: string): {
  isValid: boolean;
  error?: string;
} {
  if (!password) {
    return {
      isValid: false,
      error: 'Palavra-passe é obrigatória',
    };
  }

  if (password.length < 6) {
    return {
      isValid: false,
      error: 'Palavra-passe deve ter pelo menos 6 caracteres',
    };
  }

  return { isValid: true };
}

/**
 * Validates login form data
 */
export function validateLoginForm(
  email: string,
  password: string
): {
  isValid: boolean;
  errors: { email?: string; password?: string };
} {
  const emailValidation = validateEmail(email);
  const passwordValidation = validatePassword(password);

  const errors: { email?: string; password?: string } = {};

  if (!emailValidation.isValid) {
    errors.email = emailValidation.error;
  }

  if (!passwordValidation.isValid) {
    errors.password = passwordValidation.error;
  }

  return {
    isValid: emailValidation.isValid && passwordValidation.isValid,
    errors,
  };
}
