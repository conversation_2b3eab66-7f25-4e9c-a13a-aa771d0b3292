// Portuguese error messages
export const errorMessages = {
  required: 'Este campo é obrigatório',
  email: 'Por favor, insira um email válido',
  phone: 'Por favor, insira um número de telefone válido',
  dateConflict: 'As datas selecionadas não estão disponíveis',
  dateInvalid: 'Por favor, selecione datas válidas',
  dateRange: 'A data de fim deve ser posterior à data de início',
  networkError: 'Erro de conexão. Tente novamente.',
  uploadError: 'Erro ao carregar imagem. Tente novamente.',
  minLength: (min: number) =>
    `Este campo deve ter pelo menos ${min} caracteres`,
  maxLength: (max: number) =>
    `Este campo não pode ter mais de ${max} caracteres`,
};

// Email validation
export const isValidEmail = (email: string): boolean => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
};

// Phone validation (Portuguese format)
export const isValidPhone = (phone: string): boolean => {
  const phoneRegex = /^(\+351\s?)?[0-9]{9}$/;
  return phoneRegex.test(phone.replace(/\s/g, ''));
};

// Form validation helper
export const validateField = (
  value: string,
  rules: {
    required?: boolean;
    email?: boolean;
    phone?: boolean;
    minLength?: number;
    maxLength?: number;
  }
): string | null => {
  if (rules.required && !value.trim()) {
    return errorMessages.required;
  }

  if (rules.email && value && !isValidEmail(value)) {
    return errorMessages.email;
  }

  if (rules.phone && value && !isValidPhone(value)) {
    return errorMessages.phone;
  }

  if (rules.minLength && value.length < rules.minLength) {
    return errorMessages.minLength(rules.minLength);
  }

  if (rules.maxLength && value.length > rules.maxLength) {
    return errorMessages.maxLength(rules.maxLength);
  }

  return null;
};
