import { format, parseISO, isAfter, isBefore, isEqual } from 'date-fns';
import { pt } from 'date-fns/locale';

// Format date for display in Portuguese
export const formatDate = (
  date: string | Date,
  formatStr: string = 'dd/MM/yyyy'
): string => {
  const dateObj = typeof date === 'string' ? parseISO(date) : date;
  return format(dateObj, formatStr, { locale: pt });
};

// Check if a date range conflicts with existing bookings
export const hasDateConflict = (
  startDate: string,
  endDate: string,
  existingBookings: Array<{ start_date: string; end_date: string }>
): boolean => {
  const start = parseISO(startDate);
  const end = parseISO(endDate);

  return existingBookings.some((booking) => {
    const bookingStart = parseISO(booking.start_date);
    const bookingEnd = parseISO(booking.end_date);

    // Check for any overlap
    return (
      ((isAfter(start, bookingStart) || isEqual(start, bookingStart)) &&
        (isBefore(start, bookingEnd) || isEqual(start, bookingEnd))) ||
      ((isAfter(end, bookingStart) || isEqual(end, bookingStart)) &&
        (isBefore(end, bookingEnd) || isEqual(end, bookingEnd))) ||
      ((isBefore(start, bookingStart) || isEqual(start, bookingStart)) &&
        (isAfter(end, bookingEnd) || isEqual(end, bookingEnd)))
    );
  });
};

// Generate date range array
export const getDateRange = (startDate: string, endDate: string): string[] => {
  const dates: string[] = [];
  const start = parseISO(startDate);
  const end = parseISO(endDate);

  let currentDate = start;
  while (isBefore(currentDate, end) || isEqual(currentDate, end)) {
    dates.push(format(currentDate, 'yyyy-MM-dd'));
    currentDate = new Date(currentDate.getTime() + 24 * 60 * 60 * 1000);
  }

  return dates;
};
