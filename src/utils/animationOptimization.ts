/**
 * Animation Optimization Utilities
 * Provides performance optimization and testing utilities for animations
 */

// Device capability detection
export const detectDeviceCapabilities = () => {
  if (typeof window === 'undefined')
    return { isLowEnd: false, hasSlowConnection: false };

  const isLowEnd = navigator.hardwareConcurrency <= 2;
  const hasSlowConnection =
    'connection' in navigator &&
    (navigator as any).connection?.effectiveType === 'slow-2g';

  return { isLowEnd, hasSlowConnection };
};

// Animation timing optimization
export const getOptimizedTiming = (
  baseDuration: number,
  shouldOptimize: boolean
) => {
  return shouldOptimize ? Math.min(baseDuration * 0.3, 0.2) : baseDuration;
};

// Stagger delay optimization
export const getOptimizedStagger = (
  baseStagger: number,
  shouldOptimize: boolean
) => {
  return shouldOptimize ? Math.min(baseStagger * 0.2, 0.02) : baseStagger;
};

// Animation easing optimization
export const getOptimizedEasing = (shouldOptimize: boolean) => {
  return shouldOptimize ? 'linear' : 'easeOut';
};

// Intersection Observer for lazy animation loading
export const createAnimationObserver = (
  callback: (entries: IntersectionObserverEntry[]) => void,
  options: IntersectionObserverInit = {}
) => {
  if (typeof window === 'undefined') return null;

  const defaultOptions: IntersectionObserverInit = {
    threshold: 0.1,
    rootMargin: '50px',
    ...options,
  };

  return new IntersectionObserver(callback, defaultOptions);
};

// Performance monitoring for animations
export class AnimationPerformanceMonitor {
  private frameCount = 0;
  private lastTime = 0;
  private fps = 0;
  private isMonitoring = false;

  start() {
    if (this.isMonitoring) return;
    this.isMonitoring = true;
    this.lastTime = performance.now();
    this.frameCount = 0;
    this.measureFPS();
  }

  stop() {
    this.isMonitoring = false;
  }

  getFPS() {
    return this.fps;
  }

  private measureFPS = () => {
    if (!this.isMonitoring) return;

    const currentTime = performance.now();
    this.frameCount++;

    if (currentTime - this.lastTime >= 1000) {
      this.fps = Math.round(
        (this.frameCount * 1000) / (currentTime - this.lastTime)
      );
      this.frameCount = 0;
      this.lastTime = currentTime;
    }

    requestAnimationFrame(this.measureFPS);
  };
}

// Animation testing utilities
export const testAnimationPerformance = async (
  animationFunction: () => void,
  duration: number = 2000
): Promise<{ averageFPS: number; minFPS: number; maxFPS: number }> => {
  const monitor = new AnimationPerformanceMonitor();
  const fpsReadings: number[] = [];

  monitor.start();
  animationFunction();

  return new Promise((resolve) => {
    const interval = setInterval(() => {
      const currentFPS = monitor.getFPS();
      if (currentFPS > 0) {
        fpsReadings.push(currentFPS);
      }
    }, 100);

    setTimeout(() => {
      clearInterval(interval);
      monitor.stop();

      if (fpsReadings.length === 0) {
        resolve({ averageFPS: 0, minFPS: 0, maxFPS: 0 });
        return;
      }

      const averageFPS =
        fpsReadings.reduce((a, b) => a + b, 0) / fpsReadings.length;
      const minFPS = Math.min(...fpsReadings);
      const maxFPS = Math.max(...fpsReadings);

      resolve({ averageFPS, minFPS, maxFPS });
    }, duration);
  });
};

// Responsive animation configuration
export const getResponsiveAnimationConfig = () => {
  if (typeof window === 'undefined') {
    return { isMobile: false, isTablet: false, isDesktop: true };
  }

  const width = window.innerWidth;
  const isMobile = width < 768;
  const isTablet = width >= 768 && width < 1024;
  const isDesktop = width >= 1024;

  return {
    isMobile,
    isTablet,
    isDesktop,
    // Adjust animation complexity based on screen size
    animationComplexity: isMobile ? 'simple' : isTablet ? 'medium' : 'full',
    // Reduce stagger delays on mobile
    staggerMultiplier: isMobile ? 0.5 : isTablet ? 0.75 : 1,
    // Reduce animation distances on mobile
    distanceMultiplier: isMobile ? 0.6 : isTablet ? 0.8 : 1,
  };
};

// Animation preloading utility
export const preloadAnimationAssets = () => {
  if (typeof document === 'undefined') return;

  // Preload critical CSS animations
  const style = document.createElement('style');
  style.textContent = `
    @keyframes optimizedFadeIn {
      from { opacity: 0; transform: translateY(10px); }
      to { opacity: 1; transform: translateY(0); }
    }
    @keyframes optimizedSlideIn {
      from { opacity: 0; transform: translateX(-10px); }
      to { opacity: 1; transform: translateX(0); }
    }
    @keyframes optimizedScaleIn {
      from { opacity: 0; transform: scale(0.95); }
      to { opacity: 1; transform: scale(1); }
    }
    .animation-optimized {
      will-change: transform, opacity;
    }
    .animation-optimized.complete {
      will-change: auto;
    }
  `;

  if (!document.head.querySelector('#animation-preload-styles')) {
    style.id = 'animation-preload-styles';
    document.head.appendChild(style);
  }
};

// Cleanup utility for animations
export const cleanupAnimationResources = () => {
  if (typeof document === 'undefined') return;

  // Remove will-change properties from completed animations
  const animatedElements = document.querySelectorAll('.animation-optimized');
  animatedElements.forEach((element) => {
    const htmlElement = element as HTMLElement;
    htmlElement.style.willChange = 'auto';
    htmlElement.classList.add('complete');
  });
};

// Cleanup utility specifically for Framer Motion animations
export const cleanupFramerMotionElement = (element: HTMLElement | null) => {
  if (!element) return;

  // Reset will-change property
  element.style.willChange = 'auto';

  // Ensure proper GPU acceleration without conflicts
  element.style.backfaceVisibility = 'hidden';
  element.style.transform = element.style.transform || 'translateZ(0)';
};

// Prevent transform conflicts between CSS and Framer Motion
export const preventTransformConflicts = (element: HTMLElement | null) => {
  if (!element) return;

  // Remove any CSS transform classes that might conflict
  element.classList.remove('hover:-translate-y-1');

  // Ensure only Framer Motion controls transforms
  const computedStyle = window.getComputedStyle(element);
  if (computedStyle.transform && computedStyle.transform !== 'none') {
    // If there's a CSS transform, let Framer Motion take over
    element.style.transform = 'translateZ(0)';
  }
};

// Animation timing validation
export const validateAnimationTiming = (
  duration: number,
  delay: number,
  stagger: number
): { isValid: boolean; warnings: string[] } => {
  const warnings: string[] = [];
  let isValid = true;

  if (duration > 1.5) {
    warnings.push('Animation duration exceeds 1.5s - may feel slow');
    isValid = false;
  }

  if (delay > 1) {
    warnings.push('Animation delay exceeds 1s - may feel unresponsive');
    isValid = false;
  }

  if (stagger > 0.2) {
    warnings.push('Stagger delay exceeds 0.2s - may feel sluggish');
    isValid = false;
  }

  if (duration < 0.1) {
    warnings.push('Animation duration below 0.1s - may be imperceptible');
  }

  return { isValid, warnings };
};

// Export performance monitor instance
export const globalAnimationMonitor = new AnimationPerformanceMonitor();
