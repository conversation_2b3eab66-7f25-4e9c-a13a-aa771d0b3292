import { useEffect, useState } from 'react';

/**
 * Hook to detect user's motion preferences
 */
export const useReducedMotion = () => {
  const [prefersReducedMotion, setPrefersReducedMotion] = useState(false);

  useEffect(() => {
    const mediaQuery = window.matchMedia('(prefers-reduced-motion: reduce)');
    setPrefersReducedMotion(mediaQuery.matches);

    const handleChange = (event: MediaQueryListEvent) => {
      setPrefersReducedMotion(event.matches);
    };

    mediaQuery.addEventListener('change', handleChange);
    return () => mediaQuery.removeEventListener('change', handleChange);
  }, []);

  return prefersReducedMotion;
};

/**
 * Hook to optimize animations based on device capabilities
 */
export const useAnimationOptimization = () => {
  const [shouldOptimize, setShouldOptimize] = useState(false);
  const prefersReducedMotion = useReducedMotion();

  useEffect(() => {
    // Check for low-end devices or reduced motion preference
    const isLowEndDevice = navigator.hardwareConcurrency <= 2;
    const hasSlowConnection =
      'connection' in navigator &&
      (navigator as any).connection?.effectiveType === 'slow-2g';

    setShouldOptimize(
      isLowEndDevice || hasSlowConnection || prefersReducedMotion
    );
  }, [prefersReducedMotion]);

  return {
    shouldOptimize,
    prefersReducedMotion,
    animationConfig: {
      duration: shouldOptimize ? 0.1 : 0.6,
      ease: shouldOptimize ? 'linear' : 'easeOut',
      stagger: shouldOptimize ? 0.01 : 0.1,
    },
  };
};

/**
 * Hook to manage will-change property for performance
 */
export const useWillChange = (properties: string[] = ['transform']) => {
  useEffect(() => {
    const elements = document.querySelectorAll('[data-will-change]');

    elements.forEach((element) => {
      const htmlElement = element as HTMLElement;
      htmlElement.style.willChange = properties.join(', ');
    });

    return () => {
      elements.forEach((element) => {
        const htmlElement = element as HTMLElement;
        htmlElement.style.willChange = 'auto';
      });
    };
  }, [properties]);
};

/**
 * Hook to detect if element is in viewport for performance optimization
 */
export const useIntersectionObserver = (
  callback: (isIntersecting: boolean) => void,
  options: IntersectionObserverInit = {}
) => {
  const [elementRef, setElementRef] = useState<HTMLElement | null>(null);

  useEffect(() => {
    if (!elementRef) return;

    const observer = new IntersectionObserver(
      ([entry]) => {
        callback(entry.isIntersecting);
      },
      {
        threshold: 0.1,
        rootMargin: '50px',
        ...options,
      }
    );

    observer.observe(elementRef);

    return () => {
      observer.disconnect();
    };
  }, [elementRef, callback, options]);

  return setElementRef;
};

/**
 * Hook to preload critical animations
 */
export const useAnimationPreload = () => {
  useEffect(() => {
    // Preload critical CSS animations
    const style = document.createElement('style');
    style.textContent = `
      @keyframes fadeIn {
        from { opacity: 0; }
        to { opacity: 1; }
      }
      @keyframes slideUp {
        from { transform: translateY(20px); opacity: 0; }
        to { transform: translateY(0); opacity: 1; }
      }
      @keyframes scaleIn {
        from { transform: scale(0.95); opacity: 0; }
        to { transform: scale(1); opacity: 1; }
      }
      @keyframes shimmer {
        0% { background-position: -200% 0; }
        100% { background-position: 200% 0; }
      }
    `;
    document.head.appendChild(style);

    return () => {
      document.head.removeChild(style);
    };
  }, []);
};

/**
 * Hook to manage scroll performance
 */
export const useScrollPerformance = () => {
  useEffect(() => {
    let ticking = false;

    const handleScroll = () => {
      if (!ticking) {
        requestAnimationFrame(() => {
          // Scroll-based animations will be handled here
          ticking = false;
        });
        ticking = true;
      }
    };

    window.addEventListener('scroll', handleScroll, { passive: true });
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);
};
