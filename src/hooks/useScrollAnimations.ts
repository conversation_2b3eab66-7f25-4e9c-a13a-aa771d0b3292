import { useScroll, useTransform } from 'motion/react';
import { RefObject, useEffect, useState } from 'react';

/**
 * Enhanced scroll progress hook with customizable range
 */
export const useScrollProgress = (
  target?: RefObject<HTMLElement>,
  offset: ['start end', 'end start'] | ['start start', 'end end'] = [
    'start end',
    'end start',
  ]
) => {
  const { scrollYProgress } = useScroll({
    target,
    offset,
  });

  return scrollYProgress;
};

/**
 * Enhanced parallax hook with stronger effect for more visual impact
 */
export const useParallaxImproved = (
  speed: number = 0.5,
  target?: RefObject<HTMLElement>
) => {
  const { scrollYProgress } = useScroll({
    target,
    offset: ['start end', 'end start'],
  });

  // Enhanced multiplier for more noticeable parallax effect
  const y = useTransform(scrollYProgress, [0, 1], [0, speed * 120]);
  const scale = useTransform(scrollYProgress, [0, 1], [1, 1.05]);

  return { y, scale };
};

/**
 * Card stacking effect hook for creating layered scroll animations
 */
export const useCardStack = (
  index: number,
  total: number,
  target?: RefObject<HTMLElement>
) => {
  const { scrollYProgress } = useScroll({
    target,
    offset: ['start end', 'end start'],
  });

  // Calculate stacking progress for this card
  const stackProgress = (index + 1) / total;
  const startProgress = Math.max(0, stackProgress - 0.3);
  const endProgress = Math.min(1, stackProgress + 0.1);

  // Transform values for stacking effect
  const y = useTransform(
    scrollYProgress,
    [startProgress, endProgress],
    [0, -100 * index]
  );

  const scale = useTransform(
    scrollYProgress,
    [startProgress, endProgress],
    [1, 0.95 - index * 0.05]
  );

  const opacity = useTransform(
    scrollYProgress,
    [startProgress, endProgress],
    [1, 0.8 - index * 0.1]
  );

  const zIndex = total - index;

  return {
    y,
    scale,
    opacity,
    zIndex,
    stackProgress: scrollYProgress,
  };
};

/**
 * Scroll reveal hook for triggering animations when elements enter viewport
 */
export const useScrollReveal = (
  target?: RefObject<HTMLElement>,
  threshold: number = 0.1
) => {
  const { scrollYProgress } = useScroll({
    target,
    offset: [`start ${1 - threshold}`, 'start 0.65'],
  });

  const opacity = useTransform(scrollYProgress, [0, 1], [0, 1]);
  const y = useTransform(scrollYProgress, [0, 1], [25, 0]);
  const scale = useTransform(scrollYProgress, [0, 1], [0.98, 1]);

  return {
    opacity,
    y,
    scale,
    progress: scrollYProgress,
  };
};

/**
 * Text reveal animation hook with staggered effect
 */
export const useTextReveal = (
  target?: RefObject<HTMLElement>,
  delay: number = 0
) => {
  const { scrollYProgress } = useScroll({
    target,
    offset: ['start 0.9', 'start 0.65'],
  });

  const adjustedProgress = useTransform(scrollYProgress, [delay, 1], [0, 1]);

  const opacity = useTransform(adjustedProgress, [0, 1], [0, 1]);
  const y = useTransform(adjustedProgress, [0, 1], [20, 0]);

  return {
    opacity,
    y,
    progress: adjustedProgress,
  };
};

/**
 * Smooth scroll-based transform hook for complex animations
 */
export const useScrollTransform = (
  inputRange: number[],
  outputRange: number[],
  target?: RefObject<HTMLElement>
) => {
  const { scrollYProgress } = useScroll({
    target,
    offset: ['start end', 'end start'],
  });

  return useTransform(scrollYProgress, inputRange, outputRange);
};

/**
 * Hook for creating scroll-based background overlay effects
 */
export const useScrollOverlay = (target?: RefObject<HTMLElement>) => {
  const { scrollYProgress } = useScroll({
    target,
    offset: ['start end', 'end start'],
  });

  const opacity = useTransform(
    scrollYProgress,
    [0, 0.5, 1],
    [0.75, 0.85, 0.95]
  );

  return opacity;
};

/**
 * Staggered reveal animation for horizontal card layouts
 */
export const useStaggeredReveal = (
  index: number,
  target?: RefObject<HTMLElement>
) => {
  const { scrollYProgress } = useScroll({
    target,
    offset: ['start end', 'start 0.65'],
  });

  // Calculate stagger delay based on index with corrected coordinate system
  const staggerDelay = index * 0.15; // 150ms delay between cards
  const startProgress = Math.max(0, staggerDelay);
  const endProgress = Math.min(1, startProgress + 0.4);

  const adjustedProgress = useTransform(
    scrollYProgress,
    [startProgress, endProgress],
    [0, 1]
  );

  const opacity = useTransform(adjustedProgress, [0, 1], [0, 1]);
  const y = useTransform(adjustedProgress, [0, 1], [30, 0]);
  const scale = useTransform(adjustedProgress, [0, 1], [0.96, 1]);
  const rotateX = useTransform(adjustedProgress, [0, 1], [8, 0]);

  return {
    opacity,
    y,
    scale,
    rotateX,
    progress: adjustedProgress,
  };
};

/**
 * Independent section scroll trigger to prevent timing conflicts
 */
export const useIndependentScrollTrigger = (
  target?: RefObject<HTMLElement>,
  triggerPoint: number = 0.85
) => {
  const { scrollYProgress } = useScroll({
    target,
    offset: [`start ${triggerPoint}`, 'start 0.65'],
  });

  const opacity = useTransform(scrollYProgress, [0, 1], [0, 1]);
  const y = useTransform(scrollYProgress, [0, 1], [20, 0]);
  const scale = useTransform(scrollYProgress, [0, 1], [0.98, 1]);

  return {
    opacity,
    y,
    scale,
    progress: scrollYProgress,
  };
};

/**
 * Enhanced hero parallax with stronger visual impact but controlled movement
 */
export const useHeroParallax = (
  speed: number = 0.8,
  target?: RefObject<HTMLElement>
) => {
  const { scrollYProgress } = useScroll({
    target,
    offset: ['start start', 'end start'],
  });

  // Controlled parallax effect that keeps background visible
  const y = useTransform(scrollYProgress, [0, 1], [0, speed * 150]);
  const scale = useTransform(scrollYProgress, [0, 1], [1, 1.05]);

  return { y, scale };
};

/**
 * Responsive parallax hook that preserves mobile positioning
 * Reduces parallax effect on mobile to prevent interference with object-position
 */
export const useResponsiveParallax = (
  speed: number = 0.5,
  target?: RefObject<HTMLElement>
) => {
  // Initialize with proper mobile detection
  const [isMobile, setIsMobile] = useState(() => {
    if (typeof window !== 'undefined') {
      return window.innerWidth <= 480;
    }
    return false;
  });

  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth <= 480);
    };

    // Initial check (in case window wasn't available during useState initialization)
    checkMobile();

    // Debounced resize listener for performance
    let timeoutId: NodeJS.Timeout;
    const debouncedCheck = () => {
      clearTimeout(timeoutId);
      timeoutId = setTimeout(checkMobile, 100);
    };

    window.addEventListener('resize', debouncedCheck);
    return () => {
      window.removeEventListener('resize', debouncedCheck);
      clearTimeout(timeoutId);
    };
  }, []);

  const { scrollYProgress } = useScroll({
    target,
    offset: ['start end', 'end start'],
  });

  // Mobile: minimal parallax to preserve object-position
  // Desktop: enhanced parallax effect for more visual impact
  const mobileSpeed = speed * 0.2; // Slightly more movement on mobile
  const desktopSpeed = speed;

  const y = useTransform(
    scrollYProgress,
    [0, 1],
    [0, (isMobile ? mobileSpeed : desktopSpeed) * 200] // Increased from 120 to 200
  );

  const scale = useTransform(
    scrollYProgress,
    [0, 1],
    [1, isMobile ? 1.02 : 1.12] // Increased scaling: mobile 1.02, desktop 1.12
  );

  return { y, scale, isMobile };
};
