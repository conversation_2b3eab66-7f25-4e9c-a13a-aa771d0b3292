import { useState, useCallback, useMemo, useEffect } from 'react';
import { useSearchParams } from 'next/navigation';
import { BookingFormData, Vehicle } from '@/types';
import { supabase } from '@/lib/supabase';

export type BookingStep = 'vehicle' | 'dates' | 'details' | 'confirmation';

export interface BookingFormState extends Partial<BookingFormData> {
  selectedVehicle?: Vehicle;
}

export const useBookingForm = () => {
  const searchParams = useSearchParams();
  const [currentStep, setCurrentStep] = useState<BookingStep>('vehicle');
  const [formData, setFormData] = useState<BookingFormState>({});
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isCompleted, setIsCompleted] = useState(false);
  const [isLoadingPreselection, setIsLoadingPreselection] = useState(false);

  const steps = useMemo(
    () => [
      {
        key: 'vehicle' as BookingStep,
        title: 'Escol<PERSON>',
        description: '<PERSON>lecione o veículo vintage perfeito',
      },
      {
        key: 'dates' as BookingStep,
        title: 'Selecionar Datas',
        description: 'Escolha as datas da sua reserva',
      },
      {
        key: 'details' as BookingStep,
        title: 'Dados Pessoais',
        description: 'Preencha os seus dados de contacto',
      },
      {
        key: 'confirmation' as BookingStep,
        title: 'Confirmação',
        description: 'Revise e confirme a sua reserva',
      },
    ],
    []
  );

  const currentStepIndex = steps.findIndex((step) => step.key === currentStep);
  const isFirstStep = currentStepIndex === 0;
  const isLastStep = currentStepIndex === steps.length - 1;

  // Handle vehicle pre-selection from URL parameters
  useEffect(() => {
    const vehicleId = searchParams.get('vehicle');
    if (vehicleId && !formData.vehicle_id && supabase) {
      setIsLoadingPreselection(true);

      const fetchPreselectedVehicle = async () => {
        try {
          const { data: vehicle, error } = await supabase
            .from('vehicles')
            .select('*')
            .eq('id', vehicleId)
            .single();

          if (error) {
            console.error('Error fetching preselected vehicle:', error);
            return;
          }

          if (vehicle) {
            // Pre-populate form with selected vehicle
            setFormData({
              vehicle_id: vehicle.id,
              selectedVehicle: vehicle,
            });

            // Advance to dates step since vehicle is already selected
            setCurrentStep('dates');
          }
        } catch (err) {
          console.error('Error in vehicle preselection:', err);
        } finally {
          setIsLoadingPreselection(false);
        }
      };

      fetchPreselectedVehicle();
    }
  }, [searchParams, formData.vehicle_id]);

  const updateFormData = useCallback((data: Partial<BookingFormState>) => {
    setFormData((prev) => ({ ...prev, ...data }));
  }, []);

  const goToNextStep = useCallback(() => {
    if (!isLastStep) {
      const nextStep = steps[currentStepIndex + 1];
      setCurrentStep(nextStep.key);
    }
  }, [currentStepIndex, isLastStep, steps]);

  const goToPreviousStep = useCallback(() => {
    if (!isFirstStep) {
      const previousStep = steps[currentStepIndex - 1];
      setCurrentStep(previousStep.key);
    }
  }, [currentStepIndex, isFirstStep, steps]);

  const canProceedToNext = useCallback((): boolean => {
    switch (currentStep) {
      case 'vehicle':
        return !!formData.vehicle_id && !!formData.selectedVehicle;
      case 'dates':
        return !!formData.start_date && !!formData.end_date;
      case 'details':
        return !!(formData.customer_name && formData.customer_email);
      case 'confirmation':
        return true;
      default:
        return false;
    }
  }, [currentStep, formData]);

  const handleStepComplete = useCallback(
    (stepData: Partial<BookingFormState>) => {
      updateFormData(stepData);
      if (currentStep !== 'confirmation') {
        goToNextStep();
      }
    },
    [currentStep, updateFormData, goToNextStep]
  );

  const resetForm = useCallback(() => {
    setIsCompleted(false);
    setCurrentStep('vehicle');
    setFormData({});
    setIsSubmitting(false);
  }, []);

  return {
    // State
    currentStep,
    formData,
    isSubmitting,
    isCompleted,
    isLoadingPreselection,
    steps,
    currentStepIndex,
    isFirstStep,
    isLastStep,

    // Actions
    setCurrentStep,
    updateFormData,
    goToNextStep,
    goToPreviousStep,
    canProceedToNext,
    handleStepComplete,
    setIsSubmitting,
    setIsCompleted,
    resetForm,
  };
};
