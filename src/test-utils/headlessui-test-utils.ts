/**
 * Headless UI Testing Utilities
 *
 * This module provides utilities for testing components that use Headless UI
 * components like Dialog, Transition, Menu, etc. These components often trigger
 * internal state updates that need to be properly wrapped in act() calls.
 */

import { render, act, waitFor, RenderResult } from '@testing-library/react';
import { ReactElement } from 'react';

/**
 * Renders a component that uses Headless UI Transition components
 * and properly handles the internal state updates with act()
 *
 * @param ui - The React element to render
 * @param options - Additional render options
 * @returns Promise<RenderResult> - The render result after state updates complete
 */
export const renderWithTransition = async (
  ui: ReactElement,
  options?: Parameters<typeof render>[1]
): Promise<RenderResult> => {
  let result: RenderResult;

  // Wrap render in act() to handle Headless UI internal state updates
  await act(async () => {
    result = render(ui, options);
  });

  // Wait for any transition state updates to complete
  await waitFor(() => {
    // This ensures all async state updates have settled
    expect(result.container).toBeInTheDocument();
  });

  return result!;
};

/**
 * Renders a Headless UI Dialog/Modal component with proper state handling
 *
 * @param ui - The React element containing the Dialog component
 * @param options - Additional render options
 * @returns Promise<RenderResult> - The render result after modal state settles
 */
export const renderModal = async (
  ui: ReactElement,
  options?: Parameters<typeof render>[1]
): Promise<RenderResult> => {
  return renderWithTransition(ui, options);
};

/**
 * Performs an action (like clicking) on a Headless UI component
 * and waits for any resulting state updates
 *
 * @param action - The action to perform (e.g., () => fireEvent.click(button))
 */
export const actWithTransition = async (action: () => void): Promise<void> => {
  await act(async () => {
    action();
  });

  // Allow time for any transition state updates
  await waitFor(() => {
    // Small delay to ensure all state updates have processed
    return new Promise((resolve) => setTimeout(resolve, 0));
  });
};

/**
 * Waits for a Headless UI component to complete its transition
 * This is useful when you need to wait for enter/exit animations to finish
 *
 * @param timeout - Maximum time to wait in milliseconds (default: 1000)
 */
export const waitForTransition = async (
  timeout: number = 1000
): Promise<void> => {
  await waitFor(
    () => {
      // Wait for any pending state updates to complete
      return Promise.resolve();
    },
    { timeout }
  );
};

/**
 * Custom matcher to check if a Headless UI Dialog is properly rendered
 * This helps verify that the modal is in the correct state after transitions
 */
export const expectModalToBeVisible = async (
  getByTestId: (id: string) => HTMLElement,
  testId: string = 'modal'
): Promise<void> => {
  await waitFor(() => {
    const modal = getByTestId(testId);
    expect(modal).toBeInTheDocument();
    expect(modal).toBeVisible();
  });
};

/**
 * Custom matcher to check if a Headless UI Dialog is properly hidden
 */
export const expectModalToBeHidden = async (
  queryByTestId: (id: string) => HTMLElement | null,
  testId: string = 'modal'
): Promise<void> => {
  await waitFor(() => {
    const modal = queryByTestId(testId);
    expect(modal).not.toBeInTheDocument();
  });
};

/**
 * Test setup utility for components using Headless UI
 * This can be used in beforeEach blocks to ensure proper test isolation
 */
export const setupHeadlessUITest = (): void => {
  // Clear any pending timers that might affect transitions
  jest.clearAllTimers();

  // Reset any mocked functions
  jest.clearAllMocks();
};

/**
 * Test cleanup utility for components using Headless UI
 * This can be used in afterEach blocks to clean up after tests
 */
export const cleanupHeadlessUITest = (): void => {
  // Run any pending timers to completion
  jest.runOnlyPendingTimers();

  // Clear all timers
  jest.clearAllTimers();
};
