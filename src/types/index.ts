export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: Json | undefined }
  | Json[];

export type Database = {
  // Allows to automatically instanciate createClient with right options
  // instead of createClient<Database, { PostgrestVersion: 'XX' }>(URL, KEY)
  __InternalSupabase: {
    PostgrestVersion: '12.2.3 (519615d)';
  };
  public: {
    Tables: {
      bookings: {
        Row: {
          created_at: string | null;
          customer_email: string;
          customer_name: string;
          customer_phone: string | null;
          end_date: string;
          id: string;
          notes: string | null;
          start_date: string;
          status: string;
          total_price: number | null;
          updated_at: string | null;
          vehicle_id: string | null;
        };
        Insert: {
          created_at?: string | null;
          customer_email: string;
          customer_name: string;
          customer_phone?: string | null;
          end_date: string;
          id?: string;
          notes?: string | null;
          start_date: string;
          status?: string;
          total_price?: number | null;
          updated_at?: string | null;
          vehicle_id?: string | null;
        };
        Update: {
          created_at?: string | null;
          customer_email?: string;
          customer_name?: string;
          customer_phone?: string | null;
          end_date?: string;
          id?: string;
          notes?: string | null;
          start_date?: string;
          status?: string;
          total_price?: number | null;
          updated_at?: string | null;
          vehicle_id?: string | null;
        };
        Relationships: [
          {
            foreignKeyName: 'bookings_vehicle_id_fkey';
            columns: ['vehicle_id'];
            isOneToOne: false;
            referencedRelation: 'vehicles';
            referencedColumns: ['id'];
          },
        ];
      };
      contact_inquiries: {
        Row: {
          company: string | null;
          created_at: string | null;
          email: string;
          id: string;
          message: string;
          name: string;
          phone: string | null;
          status: string;
          subject: string | null;
          updated_at: string | null;
        };
        Insert: {
          company?: string | null;
          created_at?: string | null;
          email: string;
          id?: string;
          message: string;
          name: string;
          phone?: string | null;
          status?: string;
          subject?: string | null;
          updated_at?: string | null;
        };
        Update: {
          company?: string | null;
          created_at?: string | null;
          email?: string;
          id?: string;
          message?: string;
          name?: string;
          phone?: string | null;
          status?: string;
          subject?: string | null;
          updated_at?: string | null;
        };
        Relationships: [];
      };
      email_templates: {
        Row: {
          body: string;
          created_at: string | null;
          id: string;
          is_active: boolean | null;
          name: string;
          subject: string;
          template_type: string;
          updated_at: string | null;
        };
        Insert: {
          body: string;
          created_at?: string | null;
          id?: string;
          is_active?: boolean | null;
          name: string;
          subject: string;
          template_type: string;
          updated_at?: string | null;
        };
        Update: {
          body?: string;
          created_at?: string | null;
          id?: string;
          is_active?: boolean | null;
          name?: string;
          subject?: string;
          template_type?: string;
          updated_at?: string | null;
        };
        Relationships: [];
      };
      vehicles: {
        Row: {
          availability: Json | null;
          created_at: string | null;
          description: string | null;
          id: string;
          name: string;
          photo_url: string | null;
          price: number | null;
          updated_at: string | null;
          year: number;
        };
        Insert: {
          availability?: Json | null;
          created_at?: string | null;
          description?: string | null;
          id?: string;
          name: string;
          photo_url?: string | null;
          price?: number | null;
          updated_at?: string | null;
          year: number;
        };
        Update: {
          availability?: Json | null;
          created_at?: string | null;
          description?: string | null;
          id?: string;
          name?: string;
          photo_url?: string | null;
          price?: number | null;
          updated_at?: string | null;
          year?: number;
        };
        Relationships: [];
      };
    };
    Views: {
      [_ in never]: never;
    };
    Functions: {
      is_admin: {
        Args: Record<PropertyKey, never>;
        Returns: boolean;
      };
    };
    Enums: {
      [_ in never]: never;
    };
    CompositeTypes: {
      [_ in never]: never;
    };
  };
};

// Convenience types for easier usage
export type Vehicle = Database['public']['Tables']['vehicles']['Row'];
export type VehicleInsert = Database['public']['Tables']['vehicles']['Insert'];
export type VehicleUpdate = Database['public']['Tables']['vehicles']['Update'];

export type Booking = Database['public']['Tables']['bookings']['Row'];
export type BookingInsert = Database['public']['Tables']['bookings']['Insert'];
export type BookingUpdate = Database['public']['Tables']['bookings']['Update'];

export type ContactInquiry =
  Database['public']['Tables']['contact_inquiries']['Row'];
export type ContactInquiryInsert =
  Database['public']['Tables']['contact_inquiries']['Insert'];
export type ContactInquiryUpdate =
  Database['public']['Tables']['contact_inquiries']['Update'];

export type EmailTemplate =
  Database['public']['Tables']['email_templates']['Row'];
export type EmailTemplateInsert =
  Database['public']['Tables']['email_templates']['Insert'];
export type EmailTemplateUpdate =
  Database['public']['Tables']['email_templates']['Update'];

// Status enums for better type safety
export type BookingStatus =
  | 'pendente'
  | 'confirmado'
  | 'cancelado'
  | 'completo';
export type InquiryStatus = 'new' | 'in_progress' | 'resolved' | 'closed';
export type EmailTemplateType =
  | 'booking_confirmation'
  | 'booking_cancellation'
  | 'contact_response'
  | 'admin_notification';

// Form interfaces for frontend usage
export interface BookingFormData {
  vehicle_id: string;
  customer_name: string;
  customer_email: string;
  customer_phone?: string;
  start_date: string;
  end_date: string;
  notes?: string;
}

export interface ContactFormData {
  name: string;
  email: string;
  phone?: string;
  company?: string;
  subject?: string;
  message: string;
}

// API response interfaces
export interface ApiResponse<T> {
  data?: T;
  error?: string;
  success: boolean;
}
