import { Metadata } from 'next';
import { Suspense } from 'react';
import BookingForm from '@/components/forms/BookingForm';
import { ScrollReveal, FadeIn } from '@/components/animations';

export const metadata: Metadata = {
  title: 'Reservas - Vintage Marketing Portugal',
  description:
    'Reserve o seu veículo vintage para eventos de marketing. Processo simples e rápido.',
  keywords: 'reserva, aluguer veículos vintage, eventos marketing, Portugal',
};

export default function ReservasPage() {
  return (
    <div className="min-h-screen bg-background-cream">
      {/* Hero Section */}
      <section className="section-light">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8 max-w-4xl text-center">
          <FadeIn className="font-heading text-hero font-hero text-primary-black tracking-hero leading-hero mb-6">
            Reserve o Seu Veículo Vintage
          </FadeIn>
          <FadeIn
            delay={0.2}
            className="text-lg text-primary-mediumGray max-w-2xl mx-auto leading-relaxed"
          >
            Preencha o formulário abaixo para reservar o veículo perfeito para o
            seu evento de marketing. Entraremos em contacto consigo para
            confirmar todos os detalhes.
          </FadeIn>
        </div>
      </section>

      {/* Booking Form Section */}
      <section className="section-light">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8 max-w-4xl">
          <FadeIn delay={0.4}>
            <Suspense
              fallback={
                <div className="bg-white rounded-xl shadow-lg p-8 text-center">
                  <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-black mx-auto mb-4"></div>
                  <h2 className="font-heading text-xl font-bold text-primary-black mb-2">
                    A carregar formulário...
                  </h2>
                  <p className="text-primary-mediumGray">
                    Estamos a preparar o formulário de reserva.
                  </p>
                </div>
              }
            >
              <BookingForm />
            </Suspense>
          </FadeIn>
        </div>
      </section>
    </div>
  );
}
