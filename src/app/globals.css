@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  html {
    font-family: Inter, system-ui, sans-serif;
    background-color: #f5f1e8;
  }

  body {
    color: #666666;
    line-height: 1.6;
  }

  h1,
  h2,
  h3,
  h4,
  h5,
  h6 {
    font-family: Georgia, serif;
    color: #000000;
  }
}

@layer components {
  /* Design System Button Styles */
  .btn-primary {
    @apply bg-primary-black hover:bg-primary-darkGray text-primary-white border-none rounded-button px-6 py-3 text-nav font-semibold transition-all duration-300 hover:-translate-y-px;
  }

  .btn-secondary {
    @apply bg-transparent text-primary-black border-2 border-primary-black rounded-button px-6 py-3 text-nav font-semibold transition-all duration-300 hover:bg-primary-black hover:text-primary-white;
    align-content: center;
  }

  .btn-cta {
    @apply bg-primary-black text-primary-white rounded-cta px-7 py-4 text-base font-semibold inline-flex items-center gap-2 transition-all duration-300 hover:bg-primary-darkGray hover:translate-x-1;
  }

  /* Design System Card Styles */
  .card {
    @apply bg-background-cardBackground rounded-card shadow-card overflow-hidden transition-all duration-300 hover:shadow-card-hover hover:-translate-y-1;
  }

  /* Card styles for Framer Motion animated components (no CSS transforms) */
  .card-animated {
    @apply bg-background-cardBackground rounded-card shadow-card overflow-hidden transition-shadow duration-300 hover:shadow-card-hover;
  }

  /* Special card style for carousel stacking - prevents clipping */
  .card-carousel {
    @apply bg-background-cardBackground rounded-card shadow-card overflow-visible transition-shadow duration-300 hover:shadow-card-hover;
  }

  /* Carousel specific styles for optimal performance */
  .carousel-container {
    -webkit-overflow-scrolling: touch;
    scroll-behavior: smooth;
    overscroll-behavior-x: contain;
  }

  .carousel-item {
    scroll-snap-align: start;
    scroll-snap-stop: always;
  }

  /* Design System Navigation */
  .nav-blur {
    @apply bg-background-cream/95 backdrop-blur-nav border-b border-primary-black/10;
  }

  /* Design System Typography */
  .text-hero {
    @apply text-4xl font-hero text-primary-black tracking-hero leading-hero;
  }

  .text-section {
    @apply text-3xl font-section text-primary-black tracking-section;
  }

  .text-card {
    @apply text-xl font-card text-primary-black;
  }

  .text-body {
    @apply text-base font-normal text-primary-mediumGray leading-body;
  }

  .text-body-secondary {
    @apply text-sm font-normal text-primary-lightGray;
  }

  /* Legacy button styles for backward compatibility */
  .btn-primary-legacy {
    @apply bg-vintage-gold hover:bg-vintage-darkBrown text-white font-semibold py-2 px-4 rounded transition-colors duration-200;
  }

  .btn-secondary-legacy {
    @apply bg-vintage-brown hover:bg-vintage-darkBrown text-white font-semibold py-2 px-4 rounded transition-colors duration-200;
  }

  /* Utility classes */
  .line-clamp-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }

  .line-clamp-3 {
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }

  /* Section backgrounds */
  .section-light {
    @apply bg-background-cream py-2 md:py-16;
  }

  .section-alternate {
    @apply bg-[#F0EBE3] py-16;
  }

  .section-dark {
    @apply bg-background-darkSection text-primary-white py-16 px-8;
  }

  /* Hero Section Background Image Optimization with Parallax */
  .hero-bg-image {
    @apply object-cover;
    opacity: 0.4;
    object-position: center center;
    will-change: transform;
    backface-visibility: hidden;
    transform: translateZ(0);
  }

  /* Mobile-specific image positioning (small screens) */
  @media (max-width: 480px) {
    .hero-bg-image {
      opacity: 0.7;
      object-position: 5% center !important;
      object-fit: cover;
    }
  }

  /* Tablet-specific image positioning */
  @media (min-width: 481px) and (max-width: 768px) {
    .hero-bg-image {
      opacity: 0.4;
      object-position: 60% center;
    }
  }

  /* Desktop-specific image positioning */
  @media (min-width: 1024px) {
    .hero-bg-image {
      opacity: 0.4;
      object-position: center 35%;
    }
  }

  /* Large desktop optimization */
  @media (min-width: 1440px) {
    .hero-bg-image {
      opacity: 0.4;
      object-position: center 30%;
    }
  }

  /* Parallax performance optimizations */
  .parallax-container {
    transform: translateZ(0);
    backface-visibility: hidden;
    perspective: 1000px;
    overflow: hidden;
  }
}

@layer utilities {
  /* Performance-optimized animation utilities */
  .animate-fade-in {
    animation: fadeIn 0.6s ease-out forwards;
    will-change: opacity;
  }

  .animate-slide-up {
    animation: slideUp 0.6s ease-out forwards;
    will-change: transform;
  }

  .animate-scale-in {
    animation: scaleIn 0.4s ease-out forwards;
    will-change: transform;
  }

  /* GPU-accelerated transforms */
  .gpu-accelerated {
    transform: translateZ(0);
    backface-visibility: hidden;
    perspective: 1000px;
  }

  /* Smooth scroll animations */
  .smooth-scroll {
    scroll-behavior: smooth;
  }

  /* Hover effects with performance optimization */
  .hover-lift {
    transition:
      transform 0.3s ease,
      box-shadow 0.3s ease;
    will-change: transform, box-shadow;
  }

  .hover-lift:hover {
    transform: translateY(-4px) translateZ(0);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
  }

  /* Card stacking effect utilities */
  .card-stack {
    position: sticky;
    top: 5rem;
    will-change: transform, opacity;
  }

  /* Staggered card animations */
  .staggered-card {
    will-change: transform, opacity;
    transform-style: preserve-3d;
    backface-visibility: hidden;
  }

  /* Enhanced hero parallax */
  .hero-parallax {
    will-change: transform;
    transform: translateZ(0);
    backface-visibility: hidden;
  }

  /* Hero background image optimization */
  .hero-bg-image {
    will-change: transform;
    transform: translateZ(0);
    backface-visibility: hidden;
    object-fit: cover;
    object-position: center;
  }

  /* Independent section animations */
  .independent-section {
    will-change: transform, opacity;
    transform: translateZ(0);
  }

  /* Parallax optimization */
  .parallax-element {
    will-change: transform;
    transform: translateZ(0);
  }

  /* Focus states */
  .focus-ring {
    @apply focus:outline-none focus:ring-2 focus:ring-primary-black focus:ring-offset-2 focus:ring-offset-background-cream;
  }

  /* Loading states */
  .loading-shimmer {
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200% 100%;
    animation: shimmer 1.5s infinite;
    will-change: background-position;
  }

  /* Reduced motion preferences */
  @media (prefers-reduced-motion: reduce) {
    .animate-fade-in,
    .animate-slide-up,
    .animate-scale-in,
    .hover-lift,
    .loading-shimmer {
      animation: none !important;
      transition: none !important;
    }

    .parallax-element {
      transform: none !important;
    }

    * {
      animation-duration: 0.01ms !important;
      animation-iteration-count: 1 !important;
      transition-duration: 0.01ms !important;
    }
  }
}
