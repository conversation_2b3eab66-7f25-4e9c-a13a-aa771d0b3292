'use client';

import { useState, useEffect } from 'react';
import { Vehicle } from '@/types';
import { supabase } from '@/lib/supabase';
import VehicleCard from '@/components/VehicleCard';
import VehicleCarousel from '@/components/VehicleCarousel';
import VehicleModal from '@/components/VehicleModal';
import {
  FadeIn,
  StaggerContainer,
  ScrollReveal,
  TextReveal,
  AnimatedButton,
  LoadingAnimation,
} from '@/components/animations';

export default function FleetPage() {
  const [vehicles, setVehicles] = useState<Vehicle[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [selectedVehicle, setSelectedVehicle] = useState<Vehicle | null>(null);
  const [isModalOpen, setIsModalOpen] = useState(false);

  useEffect(() => {
    fetchVehicles();
  }, []);

  const fetchVehicles = async () => {
    try {
      setLoading(true);

      if (!supabase) {
        // If Supabase is not configured, show connection error
        setError(
          'Sem ligação à base de dados. Configure as variáveis de ambiente do Supabase.'
        );
        setLoading(false);
        return;
      }

      const { data, error } = await supabase
        .from('vehicles')
        .select('*')
        .order('created_at', { ascending: true });

      if (error) {
        throw error;
      }

      setVehicles(data || []);
    } catch (err) {
      console.error('Error fetching vehicles:', err);
      setError('Erro ao carregar veículos. Tente novamente.');
    } finally {
      setLoading(false);
    }
  };

  const handleVehicleClick = (vehicle: Vehicle) => {
    setSelectedVehicle(vehicle);
    setIsModalOpen(true);
  };

  const closeModal = () => {
    setIsModalOpen(false);
    setSelectedVehicle(null);
  };

  if (loading) {
    return <LoadingAnimation isLoading={loading} />;
  }

  if (error) {
    return (
      <div className="min-h-screen bg-background-cream">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8 py-16">
          <div className="text-center">
            <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded-card mb-4 max-w-md mx-auto">
              {error}
            </div>
            <button onClick={fetchVehicles} className="btn-primary">
              Tentar Novamente
            </button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-background-cream">
      {/* Hero Section */}
      <section className="section-alternate">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <FadeIn className="font-heading text-hero font-hero text-primary-black tracking-hero leading-hero mb-6">
            A Nossa Frota Vintage
          </FadeIn>
          <FadeIn
            delay={0.2}
            className="text-body text-primary-mediumGray leading-body max-w-3xl mx-auto text-xl"
          >
            Descubra a nossa coleção cuidadosamente selecionada de veículos
            vintage, cada um com a sua própria história e charme único,
            perfeitos para o seu evento especial.
          </FadeIn>
        </div>
      </section>

      {/* Vehicles Grid */}
      <section className="section-light">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          {vehicles.length === 0 ? (
            <FadeIn>
              <div className="text-center py-16">
                <div className="text-primary-mediumGray mb-4">
                  <svg
                    className="w-16 h-16 mx-auto mb-4"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-4m-5 0H9m0 0H5m0 0h2M7 7h10M7 11h10M7 15h10"
                    />
                  </svg>
                </div>
                <h3 className="font-heading text-section font-section text-primary-black mb-4">
                  Nenhum Veículo Disponível
                </h3>
                <p className="text-body text-primary-mediumGray">
                  Estamos a preparar a nossa frota. Volte em breve para ver os
                  nossos veículos vintage.
                </p>
              </div>
            </FadeIn>
          ) : (
            <>
              {/* Mobile Carousel */}
              <div className="block md:hidden">
                <VehicleCarousel
                  vehicles={vehicles}
                  onVehicleClick={handleVehicleClick}
                  className="mb-12"
                />
              </div>

              {/* Desktop Grid */}
              <StaggerContainer className="hidden md:grid grid-cols-1 lg:grid-cols-2 gap-12 max-w-6xl mx-auto justify-items-center">
                {vehicles.map((vehicle) => (
                  <div
                    key={vehicle.id}
                    onClick={() => handleVehicleClick(vehicle)}
                    className="cursor-pointer"
                  >
                    <VehicleCard vehicle={vehicle} />
                  </div>
                ))}
              </StaggerContainer>
            </>
          )}
        </div>
      </section>

      {/* How It Works Section */}
      <section className="section-alternate">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="max-w-4xl mx-auto">
            <ScrollReveal>
              <div className="text-center mb-12">
                <h2 className="font-heading text-section font-section text-primary-black mb-6">
                  Como Funciona
                </h2>
                <p className="text-body text-primary-mediumGray leading-body max-w-2xl mx-auto">
                  Os nossos veículos vintage são disponibilizados com serviço completo de motorista profissional para garantir a segurança e preservação destes tesouros históricos.
                </p>
              </div>
            </ScrollReveal>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mb-12">
              <ScrollReveal delay={0.1}>
                <div className="text-center">
                  <div className="w-16 h-16 bg-accent-vintage text-white rounded-full flex items-center justify-center mx-auto mb-4">
                    <svg className="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                    </svg>
                  </div>
                  <h3 className="font-heading text-card font-card text-primary-black mb-3">
                    Motorista Profissional
                  </h3>
                  <p className="text-body text-primary-mediumGray">
                    Todos os veículos incluem motorista qualificado e experiente em veículos vintage.
                  </p>
                </div>
              </ScrollReveal>

              <ScrollReveal delay={0.2}>
                <div className="text-center">
                  <div className="w-16 h-16 bg-accent-vintage text-white rounded-full flex items-center justify-center mx-auto mb-4">
                    <svg className="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 9a2 2 0 012-2h.93a2 2 0 001.664-.89l.812-1.22A2 2 0 0110.07 4h3.86a2 2 0 011.664.89l.812 1.22A2 2 0 0018.07 7H19a2 2 0 012 2v9a2 2 0 01-2 2H5a2 2 0 01-2-2V9z" />
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 13a3 3 0 11-6 0 3 3 0 016 0z" />
                    </svg>
                  </div>
                  <h3 className="font-heading text-card font-card text-primary-black mb-3">
                    Sessões Fotográficas
                  </h3>
                  <p className="text-body text-primary-mediumGray">
                    Perfeitos para campanhas publicitárias, eventos corporativos e produções criativas.
                  </p>
                </div>
              </ScrollReveal>

              <ScrollReveal delay={0.3}>
                <div className="text-center">
                  <div className="w-16 h-16 bg-accent-vintage text-white rounded-full flex items-center justify-center mx-auto mb-4">
                    <svg className="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
                    </svg>
                  </div>
                  <h3 className="font-heading text-card font-card text-primary-black mb-3">
                    Segurança Garantida
                  </h3>
                  <p className="text-body text-primary-mediumGray">
                    Preservação e segurança dos veículos históricos com acompanhamento especializado.
                  </p>
                </div>
              </ScrollReveal>
            </div>

            <ScrollReveal delay={0.4}>
              <div className="bg-background-beige border border-primary-lightGray/20 rounded-lg p-6 text-center">
                <h3 className="font-heading text-card font-card text-primary-black mb-3">
                  Política de Utilização
                </h3>
                <p className="text-body text-primary-mediumGray mb-4">
                  <strong className="text-primary-black">Importante:</strong> Os nossos veículos vintage são disponibilizados exclusivamente para exposição e sessões fotográficas, sempre acompanhados por motorista profissional. Não é permitida a condução independente por questões de segurança e preservação histórica.
                </p>
                <div className="flex items-center justify-center">
                  <div className="bg-accent-vintage text-white px-4 py-2 rounded-full text-sm font-semibold">
                    Motorista Sempre Incluído
                  </div>
                </div>
              </div>
            </ScrollReveal>
          </div>
        </div>
      </section>

      {/* Call to Action */}
      <section className="section-light">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <TextReveal className="font-heading text-section font-section text-primary-black mb-6">
            Pronto para Reservar?
          </TextReveal>
          <TextReveal
            delay={0.2}
            className="text-body text-primary-mediumGray leading-body mb-8 max-w-2xl mx-auto"
          >
            Escolha o veículo perfeito para o seu evento e faça a sua reserva
            hoje mesmo. A nossa equipa está pronta para ajudar a tornar o seu
            evento inesquecível.
          </TextReveal>
          <ScrollReveal delay={0.4}>
            <AnimatedButton
              href="/reservas"
              variant="cta"
              className="inline-flex items-center justify-center"
              enableGlow={true}
              glowColor="rgba(0, 0, 0, 0.2)"
            >
              Fazer Reserva Agora
              <svg
                className="w-4 h-4 ml-2"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M9 5l7 7-7 7"
                />
              </svg>
            </AnimatedButton>
          </ScrollReveal>
        </div>
      </section>

      {/* Vehicle Detail Modal */}
      <VehicleModal
        vehicle={selectedVehicle}
        isOpen={isModalOpen}
        onClose={closeModal}
      />
    </div>
  );
}
