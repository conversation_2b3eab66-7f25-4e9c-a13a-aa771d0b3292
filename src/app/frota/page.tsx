'use client';

import { useState, useEffect } from 'react';
import { Vehicle } from '@/types';
import { supabase } from '@/lib/supabase';
import VehicleCard from '@/components/VehicleCard';
import VehicleCarousel from '@/components/VehicleCarousel';
import VehicleModal from '@/components/VehicleModal';
import {
  FadeIn,
  StaggerContainer,
  ScrollReveal,
  TextReveal,
  AnimatedButton,
  GlowEffect,
  LoadingAnimation,
} from '@/components/animations';

export default function FleetPage() {
  const [vehicles, setVehicles] = useState<Vehicle[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [selectedVehicle, setSelectedVehicle] = useState<Vehicle | null>(null);
  const [isModalOpen, setIsModalOpen] = useState(false);

  useEffect(() => {
    fetchVehicles();
  }, []);

  const fetchVehicles = async () => {
    try {
      setLoading(true);

      if (!supabase) {
        // If Supabase is not configured, show connection error
        setError(
          'Sem ligação à base de dados. Configure as variáveis de ambiente do Supabase.'
        );
        setLoading(false);
        return;
      }

      const { data, error } = await supabase
        .from('vehicles')
        .select('*')
        .order('created_at', { ascending: true });

      if (error) {
        throw error;
      }

      setVehicles(data || []);
    } catch (err) {
      console.error('Error fetching vehicles:', err);
      setError('Erro ao carregar veículos. Tente novamente.');
    } finally {
      setLoading(false);
    }
  };

  const handleVehicleClick = (vehicle: Vehicle) => {
    setSelectedVehicle(vehicle);
    setIsModalOpen(true);
  };

  const closeModal = () => {
    setIsModalOpen(false);
    setSelectedVehicle(null);
  };

  if (loading) {
    return <LoadingAnimation isLoading={loading} />;
  }

  if (error) {
    return (
      <div className="min-h-screen bg-background-cream">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8 py-16">
          <div className="text-center">
            <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded-card mb-4 max-w-md mx-auto">
              {error}
            </div>
            <button onClick={fetchVehicles} className="btn-primary">
              Tentar Novamente
            </button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-background-cream">
      {/* Hero Section */}
      <section className="section-alternate">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <FadeIn className="font-heading text-hero font-hero text-primary-black tracking-hero leading-hero mb-6">
            A Nossa Frota Vintage
          </FadeIn>
          <FadeIn
            delay={0.2}
            className="text-body text-primary-mediumGray leading-body max-w-3xl mx-auto text-xl"
          >
            Descubra a nossa coleção cuidadosamente selecionada de veículos
            vintage, cada um com a sua própria história e charme único,
            perfeitos para o seu evento especial.
          </FadeIn>
        </div>
      </section>

      {/* Vehicles Grid */}
      <section className="section-light">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          {vehicles.length === 0 ? (
            <FadeIn>
              <div className="text-center py-16">
                <div className="text-primary-mediumGray mb-4">
                  <svg
                    className="w-16 h-16 mx-auto mb-4"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-4m-5 0H9m0 0H5m0 0h2M7 7h10M7 11h10M7 15h10"
                    />
                  </svg>
                </div>
                <h3 className="font-heading text-section font-section text-primary-black mb-4">
                  Nenhum Veículo Disponível
                </h3>
                <p className="text-body text-primary-mediumGray">
                  Estamos a preparar a nossa frota. Volte em breve para ver os
                  nossos veículos vintage.
                </p>
              </div>
            </FadeIn>
          ) : (
            <>
              {/* Mobile Carousel */}
              <div className="block md:hidden">
                <VehicleCarousel
                  vehicles={vehicles}
                  onVehicleClick={handleVehicleClick}
                  className="mb-12"
                />
              </div>

              {/* Desktop Grid */}
              <StaggerContainer className="hidden md:grid grid-cols-1 lg:grid-cols-2 gap-12 max-w-6xl mx-auto justify-items-center">
                {vehicles.map((vehicle) => (
                  <div
                    key={vehicle.id}
                    onClick={() => handleVehicleClick(vehicle)}
                    className="cursor-pointer"
                  >
                    <VehicleCard vehicle={vehicle} />
                  </div>
                ))}
              </StaggerContainer>
            </>
          )}
        </div>
      </section>

      {/* Call to Action */}
      <section className="section-light">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <TextReveal className="font-heading text-section font-section text-primary-black mb-6">
            Pronto para Reservar?
          </TextReveal>
          <TextReveal
            delay={0.2}
            className="text-body text-primary-mediumGray leading-body mb-8 max-w-2xl mx-auto"
          >
            Escolha o veículo perfeito para o seu evento e faça a sua reserva
            hoje mesmo. A nossa equipa está pronta para ajudar a tornar o seu
            evento inesquecível.
          </TextReveal>
          <ScrollReveal delay={0.4}>
            <GlowEffect glowColor="rgba(0, 0, 0, 0.2)">
              <AnimatedButton
                href="/reservas"
                variant="cta"
                className="inline-flex items-center justify-center"
              >
                Fazer Reserva Agora
                <svg
                  className="w-4 h-4 ml-2"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M9 5l7 7-7 7"
                  />
                </svg>
              </AnimatedButton>
            </GlowEffect>
          </ScrollReveal>
        </div>
      </section>

      {/* Vehicle Detail Modal */}
      <VehicleModal
        vehicle={selectedVehicle}
        isOpen={isModalOpen}
        onClose={closeModal}
      />
    </div>
  );
}
