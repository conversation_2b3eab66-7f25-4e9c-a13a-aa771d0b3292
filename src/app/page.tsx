import FeaturedVehicles from '@/components/FeaturedVehicles';
import ParallaxImage from '@/components/ParallaxImage';
import FadeIn from '@/components/animations/FadeIn';
import {
  ScrollReveal,
  TextReveal,
  StaggeredCard,
  IndependentSection,
  AnimatedButton,
  HoverCard as HoverCardEnhanced,
  ScrollOverlay,
} from '@/components/animations';

export default function HomePage() {
  return (
    <>
      {/* Hero Section */}
      <section className="section-light min-h-[80vh] flex items-center justify-center relative overflow-hidden parallax-container">
        <div className="absolute inset-0 z-0">
          <ParallaxImage
            src="/images/Fleur_de_Lys-sideview.png"
            alt="Fleur de Lys Vintage Van"
            speed={0.8}
          />
          <ScrollOverlay />
        </div>
        <div className="container mx-auto px-4 sm:px-6 lg:px-8 text-center relative z-10">
          <div className="max-w-4xl mx-auto">
            <FadeIn className="font-heading text-hero font-hero text-primary-black tracking-hero leading-hero mb-6">
              Veículos Vintage para o Seu Evento
            </FadeIn>
            <FadeIn
              delay={0.2}
              className="text-body text-primary-mediumGray leading-body mb-8 max-w-3xl mx-auto text-xl"
            >
              Transforme a sua campanha de marketing com os nossos veículos
              vintage únicos. Perfeitos para eventos, sessões fotográficas e
              campanhas publicitárias memoráveis.
            </FadeIn>
            <ScrollReveal
              delay={0.2}
              className="flex flex-col sm:flex-row gap-4 justify-center"
            >
              <AnimatedButton href="/frota" variant="primary">
                Ver a Nossa Frota
              </AnimatedButton>
              <AnimatedButton href="/reservas" variant="secondary">
                Fazer Reserva
              </AnimatedButton>
            </ScrollReveal>
          </div>
        </div>
      </section>

      {/* Service Overview Section */}
      <section className="section-alternate relative">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <ScrollReveal className="text-center mb-16">
            <TextReveal className="font-heading text-section font-section text-primary-black tracking-section mb-6">
              Porquê Escolher Veículos Vintage?
            </TextReveal>
            <TextReveal
              delay={0.2}
              className="text-body text-primary-mediumGray leading-body max-w-3xl mx-auto"
            >
              Os nossos veículos vintage oferecem uma presença única e memorável
              para qualquer evento de marketing, criando uma conexão emocional
              instantânea com o seu público.
            </TextReveal>
          </ScrollReveal>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mb-16 relative">
            {/* Service 1 */}
            <StaggeredCard index={0} className="w-full">
              <HoverCardEnhanced className="card p-8 text-center shadow-lg rounded-lg h-full">
                <div className="w-12 h-12 bg-accent-vintage text-white rounded-full flex items-center justify-center mx-auto mb-6">
                  <svg
                    className="w-6 h-6"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-4m-5 0H9m0 0H5m0 0h2M7 7h10M7 11h10M7 15h10"
                    />
                  </svg>
                </div>
                <h3 className="font-heading text-card font-card text-primary-black mb-4">
                  Eventos Corporativos
                </h3>
                <p className="text-body text-primary-mediumGray leading-body">
                  Impressione clientes e parceiros com veículos vintage únicos
                  em lançamentos de produtos, conferências e eventos
                  empresariais.
                </p>
              </HoverCardEnhanced>
            </StaggeredCard>

            {/* Service 2 */}
            <StaggeredCard index={1} className="w-full">
              <HoverCardEnhanced className="card p-8 text-center shadow-lg rounded-lg h-full">
                <div className="w-12 h-12 bg-accent-vintage text-white rounded-full flex items-center justify-center mx-auto mb-6">
                  <svg
                    className="w-6 h-6"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M3 9a2 2 0 012-2h.93a2 2 0 001.664-.89l.812-1.22A2 2 0 0110.07 4h3.86a2 2 0 011.664.89l.812 1.22A2 2 0 0018.07 7H19a2 2 0 012 2v9a2 2 0 01-2 2H5a2 2 0 01-2-2V9z"
                    />
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M15 13a3 3 0 11-6 0 3 3 0 016 0z"
                    />
                  </svg>
                </div>
                <h3 className="font-heading text-card font-card text-primary-black mb-4">
                  Sessões Fotográficas
                </h3>
                <p className="text-body text-primary-mediumGray leading-body">
                  Crie campanhas publicitárias memoráveis com o charme autêntico
                  dos nossos veículos vintage como cenário perfeito.
                </p>
              </HoverCardEnhanced>
            </StaggeredCard>

            {/* Service 3 */}
            <StaggeredCard index={2} className="w-full">
              <HoverCardEnhanced className="card p-8 text-center shadow-lg rounded-lg h-full">
                <div className="w-12 h-12 bg-accent-vintage text-white rounded-full flex items-center justify-center mx-auto mb-6">
                  <svg
                    className="w-6 h-6"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M11.049 2.927c.3-.921 1.603-.921 1.902 0l1.519 4.674a1 1 0 00.95.69h4.915c.969 0 1.371 1.24.588 1.81l-3.976 2.888a1 1 0 00-.363 1.118l1.518 4.674c.3.922-.755 1.688-1.538 1.118l-3.976-2.888a1 1 0 00-1.176 0l-3.976 2.888c-.783.57-1.838-.197-1.538-1.118l1.518-4.674a1 1 0 00-.363-1.118l-3.976-2.888c-.784-.57-.38-1.81.588-1.81h4.914a1 1 0 00.951-.69l1.519-4.674z"
                    />
                  </svg>
                </div>
                <h3 className="font-heading text-card font-card text-primary-black mb-4">
                  Eventos Especiais
                </h3>
                <p className="text-body text-primary-mediumGray leading-body">
                  Adicione um toque de elegância e nostalgia a casamentos,
                  festivais e celebrações com os nossos veículos vintage.
                </p>
              </HoverCardEnhanced>
            </StaggeredCard>
          </div>

          {/* Call to Action */}
          <IndependentSection triggerPoint={0.9} className="text-center">
            <TextReveal className="font-heading text-section font-section text-primary-black mb-4">
              Pronto para Criar Algo Memorável?
            </TextReveal>
            <TextReveal
              delay={0.2}
              className="text-body text-primary-mediumGray leading-body mb-8 max-w-2xl mx-auto"
            >
              Entre em contacto connosco hoje e descubra como os nossos veículos
              vintage podem transformar o seu próximo evento de marketing.
            </TextReveal>
            <ScrollReveal delay={0.2}>
              <AnimatedButton href="/contacto" variant="cta">
                Contacte-nos Agora
                <svg
                  className="w-4 h-4"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M9 5l7 7-7 7"
                  />
                </svg>
              </AnimatedButton>
            </ScrollReveal>
          </IndependentSection>
        </div>
      </section>

      {/* Featured Vehicles from Supabase */}
      <FeaturedVehicles />
    </>
  );
}
