'use client';

import { useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useAuth } from '@/contexts/AuthContext';

export default function AdminPage() {
  const { user, isLoading, isAdmin } = useAuth();
  const router = useRouter();

  useEffect(() => {
    if (!isLoading) {
      if (!user || !isAdmin) {
        router.push('/admin/login');
      } else {
        router.push('/admin/dashboard');
      }
    }
  }, [user, isLoading, isAdmin, router]);

  return (
    <div className="min-h-screen flex items-center justify-center bg-background-cream">
      <div className="text-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-black mx-auto mb-4"></div>
        <p className="text-primary-mediumGray">A redirecionar...</p>
      </div>
    </div>
  );
}
