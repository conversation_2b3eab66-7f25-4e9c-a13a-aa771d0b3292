'use client';

import { useState, useEffect, Suspense } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { useAuth } from '@/contexts/AuthContext';
import { validateLoginForm } from '@/utils/authErrors';
import Link from 'next/link';

function AdminLoginForm() {
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState('');
  const [fieldErrors, setFieldErrors] = useState<{
    email?: string;
    password?: string;
  }>({});

  const { signIn, user, isAdmin } = useAuth();
  const router = useRouter();
  const searchParams = useSearchParams();

  // Check for error in URL params
  useEffect(() => {
    const errorParam = searchParams.get('error');
    if (errorParam === 'access_denied') {
      setError(
        'Acesso negado. Esta área é restrita a administradores autorizados.'
      );
    }
  }, [searchParams]);

  // Clear general error when user starts typing
  useEffect(() => {
    if (error && (email || password)) {
      const timer = setTimeout(() => {
        setError('');
      }, 3000);
      return () => clearTimeout(timer);
    }
  }, [email, password, error]);

  // Redirect if already authenticated as admin
  useEffect(() => {
    if (user && isAdmin) {
      router.push('/admin/dashboard');
    }
  }, [user, isAdmin, router]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);
    setError('');
    setFieldErrors({});

    // Client-side validation
    const validation = validateLoginForm(email, password);
    if (!validation.isValid) {
      setFieldErrors(validation.errors);
      setIsLoading(false);
      return;
    }

    const result = await signIn(email, password);

    if (result.error) {
      setError(result.error.message);
      setIsLoading(false);
    } else {
      // Success - redirect will happen via useEffect
      router.push('/admin/dashboard');
    }
  };

  // Clear field errors when user starts typing
  const handleEmailChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setEmail(e.target.value);
    if (fieldErrors.email) {
      setFieldErrors((prev) => ({ ...prev, email: undefined }));
    }
  };

  const handlePasswordChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setPassword(e.target.value);
    if (fieldErrors.password) {
      setFieldErrors((prev) => ({ ...prev, password: undefined }));
    }
  };

  return (
    <div className="min-h-screen flex items-center justify-center bg-background-cream py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-md w-full space-y-8">
        <div>
          <Link href="/" className="flex justify-center">
            <h1 className="font-logo text-2xl font-bold text-primary-black">
              Vintage Marketing Portugal
            </h1>
          </Link>
          <h2 className="mt-6 text-center text-3xl font-extrabold text-primary-black">
            Acesso Administrativo
          </h2>
          <p className="mt-2 text-center text-sm text-primary-mediumGray">
            Faça login para aceder ao painel de administração
          </p>
          <div className="mt-4 text-center">
            <div className="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
              <svg
                className="w-3 h-3 mr-1"
                fill="currentColor"
                viewBox="0 0 20 20"
              >
                <path
                  fillRule="evenodd"
                  d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z"
                  clipRule="evenodd"
                />
              </svg>
              Apenas administradores autorizados
            </div>
          </div>
        </div>

        <form className="mt-8 space-y-6" onSubmit={handleSubmit}>
          <div className="space-y-4">
            <div>
              <label
                htmlFor="email"
                className="block text-sm font-medium text-gray-700 mb-1"
              >
                Endereço de email
              </label>
              <input
                id="email"
                name="email"
                type="email"
                autoComplete="email"
                required
                className={`appearance-none relative block w-full px-3 py-2 border placeholder-primary-mediumGray text-primary-black rounded-md focus:outline-none focus:ring-2 focus:ring-primary-black focus:border-primary-black sm:text-sm ${
                  fieldErrors.email
                    ? 'border-red-300 focus:ring-red-500 focus:border-red-500'
                    : 'border-primary-lightGray'
                }`}
                placeholder="<EMAIL>"
                value={email}
                onChange={handleEmailChange}
                disabled={isLoading}
              />
              {fieldErrors.email && (
                <p className="mt-1 text-sm text-red-600">{fieldErrors.email}</p>
              )}
            </div>
            <div>
              <label
                htmlFor="password"
                className="block text-sm font-medium text-gray-700 mb-1"
              >
                Palavra-passe
              </label>
              <input
                id="password"
                name="password"
                type="password"
                autoComplete="current-password"
                required
                className={`appearance-none relative block w-full px-3 py-2 border placeholder-primary-mediumGray text-primary-black rounded-md focus:outline-none focus:ring-2 focus:ring-primary-black focus:border-primary-black sm:text-sm ${
                  fieldErrors.password
                    ? 'border-red-300 focus:ring-red-500 focus:border-red-500'
                    : 'border-primary-lightGray'
                }`}
                placeholder="Introduza a sua palavra-passe"
                value={password}
                onChange={handlePasswordChange}
                disabled={isLoading}
              />
              {fieldErrors.password && (
                <p className="mt-1 text-sm text-red-600">
                  {fieldErrors.password}
                </p>
              )}
            </div>
          </div>

          {error && (
            <div className="rounded-md bg-red-50 border border-red-200 p-4">
              <div className="flex">
                <div className="flex-shrink-0">
                  <svg
                    className="h-5 w-5 text-red-400"
                    viewBox="0 0 20 20"
                    fill="currentColor"
                  >
                    <path
                      fillRule="evenodd"
                      d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z"
                      clipRule="evenodd"
                    />
                  </svg>
                </div>
                <div className="ml-3">
                  <h3 className="text-sm font-medium text-red-800">
                    Erro de Autenticação
                  </h3>
                  <div className="mt-1 text-sm text-red-700">{error}</div>
                </div>
              </div>
            </div>
          )}

          <div>
            <button
              type="submit"
              disabled={isLoading}
              className="group relative w-full flex justify-center py-2 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-primary-black hover:bg-primary-mediumGray focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-black disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {isLoading ? (
                <div className="flex items-center">
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                  A entrar...
                </div>
              ) : (
                'Entrar'
              )}
            </button>
          </div>

          <div className="text-center space-y-2">
            <Link
              href="/"
              className="text-sm text-primary-mediumGray hover:text-primary-black"
            >
              ← Voltar ao site principal
            </Link>

            {/* Development helper - only show in development */}
            {process.env.NODE_ENV === 'development' && (
              <details className="mt-4 text-left">
                <summary className="text-xs text-gray-500 cursor-pointer hover:text-gray-700">
                  ℹ️ Informações para Desenvolvedores
                </summary>
                <div className="mt-2 p-3 bg-gray-50 rounded-md text-xs text-gray-600">
                  <p className="font-medium mb-2">
                    Como criar um utilizador admin:
                  </p>
                  <ol className="list-decimal list-inside space-y-1">
                    <li>Aceda ao dashboard do Supabase</li>
                    <li>Vá para Authentication → Users</li>
                    <li>
                      Clique em &quot;Add user&quot; ou &quot;Invite user&quot;
                    </li>
                    <li>Introduza email e palavra-passe</li>
                    <li>Após criar, clique no utilizador para editar</li>
                    <li>
                      Em &quot;User Metadata&quot;, adicione:{' '}
                      <code>{`{"role": "admin"}`}</code>
                    </li>
                    <li>Guarde as alterações</li>
                  </ol>
                </div>
              </details>
            )}
          </div>
        </form>
      </div>
    </div>
  );
}

export default function AdminLoginPage() {
  return (
    <Suspense
      fallback={
        <div className="min-h-screen flex items-center justify-center bg-background-cream">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-black mx-auto mb-4"></div>
            <p className="text-primary-mediumGray">A carregar...</p>
          </div>
        </div>
      }
    >
      <AdminLoginForm />
    </Suspense>
  );
}
