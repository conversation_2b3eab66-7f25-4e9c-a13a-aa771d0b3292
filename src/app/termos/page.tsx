import { Metadata } from 'next';
import FadeIn from '@/components/animations/FadeIn';
import {
  ScrollReveal,
  TextReveal,
  AnimatedButton,
  GlowEffect,
} from '@/components/animations';

export const metadata: Metadata = {
  title: 'Termos e Condições - Vintage Marketing Portugal',
  description:
    'Consulte os nossos termos e condições de aluguer de veículos vintage para eventos de marketing e campanhas publicitárias em Portugal.',
  keywords:
    'termos condições aluguer veículos vintage, políticas aluguer carros vintage, condições rental Portugal',
};

export default function TermosPage() {
  return (
    <div className="min-h-screen bg-background-cream">
      {/* Hero Section */}
      <section className="section-light">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <div className="max-w-4xl mx-auto">
            <FadeIn className="font-heading text-hero font-hero text-primary-black tracking-hero leading-hero mb-6">
              Termos e Condições
            </FadeIn>
            <FadeIn
              delay={0.2}
              className="text-body text-primary-mediumGray leading-body mb-8 max-w-3xl mx-auto"
            >
              Consulte os nossos termos e condições de aluguer de veículos
              vintage. Transparência e clareza são fundamentais para uma
              parceria de sucesso.
            </FadeIn>
            <FadeIn delay={0.4} className="text-sm text-primary-lightGray">
              Última atualização: Janeiro 2025
            </FadeIn>
          </div>
        </div>
      </section>

      {/* Terms Content */}
      <section className="section-light">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="max-w-4xl mx-auto">
            <ScrollReveal delay={0.1}>
              <div className="card p-8 mb-8">
                <h2 className="font-heading text-section font-section text-primary-black mb-6">
                  1. Condições Gerais de Aluguer
                </h2>

                <div className="space-y-6 text-body text-primary-mediumGray leading-body">
                  <div>
                    <h3 className="font-heading text-card font-card text-primary-black mb-3">
                      1.1 Elegibilidade
                    </h3>
                    <p className="mb-3">
                      O aluguer de veículos vintage está disponível apenas para
                      clientes com idade igual ou superior a 25 anos, com carta
                      de condução válida há pelo menos 3 anos e seguro de
                      responsabilidade civil em vigor.
                    </p>
                  </div>

                  <div>
                    <h3 className="font-heading text-card font-card text-primary-black mb-3">
                      1.2 Reservas
                    </h3>
                    <p className="mb-3">
                      As reservas devem ser efetuadas com um mínimo de 7 dias de
                      antecedência. A confirmação da reserva está sujeita à
                      disponibilidade do veículo e ao pagamento de um sinal de
                      50% do valor total.
                    </p>
                  </div>

                  <div>
                    <h3 className="font-heading text-card font-card text-primary-black mb-3">
                      1.3 Cancelamentos
                    </h3>
                    <p className="mb-3">
                      Cancelamentos efetuados até 48 horas antes do evento têm
                      direito a reembolso total. Cancelamentos com menos de 48
                      horas de antecedência estão sujeitos a uma taxa de 50% do
                      valor da reserva.
                    </p>
                  </div>
                </div>
              </div>
            </ScrollReveal>

            <ScrollReveal delay={0.2}>
              <div className="card p-8 mb-8">
                <h2 className="font-heading text-section font-section text-primary-black mb-6">
                  2. Responsabilidades do Cliente
                </h2>

                <div className="space-y-6 text-body text-primary-mediumGray leading-body">
                  <div>
                    <h3 className="font-heading text-card font-card text-primary-black mb-3">
                      2.1 Cuidados com o Veículo
                    </h3>
                    <ul className="space-y-2 ml-4">
                      <li className="flex items-start">
                        <span className="w-2 h-2 bg-primary-black rounded-full mt-2 mr-3 flex-shrink-0"></span>
                        Utilizar o veículo apenas para os fins acordados no
                        contrato
                      </li>
                      <li className="flex items-start">
                        <span className="w-2 h-2 bg-primary-black rounded-full mt-2 mr-3 flex-shrink-0"></span>
                        Não fumar no interior do veículo
                      </li>
                      <li className="flex items-start">
                        <span className="w-2 h-2 bg-primary-black rounded-full mt-2 mr-3 flex-shrink-0"></span>
                        Não consumir alimentos ou bebidas no interior do veículo
                      </li>
                      <li className="flex items-start">
                        <span className="w-2 h-2 bg-primary-black rounded-full mt-2 mr-3 flex-shrink-0"></span>
                        Reportar imediatamente qualquer dano ou problema técnico
                      </li>
                    </ul>
                  </div>

                  <div>
                    <h3 className="font-heading text-card font-card text-primary-black mb-3">
                      2.2 Condução
                    </h3>
                    <p className="mb-3">
                      Apenas condutores autorizados e identificados no contrato
                      podem conduzir o veículo. A velocidade máxima não deve
                      exceder os 50 km/h em qualquer circunstância.
                    </p>
                  </div>
                </div>
              </div>
            </ScrollReveal>

            <ScrollReveal delay={0.3}>
              <div className="card p-8 mb-8">
                <h2 className="font-heading text-section font-section text-primary-black mb-6">
                  3. Seguros e Responsabilidades
                </h2>

                <div className="space-y-6 text-body text-primary-mediumGray leading-body">
                  <div>
                    <h3 className="font-heading text-card font-card text-primary-black mb-3">
                      3.1 Cobertura de Seguro
                    </h3>
                    <p className="mb-3">
                      Todos os nossos veículos estão cobertos por seguro contra
                      todos os riscos. O cliente é responsável por uma franquia
                      de €500 em caso de danos.
                    </p>
                  </div>

                  <div>
                    <h3 className="font-heading text-card font-card text-primary-black mb-3">
                      3.2 Exclusões
                    </h3>
                    <p className="mb-3">
                      O seguro não cobre danos resultantes de negligência grave,
                      condução sob influência de álcool ou drogas, ou utilização
                      do veículo fora dos termos acordados.
                    </p>
                  </div>
                </div>
              </div>
            </ScrollReveal>

            <ScrollReveal delay={0.4}>
              <div className="card p-8 mb-8">
                <h2 className="font-heading text-section font-section text-primary-black mb-6">
                  4. Preços e Pagamentos
                </h2>

                <div className="space-y-6 text-body text-primary-mediumGray leading-body">
                  <div>
                    <h3 className="font-heading text-card font-card text-primary-black mb-3">
                      4.1 Estrutura de Preços
                    </h3>
                    <ul className="space-y-2 ml-4">
                      <li className="flex items-start">
                        <span className="w-2 h-2 bg-primary-black rounded-full mt-2 mr-3 flex-shrink-0"></span>
                        Aluguer por meio dia (4 horas): 70% do preço diário
                      </li>
                      <li className="flex items-start">
                        <span className="w-2 h-2 bg-primary-black rounded-full mt-2 mr-3 flex-shrink-0"></span>
                        Aluguer por dia completo (8 horas): preço base
                      </li>
                      <li className="flex items-start">
                        <span className="w-2 h-2 bg-primary-black rounded-full mt-2 mr-3 flex-shrink-0"></span>
                        Períodos prolongados: desconto progressivo a partir do
                        3º dia
                      </li>
                    </ul>
                  </div>

                  <div>
                    <h3 className="font-heading text-card font-card text-primary-black mb-3">
                      4.2 Custos Adicionais
                    </h3>
                    <p className="mb-3">
                      Entregas fora da área metropolitana de Lisboa estão
                      sujeitas a custos de deslocação. Limpeza especial ou
                      reparações necessárias serão cobradas separadamente.
                    </p>
                  </div>
                </div>
              </div>
            </ScrollReveal>

            <ScrollReveal delay={0.5}>
              <div className="card p-8 mb-8">
                <h2 className="font-heading text-section font-section text-primary-black mb-6">
                  5. Política de Privacidade
                </h2>

                <div className="space-y-6 text-body text-primary-mediumGray leading-body">
                  <p>
                    A Vintage Marketing Portugal compromete-se a proteger a
                    privacidade dos seus clientes. Os dados pessoais recolhidos
                    são utilizados exclusivamente para a prestação dos nossos
                    serviços e não são partilhados com terceiros sem
                    consentimento expresso.
                  </p>

                  <p>
                    Os clientes têm o direito de aceder, retificar ou eliminar
                    os seus dados pessoais a qualquer momento, contactando-nos
                    através dos meios disponibilizados na nossa página de
                    contacto.
                  </p>
                </div>
              </div>
            </ScrollReveal>

            <ScrollReveal delay={0.6}>
              <div className="card p-8">
                <h2 className="font-heading text-section font-section text-primary-black mb-6">
                  6. Contactos e Resolução de Conflitos
                </h2>

                <div className="space-y-6 text-body text-primary-mediumGray leading-body">
                  <p>
                    Para esclarecimentos sobre estes termos e condições ou para
                    reportar qualquer problema, contacte-nos através do email
                    <EMAIL> ou telefone +351 912 345
                    678.
                  </p>

                  <p>
                    Eventuais conflitos serão resolvidos preferencialmente
                    através de mediação. Em caso de litígio, será competente o
                    tribunal da comarca de Lisboa.
                  </p>

                  <p className="text-sm text-primary-lightGray pt-4 border-t border-primary-lightGray/30">
                    Estes termos e condições estão sujeitos à legislação
                    portuguesa e podem ser atualizados periodicamente. Os
                    clientes serão notificados de quaisquer alterações
                    significativas.
                  </p>
                </div>
              </div>
            </ScrollReveal>
          </div>
        </div>
      </section>

      {/* Contact CTA */}
      <section className="section-dark">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <TextReveal className="font-heading text-section font-section text-primary-white mb-6">
            Tem Questões?
          </TextReveal>
          <TextReveal
            delay={0.2}
            className="text-body text-primary-lightGray leading-body mb-8 max-w-2xl mx-auto"
          >
            A nossa equipa está disponível para esclarecer qualquer dúvida sobre
            os nossos termos e condições ou serviços de aluguer.
          </TextReveal>
          <ScrollReveal delay={0.4}>
            <GlowEffect glowColor="rgba(255, 255, 255, 0.3)">
              <AnimatedButton
                href="/contacto"
                variant="cta"
                className="flex items-center justify-center"
              >
                Contacte-nos
                <svg
                  className="w-4 h-4 ml-2"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M9 5l7 7-7 7"
                  />
                </svg>
              </AnimatedButton>
            </GlowEffect>
          </ScrollReveal>
        </div>
      </section>
    </div>
  );
}
