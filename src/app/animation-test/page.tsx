'use client';

import { useEffect, useState } from 'react';
import AnimationTestSuite from '@/components/AnimationTestSuite';
import {
  testAnimationPerformance,
  getResponsiveAnimationConfig,
  preloadAnimationAssets,
  validateAnimationTiming,
  globalAnimationMonitor,
} from '@/utils/animationOptimization';
import { useAnimationOptimization } from '@/hooks/usePerformanceOptimization';

export default function AnimationTestPage() {
  const [performanceResults, setPerformanceResults] = useState<{
    averageFPS: number;
    minFPS: number;
    maxFPS: number;
  } | null>(null);
  const [responsiveConfig, setResponsiveConfig] = useState(
    getResponsiveAnimationConfig()
  );
  const [isTestingPerformance, setIsTestingPerformance] = useState(false);
  const { shouldOptimize, prefersReducedMotion, animationConfig } =
    useAnimationOptimization();

  useEffect(() => {
    // Preload animation assets
    preloadAnimationAssets();

    // Update responsive config on resize
    const handleResize = () => {
      setResponsiveConfig(getResponsiveAnimationConfig());
    };

    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  const runPerformanceTest = async () => {
    setIsTestingPerformance(true);

    try {
      const results = await testAnimationPerformance(() => {
        // Trigger multiple animations simultaneously
        const elements = document.querySelectorAll(
          '[data-testid="performance-test"]'
        );
        elements.forEach((el, index) => {
          const htmlEl = el as HTMLElement;
          htmlEl.style.animation = `optimizedFadeIn 0.6s ease-out ${index * 0.05}s both`;
        });
      }, 3000);

      setPerformanceResults(results);
    } catch (error) {
      console.error('Performance test failed:', error);
    } finally {
      setIsTestingPerformance(false);
    }
  };

  const timingValidation = validateAnimationTiming(
    animationConfig.duration,
    0.2, // typical delay
    animationConfig.stagger
  );

  return (
    <div className="min-h-screen bg-background-cream">
      {/* Header */}
      <div className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
          <h1 className="text-3xl font-bold text-gray-900">
            Animation Performance Test Suite
          </h1>
          <p className="mt-2 text-gray-600">
            Comprehensive testing of all animation components across different
            devices and preferences.
          </p>
        </div>
      </div>

      {/* Performance Dashboard */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-8">
          {/* Device & Performance Info */}
          <div className="bg-white rounded-lg shadow-sm p-6">
            <h2 className="text-lg font-semibold mb-4">Device Information</h2>
            <div className="space-y-2 text-sm">
              <div className="flex justify-between">
                <span>Screen Size:</span>
                <span className="font-medium">
                  {responsiveConfig.isMobile && 'Mobile'}
                  {responsiveConfig.isTablet && 'Tablet'}
                  {responsiveConfig.isDesktop && 'Desktop'}
                </span>
              </div>
              <div className="flex justify-between">
                <span>Should Optimize:</span>
                <span
                  className={`font-medium ${shouldOptimize ? 'text-orange-600' : 'text-green-600'}`}
                >
                  {shouldOptimize ? 'Yes' : 'No'}
                </span>
              </div>
              <div className="flex justify-between">
                <span>Reduced Motion:</span>
                <span
                  className={`font-medium ${prefersReducedMotion ? 'text-blue-600' : 'text-gray-600'}`}
                >
                  {prefersReducedMotion ? 'Enabled' : 'Disabled'}
                </span>
              </div>
              <div className="flex justify-between">
                <span>Animation Complexity:</span>
                <span className="font-medium capitalize">
                  {responsiveConfig.animationComplexity}
                </span>
              </div>
            </div>
          </div>

          {/* Animation Configuration */}
          <div className="bg-white rounded-lg shadow-sm p-6">
            <h2 className="text-lg font-semibold mb-4">Animation Config</h2>
            <div className="space-y-2 text-sm">
              <div className="flex justify-between">
                <span>Duration:</span>
                <span className="font-medium">{animationConfig.duration}s</span>
              </div>
              <div className="flex justify-between">
                <span>Easing:</span>
                <span className="font-medium">{animationConfig.ease}</span>
              </div>
              <div className="flex justify-between">
                <span>Stagger:</span>
                <span className="font-medium">{animationConfig.stagger}s</span>
              </div>
              <div className="flex justify-between">
                <span>Distance Multiplier:</span>
                <span className="font-medium">
                  {responsiveConfig.distanceMultiplier}x
                </span>
              </div>
            </div>
          </div>

          {/* Performance Results */}
          <div className="bg-white rounded-lg shadow-sm p-6">
            <div className="flex items-center justify-between mb-4">
              <h2 className="text-lg font-semibold">Performance Test</h2>
              <button
                onClick={runPerformanceTest}
                disabled={isTestingPerformance}
                className="btn-primary text-sm px-4 py-2 disabled:opacity-50"
              >
                {isTestingPerformance ? 'Testing...' : 'Run Test'}
              </button>
            </div>

            {performanceResults ? (
              <div className="space-y-2 text-sm">
                <div className="flex justify-between">
                  <span>Average FPS:</span>
                  <span
                    className={`font-medium ${
                      performanceResults.averageFPS >= 55
                        ? 'text-green-600'
                        : performanceResults.averageFPS >= 30
                          ? 'text-orange-600'
                          : 'text-red-600'
                    }`}
                  >
                    {performanceResults.averageFPS.toFixed(1)}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span>Min FPS:</span>
                  <span className="font-medium">
                    {performanceResults.minFPS}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span>Max FPS:</span>
                  <span className="font-medium">
                    {performanceResults.maxFPS}
                  </span>
                </div>
              </div>
            ) : (
              <p className="text-sm text-gray-500">
                Click &quot;Run Test&quot; to measure animation performance
              </p>
            )}
          </div>
        </div>

        {/* Timing Validation */}
        {!timingValidation.isValid && (
          <div className="bg-background-beige border border-primary-lightGray/30 rounded-lg p-4 mb-8">
            <h3 className="text-sm font-medium text-primary-black mb-2">
              Animation Timing Warnings:
            </h3>
            <ul className="text-sm text-primary-mediumGray space-y-1">
              {timingValidation.warnings.map((warning, index) => (
                <li key={index}>• {warning}</li>
              ))}
            </ul>
          </div>
        )}

        {/* Performance Test Grid */}
        <div className="mb-8">
          <h2 className="text-xl font-semibold mb-4">
            Performance Stress Test
          </h2>
          <div className="grid grid-cols-4 md:grid-cols-8 lg:grid-cols-12 gap-2">
            {Array.from({ length: 48 }, (_, i) => (
              <div
                key={i}
                data-testid="performance-test"
                className="aspect-square bg-primary-black rounded opacity-20 flex items-center justify-center text-white text-xs"
              >
                {i + 1}
              </div>
            ))}
          </div>
        </div>

        {/* Main Test Suite */}
        <AnimationTestSuite />

        {/* Mobile-specific Tests */}
        {responsiveConfig.isMobile && (
          <div className="mt-12 bg-blue-50 border border-blue-200 rounded-lg p-6">
            <h2 className="text-lg font-semibold text-blue-900 mb-4">
              Mobile-Optimized Animations
            </h2>
            <p className="text-blue-800 text-sm mb-4">
              These animations are automatically optimized for mobile devices
              with reduced complexity and shorter durations.
            </p>
            <div className="grid grid-cols-2 gap-4">
              <div className="bg-white rounded p-4 text-center">
                <div className="w-8 h-8 bg-blue-500 rounded-full mx-auto mb-2 animate-pulse"></div>
                <p className="text-xs">Reduced Motion</p>
              </div>
              <div className="bg-white rounded p-4 text-center">
                <div className="w-8 h-8 bg-green-500 rounded-full mx-auto mb-2 animate-bounce"></div>
                <p className="text-xs">Touch Optimized</p>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
