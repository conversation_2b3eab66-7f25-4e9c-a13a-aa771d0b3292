import type { Metadata } from 'next';
import { Inter, Open_Sans, Limelight } from 'next/font/google';
import './globals.css';
import { Layout } from '@/components/layout';
import StructuredData from '@/components/StructuredData';
import { AuthProvider } from '@/contexts/AuthContext';
import PagePreloader from '@/components/PagePreloader';

const inter = Inter({ subsets: ['latin'] });
const openSans = Open_Sans({
  weight: ['400', '500', '600', '700', '800'],
  subsets: ['latin'],
  variable: '--font-open-sans',
});

const limelight = Limelight({
  weight: '400',
  subsets: ['latin'],
  variable: '--font-limelight',
});

export const metadata: Metadata = {
  title:
    'Vintage Marketing Portugal - Aluguer de Veículos Vintage para Eventos',
  description:
    'Aluguer de veículos vintage únicos para eventos de marketing, sessões fotográficas e campanhas publicitárias em Portugal. Fleur de Lys Van e BMW R100 RS disponíveis.',
  keywords:
    'aluguer veículos vintage, carros vintage eventos, marketing Portugal, sessões fotográficas vintage, eventos corporativos, Fleur de Lys Van, BMW R100 RS, veículos antigos Portugal, aluguer carros vintage Lisboa, eventos marketing Portugal',
  authors: [{ name: 'Vintage Marketing Portugal' }],
  robots: 'index, follow',
  openGraph: {
    title: 'Vintage Marketing Portugal - Veículos Vintage para Eventos',
    description:
      'Transforme a sua campanha de marketing com os nossos veículos vintage únicos. Perfeitos para eventos, sessões fotográficas e campanhas publicitárias memoráveis.',
    locale: 'pt_PT',
    type: 'website',
    siteName: 'Vintage Marketing Portugal',
    url: 'https://vintagemarketingportugal.com',
  },
  twitter: {
    card: 'summary_large_image',
    title: 'Vintage Marketing Portugal - Veículos Vintage para Eventos',
    description:
      'Aluguer de veículos vintage para eventos de marketing e campanhas publicitárias em Portugal.',
  },
  alternates: {
    canonical: 'https://vintagemarketingportugal.com',
  },
};

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <html lang="pt">
      <head>
        <StructuredData />
      </head>
      <body
        className={`${inter.className} ${openSans.variable} ${limelight.variable}`}
      >
        <AuthProvider>
          <Layout>{children}</Layout>
          <PagePreloader />
        </AuthProvider>
      </body>
    </html>
  );
}
