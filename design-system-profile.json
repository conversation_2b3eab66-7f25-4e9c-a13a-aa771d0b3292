{"designSystemProfile": {"colorPalette": {"primary": {"black": "#000000", "darkGray": "#1A1A1A", "mediumGray": "#666666", "lightGray": "#999999", "white": "#FFFFFF"}, "background": {"cream": "#F5F1E8", "beige": "#E8E0D0", "darkSection": "#000000", "cardBackground": "#FFFFFF"}, "accent": {"vintage": "#8B4513", "warmBrown": "#A0522D"}}, "typography": {"headings": {"hero": {"fontSize": "clamp(2.5rem, 5vw, 4rem)", "fontWeight": "900", "color": "#000000", "letterSpacing": "-0.02em", "lineHeight": "1.1"}, "section": {"fontSize": "clamp(1.8rem, 3vw, 2.5rem)", "fontWeight": "800", "color": "#000000", "letterSpacing": "-0.01em"}, "card": {"fontSize": "1.25rem", "fontWeight": "700", "color": "#000000"}}, "body": {"primary": {"fontSize": "1rem", "fontWeight": "400", "color": "#666666", "lineHeight": "1.6"}, "secondary": {"fontSize": "0.875rem", "fontWeight": "400", "color": "#999999"}}, "navigation": {"fontSize": "0.95rem", "fontWeight": "500", "color": "#666666"}}, "elementStyling": {"layout": {"pageBackground": {"background": "#F5F1E8", "texture": "subtle paper/vintage texture overlay", "DO_NOT": "Apply gradients to page background"}, "sections": {"lightSection": {"background": "#F5F1E8", "padding": "4rem 2rem"}, "darkSection": {"background": "#000000", "color": "#FFFFFF", "padding": "4rem 2rem"}}}, "cards": {"vehicleCard": {"background": "#FFFFFF", "border": "none", "borderRadius": "12px", "shadow": "0 8px 24px rgba(0, 0, 0, 0.12)", "padding": "1.5rem", "transition": "transform 0.3s ease, shadow 0.3s ease", "hover": {"transform": "translateY(-4px)", "shadow": "0 12px 32px rgba(0, 0, 0, 0.18)"}, "DO_NOT": "Apply gradients to card backgrounds or borders"}, "imageContainer": {"borderRadius": "8px", "overflow": "hidden", "aspectRatio": "4/3", "background": "transparent"}, "priceText": {"fontSize": "1.5rem", "fontWeight": "700", "color": "#000000"}, "titleText": {"fontSize": "1.25rem", "fontWeight": "600", "color": "#000000"}, "subtitleText": {"fontSize": "0.875rem", "fontWeight": "400", "color": "#666666"}}, "buttons": {"primary": {"background": "#000000", "color": "#FFFFFF", "border": "none", "borderRadius": "24px", "padding": "12px 24px", "fontSize": "0.95rem", "fontWeight": "600", "transition": "all 0.3s ease", "hover": {"background": "#1A1A1A", "transform": "translateY(-1px)"}, "DO_NOT": "Apply gradients to button backgrounds"}, "secondary": {"background": "transparent", "color": "#000000", "border": "2px solid #000000", "borderRadius": "24px", "padding": "10px 22px", "fontSize": "0.95rem", "fontWeight": "600", "transition": "all 0.3s ease", "hover": {"background": "#000000", "color": "#FFFFFF"}}, "cta": {"background": "#000000", "color": "#FFFFFF", "borderRadius": "50px", "padding": "14px 28px", "fontSize": "1rem", "fontWeight": "600", "display": "inline-flex", "alignItems": "center", "gap": "8px", "transition": "all 0.3s ease", "hover": {"background": "#1A1A1A", "transform": "translateX(4px)"}}}, "navigation": {"navbar": {"background": "rgba(245, 241, 232, 0.95)", "backdropFilter": "blur(10px)", "borderBottom": "1px solid rgba(0, 0, 0, 0.1)", "padding": "1rem 2rem"}, "logo": {"background": "#000000", "color": "#FFFFFF", "width": "40px", "height": "40px", "borderRadius": "8px", "display": "flex", "alignItems": "center", "justifyContent": "center", "fontSize": "1.25rem", "fontWeight": "700"}, "links": {"color": "#666666", "fontSize": "0.95rem", "fontWeight": "500", "textDecoration": "none", "transition": "color 0.3s ease", "hover": {"color": "#000000"}}}, "gallery": {"masonryGrid": {"display": "grid", "gridTemplateColumns": "repeat(auto-fit, minmax(280px, 1fr))", "gap": "1.5rem", "padding": "2rem"}, "imageCard": {"borderRadius": "12px", "overflow": "hidden", "shadow": "0 4px 16px rgba(0, 0, 0, 0.1)", "transition": "transform 0.3s ease, shadow 0.3s ease", "hover": {"transform": "scale(1.02)", "shadow": "0 8px 24px rgba(0, 0, 0, 0.15)"}}}, "timeline": {"container": {"position": "relative", "padding": "2rem 0"}, "yearSelector": {"activeYear": {"background": "#000000", "color": "#FFFFFF", "borderRadius": "24px", "padding": "8px 20px", "fontSize": "0.95rem", "fontWeight": "600"}, "inactiveYear": {"background": "transparent", "color": "#999999", "fontSize": "0.95rem", "fontWeight": "500"}}}, "statistics": {"container": {"background": "#000000", "color": "#FFFFFF", "padding": "4rem 2rem", "textAlign": "center"}, "number": {"fontSize": "clamp(2rem, 4vw, 3rem)", "fontWeight": "900", "color": "#FFFFFF"}, "label": {"fontSize": "1rem", "fontWeight": "400", "color": "#CCCCCC"}}, "icons": {"circularIcon": {"background": "#000000", "color": "#FFFFFF", "width": "48px", "height": "48px", "borderRadius": "50%", "display": "flex", "alignItems": "center", "justifyContent": "center", "fontSize": "1.25rem", "DO_NOT": "Apply card gradients or colors to icons"}, "arrowIcon": {"color": "#FFFFFF", "fontSize": "1rem", "marginLeft": "8px"}}}, "layoutPatterns": {"heroSection": {"background": "#F5F1E8", "minHeight": "80vh", "display": "flex", "alignItems": "center", "padding": "2rem", "textAlign": "center"}, "gridLayout": {"collections": {"display": "grid", "gridTemplateColumns": "repeat(auto-fit, minmax(300px, 1fr))", "gap": "2rem", "padding": "2rem"}, "gallery": {"display": "grid", "gridTemplateColumns": "repeat(auto-fit, minmax(250px, 1fr))", "gap": "1.5rem", "padding": "2rem"}}}, "animations": {"cardHover": {"transform": "translateY(-4px)", "transition": "transform 0.3s ease, shadow 0.3s ease"}, "buttonHover": {"transform": "translateY(-1px)", "transition": "all 0.3s ease"}, "ctaHover": {"transform": "translateX(4px)", "transition": "all 0.3s ease"}}, "doNotRules": {"gradients": ["DO NOT apply gradients to card backgrounds - use solid white (#FFFFFF)", "DO NOT apply gradients to button backgrounds - use solid black (#000000)", "DO NOT apply gradients to icon backgrounds - use solid colors only", "DO NOT apply card styling to page backgrounds"], "colors": ["DO NOT use bright colors - stick to black, white, grays, and cream tones", "DO NOT apply button colors to text elements", "DO NOT use card background colors for navigation elements", "DO NOT mix warm and cool color temperatures"], "effects": ["DO NOT apply shadows to text elements", "DO NOT use border radius on page containers", "DO NOT apply hover effects to static content", "DO NOT use card styling on navigation elements"]}, "responsiveBreakpoints": {"mobile": "320px - 768px", "tablet": "768px - 1024px", "desktop": "1024px+"}, "accessibilityGuidelines": {"contrast": "Minimum 4.5:1 ratio for text", "focusStates": "Visible focus indicators on all interactive elements", "altText": "Descriptive alt text for all vehicle images"}}}