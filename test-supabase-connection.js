// Simple test script to verify Supabase connection
const { createClient } = require('@supabase/supabase-js');
require('dotenv').config({ path: '.env.local' });

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;

console.log('Testing Supabase connection...');
console.log('URL:', supabaseUrl);
console.log('Key:', supabaseAnonKey ? 'Present' : 'Missing');

if (!supabaseUrl || !supabaseAnonKey) {
  console.error('❌ Missing Supabase credentials in .env.local');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseAnonKey);

async function testConnection() {
  try {
    // Test 1: Fetch vehicles
    console.log('\n🔍 Testing vehicles table...');
    const { data: vehicles, error: vehiclesError } = await supabase
      .from('vehicles')
      .select('*');

    if (vehiclesError) {
      console.error('❌ Error fetching vehicles:', vehiclesError);
      return false;
    }

    console.log(
      '✅ Vehicles fetched successfully:',
      vehicles.length,
      'vehicles found'
    );
    vehicles.forEach((vehicle) => {
      console.log(
        `  - ${vehicle.name} (${vehicle.year}) - €${vehicle.price}/day`
      );
    });

    // Test 2: Test bookings table
    console.log('\n🔍 Testing bookings table...');
    const { data: bookings, error: bookingsError } = await supabase
      .from('bookings')
      .select('*')
      .limit(5);

    if (bookingsError) {
      console.error('❌ Error fetching bookings:', bookingsError);
      return false;
    }

    console.log(
      '✅ Bookings table accessible:',
      bookings.length,
      'bookings found'
    );

    // Test 3: Test contact inquiries table
    console.log('\n🔍 Testing contact_inquiries table...');
    const { data: inquiries, error: inquiriesError } = await supabase
      .from('contact_inquiries')
      .select('*')
      .limit(5);

    if (inquiriesError) {
      console.error('❌ Error fetching contact inquiries:', inquiriesError);
      return false;
    }

    console.log(
      '✅ Contact inquiries table accessible:',
      inquiries.length,
      'inquiries found'
    );

    console.log(
      '\n🎉 All tests passed! Supabase connection is working correctly.'
    );
    return true;
  } catch (error) {
    console.error('❌ Connection test failed:', error.message);
    return false;
  }
}

testConnection().then((success) => {
  process.exit(success ? 0 : 1);
});
