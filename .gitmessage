# <type>: <description> (Task X)
#
# <detailed description>
#
# Components added/modified:
# - path/to/component.tsx - Brief description of changes
# - path/to/another/file.ts - Brief description of changes
#
# Key features implemented:
# - Feature 1 description
# - Feature 2 description
# - Feature 3 description
#
# Breaking changes: None/List any breaking changes
#
# ─────────────────────────────────────────────────────────────────────
# Commit Types:
# feat:     New feature implementation
# fix:      Bug fixes or improvements
# docs:     Documentation updates
# style:    Code formatting changes
# refactor: Code refactoring without feature changes
# test:     Adding or updating tests
# chore:    Maintenance tasks
#
# Example:
# feat: implement admin authentication and dashboard (Task 9)
#
# Complete admin authentication system with Supabase Auth integration.
# Includes login page, protected routes, and dashboard with booking statistics.
#
# Components added/modified:
# - src/contexts/AuthContext.tsx - Authentication context with session management
# - src/components/auth/ProtectedRoute.tsx - Route protection component
# - src/app/admin/login/page.tsx - Admin login page with validation
# - src/components/admin/AdminLayout.tsx - Admin panel layout
# - src/components/admin/AdminDashboard.tsx - Dashboard with statistics
#
# Key features implemented:
# - Role-based access control for admin users
# - Portuguese error messages and form validation
# - Real-time session management with Supabase Auth
# - Booking statistics and pending bookings display
# - Responsive design for mobile and desktop
#
# Breaking changes: None
# ─────────────────────────────────────────────────────────────────────
