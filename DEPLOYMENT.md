# 🚀 Deployment Guide - Vintage Marketing Portugal

## 📋 Prerequisites

- GitHub repository with admin access
- Vercel account (free tier available)
- Supabase project with configured database
- Domain name (optional, Vercel provides free subdomain)

## 🚀 Quick Start (Automated Setup)

### Option 1: MVP Deployment (Fastest)

```bash
# Set up essential secrets and deploy immediately
./scripts/setup-github-secrets.sh
git push origin main
```

### Option 2: Complete Setup

```bash
# Full setup with all configurations
./scripts/setup-deployment.sh
git push origin main
```

**📋 See [scripts/README.md](scripts/README.md) for detailed script documentation**

## 🔧 Manual Setup (Alternative)

### 1. GitHub Secrets Configuration

Navigate to your GitHub repository → Settings → Secrets and variables → Actions

**Essential MVP Secrets (Required):**

```bash
# Supabase Configuration
NEXT_PUBLIC_SUPABASE_URL=https://your-project.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key
NEXTAUTH_SECRET=your_nextauth_secret_key

# Vercel Deployment
VERCEL_TOKEN=your_vercel_token
VERCEL_ORG_ID=your_vercel_org_id
VERCEL_PROJECT_ID=your_vercel_project_id
```

**Optional Secrets (Full Functionality):**

```bash
# Email Service (Resend)
RESEND_API_KEY=your_resend_api_key

# Admin Features
SUPABASE_SERVICE_ROLE_KEY=your_supabase_service_role_key

# Custom Domain
NEXTAUTH_URL=https://your-domain.vercel.app
```

### 2. Vercel Setup

#### A. Install Vercel CLI

```bash
npm i -g vercel
vercel login
```

#### B. Link Project

```bash
vercel link
# Follow prompts to create/link project
```

#### C. Get Vercel IDs

```bash
# Get Organization ID
vercel teams list

# Get Project ID
vercel projects list
```

#### D. Generate Vercel Token

1. Go to [Vercel Dashboard](https://vercel.com/account/tokens)
2. Create new token with appropriate scope
3. Copy token for GitHub secrets

### 3. Environment Variables in Vercel

In Vercel Dashboard → Project → Settings → Environment Variables:

**Production Environment:**

- `NEXT_PUBLIC_SUPABASE_URL` → Your Supabase project URL
- `NEXT_PUBLIC_SUPABASE_ANON_KEY` → Your Supabase anon key
- `SUPABASE_SERVICE_ROLE_KEY` → Your Supabase service role key
- `RESEND_API_KEY` → Your Resend API key
- `NEXTAUTH_URL` → Your production domain
- `NEXTAUTH_SECRET` → Strong random secret

**Preview Environment:**

- Same variables as production
- `NEXTAUTH_URL` → Can be dynamic Vercel preview URL

### 4. Domain Configuration (Optional)

#### A. Custom Domain Setup

1. Vercel Dashboard → Project → Settings → Domains
2. Add your custom domain
3. Configure DNS records as instructed

#### B. SSL Certificate

- Automatically provisioned by Vercel
- No additional configuration needed

### 5. Supabase Configuration

#### A. Database Setup

Ensure your Supabase database has the required tables:

```sql
-- Vehicles table
CREATE TABLE vehicles (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  name TEXT NOT NULL,
  description TEXT,
  year INTEGER,
  price DECIMAL(10,2),
  photo_url TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Enable Row Level Security
ALTER TABLE vehicles ENABLE ROW LEVEL SECURITY;

-- Create policies for public read access
CREATE POLICY "Allow public read access" ON vehicles
  FOR SELECT USING (true);
```

#### B. Storage Configuration

1. Create storage bucket for vehicle images
2. Set up public access policies
3. Configure CORS for your domain

### 6. Monitoring Setup

#### A. Vercel Analytics

- Automatically enabled for Vercel deployments
- View in Vercel Dashboard → Analytics

#### B. Health Monitoring

- Health check endpoint: `/api/health-check`
- Automated checks every 6 hours via Vercel Crons
- Monitor via Vercel Functions logs

## 🚀 Deployment Process

### Automatic Deployment

1. Push to `main` branch → Production deployment
2. Create Pull Request → Preview deployment
3. All quality checks must pass before deployment

### Manual Deployment

```bash
# Deploy to production
vercel --prod

# Deploy preview
vercel
```

## ✅ Post-Deployment Verification

### 1. Functionality Checklist

- [ ] Homepage loads correctly
- [ ] Vehicle cards display properly
- [ ] "Reservar Agora" button works
- [ ] Reservation form functions
- [ ] Contact form submits
- [ ] Admin panel accessible (if applicable)

### 2. Performance Checks

- [ ] Lighthouse score > 90
- [ ] Core Web Vitals pass
- [ ] Images load optimized
- [ ] Animations perform smoothly

### 3. SEO Verification

- [ ] Meta tags present
- [ ] Sitemap accessible
- [ ] Portuguese content indexed
- [ ] Structured data valid

### 4. Security Checks

- [ ] HTTPS enabled
- [ ] Security headers present
- [ ] No sensitive data exposed
- [ ] API endpoints secured

## 🔍 Troubleshooting

### Common Issues

**Build Failures:**

- Check environment variables are set
- Verify Supabase connection
- Review build logs in GitHub Actions

**Deployment Issues:**

- Verify Vercel token permissions
- Check project ID and org ID
- Review Vercel function logs

**Runtime Errors:**

- Check health check endpoint
- Review Vercel function logs
- Verify database connectivity

### Support Resources

- [Vercel Documentation](https://vercel.com/docs)
- [Next.js Deployment Guide](https://nextjs.org/docs/deployment)
- [Supabase Integration Guide](https://supabase.com/docs/guides/getting-started/tutorials/with-nextjs)

## 📊 Monitoring & Maintenance

### Regular Tasks

- Monitor deployment success rates
- Review performance metrics
- Update dependencies monthly
- Check security audit results
- Backup database regularly

### Alerts Setup

- Configure Vercel alerts for deployment failures
- Set up uptime monitoring
- Monitor error rates and performance

---

**🎉 Congratulations!** Your Vintage Marketing Portugal application is now deployed with a professional CI/CD pipeline.
