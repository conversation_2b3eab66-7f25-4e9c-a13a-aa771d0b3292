{"$schema": "https://openapi.vercel.sh/vercel.json", "regions": ["fra1"], "functions": {"app/api/**/*.js": {"maxDuration": 10}, "app/api/**/*.ts": {"maxDuration": 10}}, "headers": [{"source": "/(.*)", "headers": [{"key": "X-Content-Type-Options", "value": "nosniff"}, {"key": "X-Frame-Options", "value": "DENY"}, {"key": "X-XSS-Protection", "value": "1; mode=block"}]}, {"source": "/api/(.*)", "headers": [{"key": "Access-Control-Allow-Origin", "value": "*"}, {"key": "Access-Control-Allow-Methods", "value": "GET, POST, PUT, DELETE, OPTIONS"}, {"key": "Access-Control-Allow-Headers", "value": "Content-Type, Authorization"}]}], "rewrites": [{"source": "/api/health", "destination": "/api/health"}], "redirects": [{"source": "/home", "destination": "/", "permanent": true}]}