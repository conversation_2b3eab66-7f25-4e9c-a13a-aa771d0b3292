# 🚀 CI/CD Quick Reference - Vintage Marketing Portugal

## 📋 Quick Setup Checklist

### ✅ Initial Setup (One-time)

- [ ] Run setup script: `./scripts/setup-deployment.sh`
- [ ] Configure GitHub secrets (see below)
- [ ] Set up Vercel project and get IDs
- [ ] Update environment variables in Vercel dashboard
- [ ] Test first deployment

### 🔑 Required GitHub Secrets

```bash
# Supabase
NEXT_PUBLIC_SUPABASE_URL=https://your-project.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_anon_key
SUPABASE_SERVICE_ROLE_KEY=your_service_role_key

# Email
RESEND_API_KEY=your_resend_key

# Auth
NEXTAUTH_URL=https://your-domain.vercel.app
NEXTAUTH_SECRET=your_secret_key

# Vercel
VERCEL_TOKEN=your_vercel_token
VERCEL_ORG_ID=your_org_id
VERCEL_PROJECT_ID=your_project_id
```

## 🔄 Workflow Triggers

| Action            | Trigger               | Result                    |
| ----------------- | --------------------- | ------------------------- |
| Push to `main`    | Production deployment | Live site updated         |
| Push to `develop` | Quality checks only   | No deployment             |
| Create PR         | Preview deployment    | Preview URL in PR comment |
| Manual trigger    | Can deploy any branch | Manual control            |

## 🧪 Quality Checks

### Automated Checks (Every Push/PR)

- ✅ TypeScript compilation
- ✅ ESLint code quality
- ✅ Prettier formatting
- ✅ Jest unit tests
- ✅ Next.js build verification
- ✅ Security audit (npm audit)

### Performance Monitoring

- ✅ Build time tracking
- ✅ Bundle size analysis
- ✅ Health check endpoint
- ✅ Deployment verification

## 🚀 Deployment Process

### Production Deployment

1. Push to `main` branch
2. All quality checks run
3. Build verification
4. Security audit
5. Deploy to Vercel production
6. Health check verification
7. Deployment notification

### Preview Deployment

1. Create/update Pull Request
2. Quality checks run
3. Deploy to Vercel preview
4. Preview URL posted in PR comment

## 🔍 Monitoring & Health Checks

### Health Check Endpoint

- **URL**: `/api/health-check`
- **Method**: GET or HEAD
- **Checks**: Database, environment, memory
- **Frequency**: Every 6 hours (automated)

### Example Health Response

```json
{
  "status": "healthy",
  "timestamp": "2024-01-20T10:30:00Z",
  "checks": {
    "database": "connected",
    "environment": "configured",
    "responseTime": "45ms"
  }
}
```

## 🛠️ Local Development Commands

```bash
# Development
npm run dev              # Start dev server
npm run build           # Build for production
npm run start           # Start production server

# Quality Checks
npm run type-check      # TypeScript validation
npm run lint            # ESLint check
npm run lint:fix        # Fix ESLint issues
npm run format          # Format code
npm run format:check    # Check formatting

# Testing
npm test                # Run tests
npm run test:watch      # Watch mode
npm run test:coverage   # With coverage

# Deployment
npm run deploy          # Deploy to production
npm run deploy:preview  # Deploy preview

# All checks (pre-commit)
npm run pre-commit      # Run all quality checks
```

## 🔧 Troubleshooting

### Common Issues & Solutions

**❌ Build Fails**

```bash
# Check environment variables
npm run type-check
# Review build logs in GitHub Actions
```

**❌ Tests Fail**

```bash
# Run tests locally
npm test
# Check test coverage
npm run test:coverage
```

**❌ Deployment Fails**

```bash
# Check Vercel token permissions
# Verify project/org IDs
# Review Vercel function logs
```

**❌ Health Check Fails**

```bash
# Check Supabase connection
# Verify environment variables
# Review API logs in Vercel
```

### Debug Commands

```bash
# Check deployment status
vercel ls

# View function logs
vercel logs

# Test health endpoint locally
curl http://localhost:3000/api/health-check

# Test production health
curl https://your-domain.vercel.app/api/health-check
```

## 📊 Performance Targets

### Build Performance

- ⏱️ Build time: < 2 minutes
- 📦 Bundle size: < 1MB (gzipped)
- 🧪 Test coverage: > 80%

### Runtime Performance

- 🚀 First Contentful Paint: < 1.5s
- ⚡ Largest Contentful Paint: < 2.5s
- 📱 Mobile Lighthouse: > 90
- 🖥️ Desktop Lighthouse: > 95

## 🔒 Security Features

### Automated Security

- 🛡️ npm audit on every build
- 🔒 Security headers via Vercel
- 🚫 Dependency vulnerability scanning
- 🔐 Environment variable encryption

### Security Headers

- `X-Content-Type-Options: nosniff`
- `X-Frame-Options: DENY`
- `X-XSS-Protection: 1; mode=block`
- `Referrer-Policy: strict-origin-when-cross-origin`

## 📞 Support & Resources

### Documentation

- 📖 [Full Deployment Guide](./DEPLOYMENT.md)
- 🔧 [Vercel Documentation](https://vercel.com/docs)
- 🧪 [Next.js Testing](https://nextjs.org/docs/testing)

### Monitoring

- 📊 Vercel Analytics Dashboard
- 🔍 GitHub Actions Logs
- 🏥 Health Check Endpoint
- 📈 Performance Metrics

---

**💡 Pro Tip**: Bookmark this page for quick reference during development and deployment!
