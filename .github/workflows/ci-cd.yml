name: CI/CD Pipeline - Vintage Marketing Portugal

on:
  push:
    branches: [main, develop]
  pull_request:
    branches: [main]

env:
  NODE_VERSION: '18'
  NEXT_TELEMETRY_DISABLED: 1

jobs:
  # Quality Assurance Job
  quality-assurance:
    name: 🔍 Quality Assurance
    runs-on: ubuntu-latest

    steps:
      - name: 📥 Checkout code
        uses: actions/checkout@v4

      - name: 📦 Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'

      - name: 🔧 Install dependencies
        run: npm ci

      - name: 🔍 TypeScript type checking
        run: npm run type-check

      - name: 🧹 ESLint code quality
        run: npm run lint

      - name: 💅 Prettier code formatting
        run: npm run format -- --check

      - name: 🧪 Run Jest tests
        run: npm test -- --coverage --watchAll=false
        env:
          NEXT_PUBLIC_SUPABASE_URL: ${{ secrets.NEXT_PUBLIC_SUPABASE_URL }}
          NEXT_PUBLIC_SUPABASE_ANON_KEY: ${{ secrets.NEXT_PUBLIC_SUPABASE_ANON_KEY }}

      - name: 📊 Upload test coverage
        uses: codecov/codecov-action@v3
        if: success()
        with:
          file: ./coverage/lcov.info
          flags: unittests
          name: vintage-marketing-portugal

  # Build Verification Job
  build-verification:
    name: 🏗️ Build Verification
    runs-on: ubuntu-latest
    needs: quality-assurance

    steps:
      - name: 📥 Checkout code
        uses: actions/checkout@v4

      - name: 📦 Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'

      - name: 🔧 Install dependencies
        run: npm ci

      - name: 🏗️ Build Next.js application
        run: npm run build
        env:
          NEXT_PUBLIC_SUPABASE_URL: ${{ secrets.NEXT_PUBLIC_SUPABASE_URL }}
          NEXT_PUBLIC_SUPABASE_ANON_KEY: ${{ secrets.NEXT_PUBLIC_SUPABASE_ANON_KEY }}
          SUPABASE_SERVICE_ROLE_KEY: ${{ secrets.SUPABASE_SERVICE_ROLE_KEY }}
          RESEND_API_KEY: ${{ secrets.RESEND_API_KEY }}
          NEXTAUTH_URL: ${{ secrets.NEXTAUTH_URL }}
          NEXTAUTH_SECRET: ${{ secrets.NEXTAUTH_SECRET }}

      - name: 🔍 Verify build output
        run: |
          if [ ! -d ".next" ]; then
            echo "❌ Build failed - .next directory not found"
            exit 1
          fi
          echo "✅ Build successful - .next directory exists"

      - name: 📦 Cache build artifacts
        uses: actions/cache@v3
        with:
          path: |
            .next/cache
            node_modules/.cache
          key: ${{ runner.os }}-nextjs-${{ hashFiles('**/package-lock.json') }}-${{ hashFiles('**/*.js', '**/*.jsx', '**/*.ts', '**/*.tsx') }}
          restore-keys: |
            ${{ runner.os }}-nextjs-${{ hashFiles('**/package-lock.json') }}-

  # Security Audit Job
  security-audit:
    name: 🔒 Security Audit
    runs-on: ubuntu-latest
    needs: quality-assurance

    steps:
      - name: 📥 Checkout code
        uses: actions/checkout@v4

      - name: 📦 Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'

      - name: 🔒 Run npm audit
        run: npm audit --audit-level=high

      - name: 🛡️ Check for known vulnerabilities
        run: |
          npm audit --json > audit-results.json
          if [ $(cat audit-results.json | jq '.metadata.vulnerabilities.high + .metadata.vulnerabilities.critical') -gt 0 ]; then
            echo "❌ High or critical vulnerabilities found"
            npm audit
            exit 1
          fi
          echo "✅ No high or critical vulnerabilities found"

  # Deployment Job (Production)
  deploy-production:
    name: 🚀 Deploy to Production
    runs-on: ubuntu-latest
    needs: [quality-assurance, build-verification, security-audit]
    if: github.ref == 'refs/heads/main' && github.event_name == 'push'
    environment: production

    steps:
      - name: 📥 Checkout code
        uses: actions/checkout@v4

      - name: 🚀 Deploy to Vercel
        uses: amondnet/vercel-action@v25
        with:
          vercel-token: ${{ secrets.VERCEL_TOKEN }}
          vercel-org-id: ${{ secrets.VERCEL_ORG_ID }}
          vercel-project-id: ${{ secrets.VERCEL_PROJECT_ID }}
          vercel-args: '--prod'

      - name: 🔍 Post-deployment health check
        run: |
          sleep 30  # Wait for deployment to be ready
          DEPLOYMENT_URL="${{ steps.deploy.outputs.preview-url }}"
          if [ -z "$DEPLOYMENT_URL" ]; then
            DEPLOYMENT_URL="https://vintage-marketing-portugal-kamikaziiis-projects.vercel.app"
          fi

          echo "🔍 Checking deployment health at: $DEPLOYMENT_URL"

          # Check if site is accessible
          HTTP_STATUS=$(curl -s -o /dev/null -w "%{http_code}" "$DEPLOYMENT_URL")
          if [ "$HTTP_STATUS" != "200" ]; then
            echo "❌ Deployment health check failed - HTTP $HTTP_STATUS"
            exit 1
          fi

          echo "✅ Deployment health check passed - HTTP $HTTP_STATUS"

      - name: 📝 Deployment notification
        if: success()
        run: |
          echo "🎉 Deployment successful!"
          echo "🌐 Production URL: https://vintage-marketing-portugal-kamikaziiis-projects.vercel.app"
          echo "📊 View deployment details in Vercel dashboard"

  # Preview Deployment Job (Pull Requests)
  deploy-preview:
    name: 🔍 Deploy Preview
    runs-on: ubuntu-latest
    needs: [quality-assurance, build-verification]
    if: github.event_name == 'pull_request'

    steps:
      - name: 📥 Checkout code
        uses: actions/checkout@v4

      - name: 🔍 Deploy Preview to Vercel
        uses: amondnet/vercel-action@v25
        id: vercel-preview
        with:
          vercel-token: ${{ secrets.VERCEL_TOKEN }}
          vercel-org-id: ${{ secrets.VERCEL_ORG_ID }}
          vercel-project-id: ${{ secrets.VERCEL_PROJECT_ID }}

      - name: 💬 Comment PR with preview URL
        uses: actions/github-script@v6
        with:
          script: |
            github.rest.issues.createComment({
              issue_number: context.issue.number,
              owner: context.repo.owner,
              repo: context.repo.repo,
              body: `🔍 **Preview Deployment Ready!**
              
              🌐 **Preview URL:** ${{ steps.vercel-preview.outputs.preview-url }}
              
              ✅ All quality checks passed
              🧪 Tests: Passed
              🔍 TypeScript: Passed  
              🧹 Linting: Passed
              🏗️ Build: Successful
              
              This preview will be automatically updated with new commits.`
            })
