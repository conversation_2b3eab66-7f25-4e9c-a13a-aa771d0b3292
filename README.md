# Vintage Marketing Portugal

A professional website for vintage vehicle rentals targeting Portuguese companies for marketing events and campaigns.

## Tech Stack

- **Frontend**: Next.js 15 with App Router, TypeScript, and Tailwind CSS
- **Backend**: Supabase (PostgreSQL, Auth, Storage, Real-time APIs)
- **UI Components**: Headless UI for accessible modal and form components
- **Typography**: Open Sans (headings), Inter (body text), Limelight (logo)
- **State Management**: Custom React hooks with TypeScript
- **Animation System**: Comprehensive Framer Motion ecosystem with 25+ components, performance optimization, and accessibility compliance
- **Image Optimization**: Next.js Image component with Supabase Storage
- **Email**: Resend for email delivery (configured)
- **Hosting**: Vercel with automatic deployments and CDN

## Getting Started

1. Install dependencies:

```bash
npm install
```

2. Copy environment variables:

```bash
cp .env.local.example .env.local
```

3. Configure your Supabase and Resend API keys in `.env.local`

4. Run the development server:

```bash
npm run dev
```

5. Open [http://localhost:3000](http://localhost:3000) in your browser.

## Available Scripts

- `npm run dev` - Start development server
- `npm run build` - Build for production
- `npm run start` - Start production server
- `npm run lint` - Run ESLint
- `npm run type-check` - TypeScript validation
- `npm run format` - Prettier formatting

## Project Structure

```
components/
├── animations/    # Root-level animation components (Framer Motion)
│   ├── index.ts          # Animation exports barrel file
│   ├── FadeIn.tsx        # Fade-in animation component
│   ├── SlideIn.tsx       # Directional slide animations
│   ├── ScaleIn.tsx       # Scale-up animations
│   ├── StaggerContainer.tsx # Staggered child animations
│   └── HoverCard.tsx     # Interactive hover animations

src/
├── app/           # Next.js App Router pages
│   ├── globals.css        # Global styles and design system
│   ├── layout.tsx         # Root layout with providers
│   ├── page.tsx           # Homepage component
│   ├── servicos/          # Services page
│   ├── frota/             # Fleet showcase page
│   ├── reservas/          # Booking system page
│   ├── contacto/          # Contact page
│   └── termos/            # Terms and conditions page
├── components/    # Feature-specific React components
│   ├── layout/           # Header, Footer components
│   ├── forms/            # Booking form components
│   ├── admin/            # Admin panel components
│   ├── auth/             # Authentication components
│   └── ui/               # Reusable UI components
├── hooks/         # Custom React hooks
│   ├── useBookingForm.ts # Booking form state management
│   └── useParallax.ts    # Parallax scroll effects
├── lib/          # Utility functions and configurations
│   └── supabase.ts       # Supabase client configuration
├── types/        # TypeScript type definitions (auto-generated from Supabase)
└── utils/        # Helper functions

Docs/
├── Architecture/  # Technical documentation
│   ├── database-schema.md         # Complete database schema with RLS policies
│   ├── api-reference.md           # Supabase API usage examples
│   ├── component-documentation.md # Component specifications and usage
│   └── technical-architecture.md  # System architecture overview
├── Development/   # Development workflows and standards
│   └── development-guide.md       # Complete development guide
├── Infrastructure/ # Deployment and infrastructure
│   └── deployment-guide.md        # Production deployment guide
└── Project/       # Project documentation
    ├── implementation-status.md   # Detailed implementation status
    └── project-summary.md         # Executive project summary
```

## Features

- Fleet showcase with vintage vehicles
- Calendar-based booking system
- Admin panel for managing bookings and vehicles
- Portuguese language content with local SEO
- Mobile-first responsive design
- Email notifications for booking confirmations

## Implementation Status

### ✅ Database & Backend (Completed)

- **Supabase Database**: All tables deployed with proper schema
  - `vehicles` - Fleet management with availability tracking
  - `bookings` - Customer booking management with status tracking
  - `contact_inquiries` - Contact form submissions with company details
  - `email_templates` - Admin-customizable email templates
- **Row Level Security**: Comprehensive RLS policies implemented
  - Public users: Can view vehicles, create bookings/inquiries
  - Admin users: Full management access to all data
- **Database Functions**: JWT-based admin role checking with `is_admin()` function
- **TypeScript Integration**: Auto-generated types from Supabase schema with convenience exports

### ✅ Frontend Core (Completed)

- **✅ Layout System**: Complete layout architecture implemented
  - **Header Component**: Responsive navigation with mobile hamburger menu, design system integration
  - **Footer Component**: Business information, contact details, quick links, social media integration
  - **Layout Wrapper**: Consistent page structure with AuthProvider integration
- **✅ Design System**: Comprehensive design system implemented
  - **Color Palette**: Primary (black/grays), background (cream/beige), accent colors
  - **Typography**: Open Sans headings, Inter body text, Limelight logo font
  - **Component Styles**: Buttons (primary/secondary/CTA), cards, navigation, sections
  - **Responsive Design**: Mobile-first approach with consistent breakpoints
  - **Animation System**: Framer Motion-based animations with hover effects, transitions, and micro-interactions

### ✅ Animation System (Completed) - **MASTERCLASS IMPLEMENTATION**

The website features a **production-ready, best-in-class animation system** that exceeds industry standards:

#### **Core Animation Components (25+ Components)**

- **Basic Animations**: FadeIn, SlideIn, ScaleIn, StaggerContainer with performance optimization
- **Scroll-Based**: ScrollReveal, TextReveal, CardStack, StaggeredCard, IndependentSection, HeroParallax
- **Page Transitions**: Event-driven transitions with Next.js App Router integration
- **Micro-Interactions**: MagneticButton, RippleButton, GlowEffect, CounterAnimation, AnimatedButton

#### **Advanced Features**

- **Card-Stacking Effects**: Mathematical precision for realistic card folding during scroll
- **Performance Excellence**: Device capability detection, network awareness, hardware acceleration
- **Accessibility Leadership**: Comprehensive `prefers-reduced-motion` support with graceful fallbacks
- **Responsive Scaling**: Screen-size aware animation complexity (mobile/tablet/desktop)
- **Event-Driven Architecture**: Uses Framer Motion callbacks instead of manual timing

#### **Technical Excellence**

- **Variants with StaggerChildren**: Proper Framer Motion orchestration patterns
- **Hardware Acceleration**: GPU-optimized properties (transform, opacity) exclusively
- **Performance Monitoring**: Built-in FPS testing and optimization utilities
- **Context Integration**: Global animation state management via PageTransitionContext
- **TypeScript Excellence**: Complete type safety with sophisticated interfaces

### ✅ Core Pages (Completed)

- **✅ Homepage** (`/`): Complete landing page with hero section, service overview, featured vehicles
- **✅ Services Page** (`/servicos`): Comprehensive service descriptions, process workflow, CTA sections
- **✅ Fleet Page** (`/frota`): Vehicle showcase with real-time data, modal integration, responsive grid
- **✅ Terms Page** (`/termos`): Complete terms and conditions with structured content
- **✅ Contact Page** (`/contacto`): Full-featured contact form, business info, Google Maps integration
- **✅ Booking Page** (`/reservas`): Multi-step booking system with complete workflow

### ✅ Booking System (Completed)

- **Multi-step Form**: 4-step wizard (vehicle selection, dates, customer details, confirmation)
- **State Management**: Custom `useBookingForm` hook with centralized state
- **Form Components**: Modular components for each step with validation
- **Date Validation**: Conflict detection with existing bookings
- **Progress Indicator**: Visual step progress with completion tracking
- **Success Handling**: Completion state with new booking option
- **Portuguese Localization**: All text and error messages in Portuguese

### ✅ Vehicle Management (Completed)

- **Vehicle Display**: Grid layouts with responsive design
- **Vehicle Cards**: Reusable components with image optimization
- **Vehicle Modals**: Detailed view with booking integration
- **Real-time Data**: Live fetching from Supabase with error handling
- **Image Optimization**: Next.js Image component with multiple source support

### 🚧 Admin Panel (Planned)

- Authentication integration with Supabase Auth
- Booking management dashboard
- Vehicle management interface
- Email template system
- SEO management tools
